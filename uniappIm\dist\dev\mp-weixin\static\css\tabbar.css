/* 自定义tabBar样式 */
.uni-tabbar {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%) !important;
  backdrop-filter: blur(20rpx) !important;
  border-top: 1rpx solid rgba(102, 126, 234, 0.15) !important;
  box-shadow: 0 -6rpx 30rpx rgba(102, 126, 234, 0.12) !important;
  height: 65px !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.uni-tabbar__item {
  transition: all 0.3s ease !important;
}

.uni-tabbar__item:active {
  transform: scale(0.95) !important;
}

.uni-tabbar__icon {
  transition: all 0.3s ease !important;
}

.uni-tabbar__label {
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

/* 选中状态的特殊效果 */
.uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__icon {
  transform: scale(1.15) !important;
  filter: drop-shadow(0 2rpx 8rpx rgba(102, 126, 234, 0.3)) !important;
}

.uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__label {
  font-weight: 600 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  text-shadow: none !important;
}

/* 添加渐变背景到选中的图标 */
.uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__icon::before {
  content: '';
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  border-radius: 50%;
  z-index: -1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* 添加点击波纹效果 */
.uni-tabbar__item:active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.2);
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    width: 80rpx;
    height: 80rpx;
    opacity: 0;
  }
}
