import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import axios from 'axios'
axios.defaults.baseURL = 'http://localhost:8083' // 根据实际后端地址修改
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) config.headers.Authorization = 'Bearer ' + token
  return config
})
const app = createApp(App)
import zhCn from 'element-plus/es/locale/lang/zh-cn'

app.use(router)

app.mount('#app')
app.use(ElementPlus, {
    locale: zhCn,
})