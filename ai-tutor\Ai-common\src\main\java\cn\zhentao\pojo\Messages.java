package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息表
 * @TableName messages
 */
@TableName(value ="messages")
@Data
public class Messages implements Serializable {
    /**
     * 消息记录ID
     */
    @TableId
    private Long id;

    /**
     * 发送用户ID
     */
    private Long fromUserId;

    /**
     * 接收用户ID
     */
    private Long toUserId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型（需补充实际枚举值）
     */
    private Object type;

    /**
     * 消息状态（需补充实际枚举值，如已读、未读等）
     */
    private Object status;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 阅读时间
     */
    private Date readTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}