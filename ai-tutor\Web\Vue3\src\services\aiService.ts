// AI服务API接口
export interface AiServiceResponse {
  code: number;
  message: string;
  data?: any;
}

export interface AiRequest {
  userId: number | string;
  input?: string;
  question?: string;
  query?: string;
  prompt?: string;
  message?: string;
  agentRole?: string;
}

export class AiService {
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  // 通用请求方法
  private async request(endpoint: string, data: any): Promise<AiServiceResponse> {
    try {
      console.log(`发送API请求: ${endpoint}`, data);

      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 180000); // 3分钟超时

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log(`API响应状态: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API请求失败: ${response.status} - ${errorText}`);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const result = await response.json();
      console.log(`API响应结果:`, result);
      return result;
    } catch (error) {
      console.error('API请求异常:', error);

      // 处理超时错误
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请稍后重试');
      }

      throw error;
    }
  }

  // 知识问答 - AI百科全书
  async knowledgeQA(userId: number | string, question: string): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/knowledge/qa', {
      userId,
      question
    });
  }

  // 信息查询 - 天气电话资讯
  async informationQuery(userId: number | string, query: string): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/info/query', {
      userId,
      query
    });
  }

  // 文本生成 - 创意写作助手
  async textGeneration(userId: number | string, prompt: string, type: string = 'general'): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/text/generate', {
      userId,
      prompt,
      type
    });
  }

  // 智能翻译
  async translation(userId: number | string, text: string, fromLang: string = '中文', toLang: string = '英文'): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/translate', {
      userId,
      text,
      fromLang,
      toLang
    });
  }

  // 情感陪伴
  async emotionalCompanion(userId: number | string, message: string): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/emotion/companion', {
      userId,
      message
    });
  }

  // 智能推荐
  async recommendation(userId: number | string, preferences: string, category: string = '通用'): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/recommend', {
      userId,
      preferences,
      category
    });
  }

  // 游戏娱乐
  async gameEntertainment(userId: number | string, input: string): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/game/entertainment', {
      userId,
      gameType: 'general',
      userInput: input,
      gameState: 'start'
    });
  }

  // 健康管理
  async healthManagement(userId: number | string, healthData: string, analysisType: string = 'comprehensive'): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/health/management', {
      userId,
      healthData,
      analysisType
    });
  }

  // 图片生成 - AI文生图
  async imageGeneration(userId: number | string, prompt: string, style: string = 'realistic', size: string = '1024x1024'): Promise<AiServiceResponse> {
    return this.request('/api/ai/miniprogram/image/generation', {
      userId,
      prompt,
      style,
      size
    });
  }

  // ==================== 流式输出API ====================

  // 知识问答 - 流式输出 (参考React版本使用EventSource)
  async knowledgeQAStream(userId: number | string, question: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      // 先发送POST请求启动流式输出
      fetch(`${this.baseUrl}/api/ai/miniprogram/knowledge/qa/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          question
        })
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 使用ReadableStream处理SSE响应
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is null');
        }

        const decoder = new TextDecoder();
        let buffer = '';
        let currentEvent = '';

        const processStream = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              buffer += decoder.decode(value, { stream: true });
              const lines = buffer.split('\n');
              buffer = lines.pop() || '';

              for (const line of lines) {
                if (line.trim() === '') continue;

                // 处理标准SSE格式（事件名和数据分两行）
                if (line.startsWith('event:')) {
                  currentEvent = line.slice(6).trim(); // 去掉 'event:'
                } else if (line.startsWith('data:')) {
                  const data = line.slice(5).trim(); // 去掉 'data:'

                  if (currentEvent === 'data') {
                    try {
                      const parsed = JSON.parse(data);

                      if (parsed.type === 'data' && parsed.content) {
                        onMessage(parsed.content);
                      }
                    } catch (e) {
                      console.warn('解析SSE数据失败:', e);
                    }
                  } else if (currentEvent === 'end') {
                    onComplete();
                    resolve();
                    return;
                  } else if (currentEvent === 'error') {
                    try {
                      const parsed = JSON.parse(data);
                      const error = new Error(parsed.message || '流式输出错误');
                      onError(error);
                      reject(error);
                    } catch (e) {
                      const error = new Error('流式输出错误');
                      onError(error);
                      reject(error);
                    }
                    return;
                  }
                }
                // 兼容其他格式
                else if (line.includes('event:data data:')) {
                  const dataStart = line.indexOf('data:') + 5;
                  const data = line.slice(dataStart).trim();
                  try {
                    const parsed = JSON.parse(data);

                    if (parsed.type === 'data' && parsed.content) {
                      onMessage(parsed.content);
                    }
                  } catch (e) {
                    console.warn('解析兼容格式SSE数据失败:', e);
                  }
                }
              }
            }

            // 流结束
            onComplete();
            resolve();
          } catch (error) {
            console.error('流式处理错误:', error);
            onError(error);
            reject(error);
          }
        };

        processStream();

      }).catch(error => {
        console.error('启动流式输出失败:', error);
        onError(error);
        reject(error);
      });
    });
  }

  // 信息查询 - 流式输出
  async informationQueryStream(userId: number | string, query: string): Promise<ReadableStream> {
    return this.createStreamConnection('/api/ai/miniprogram/info/query/stream', {
      userId,
      query
    });
  }



  // 游戏娱乐 - 流式输出
  async gameEntertainmentStream(userId: number | string, gameType: string, userInput: string, gameState: string = 'start'): Promise<ReadableStream> {
    return this.createStreamConnection('/api/ai/miniprogram/game/entertainment/stream', {
      userId,
      gameType,
      userInput,
      gameState
    });
  }

  // 健康管理 - 流式输出
  async healthManagementStream(userId: number | string, healthData: string, analysisType: string = 'comprehensive'): Promise<ReadableStream> {
    return this.createStreamConnection('/api/ai/miniprogram/health/management/stream', {
      userId,
      healthData,
      analysisType
    });
  }

  // 测试连接
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/ai/miniprogram/test`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });
      return response.ok;
    } catch (error) {
      console.error('连接测试失败:', error);
      return false;
    }
  }

  // ==================== 语音通话相关API ====================

  // 获取RTC认证Token
  async getRtcAuthToken(userId: string, channelId?: string): Promise<AiServiceResponse> {
    return this.request('/api/v2/aiagent/getRtcAuthToken', {
      user_id: userId,
      channel_id: channelId
    });
  }

  // 生成AI智能体
  async generateAIAgentCall(userId: string, aiAgentId?: string, region?: string): Promise<AiServiceResponse> {
    return this.request('/api/v2/aiagent/generateAIAgentCall', {
      user_id: userId,
      ai_agent_id: aiAgentId,
      region: region || 'cn-beijing'
    });
  }

  // 描述AI智能体实例
  async describeAIAgentInstance(instanceId: string, userId: string): Promise<AiServiceResponse> {
    return this.request('/api/v2/aiagent/describeAIAgentInstance', {
      ai_agent_instance_id: instanceId,
      user_id: userId
    });
  }

  // 生成消息聊天Token
  async generateMessageChatToken(userId: string, aiAgentId?: string, region?: string): Promise<AiServiceResponse> {
    return this.request('/api/v2/aiagent/generateMessageChatToken', {
      user_id: userId,
      ai_agent_id: aiAgentId,
      region: region || 'cn-beijing'
    });
  }

  // ==================== 新增流式输出API ====================

  // 信息查询 - 流式输出
  async informationQueryStream(userId: number | string, query: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return this.streamRequest('/api/ai/miniprogram/info/query/stream', {
      userId,
      query
    }, onMessage, onComplete, onError);
  }

  // 游戏娱乐 - 流式输出
  async gameEntertainmentStream(userId: number | string, gameType: string, userInput: string, gameState: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return this.streamRequest('/api/ai/miniprogram/game/entertainment/stream', {
      userId,
      gameType,
      userInput,
      gameState
    }, onMessage, onComplete, onError);
  }

  // 健康管理 - 流式输出
  async healthManagementStream(userId: number | string, healthData: string, analysisType: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return this.streamRequest('/api/ai/miniprogram/health/management/stream', {
      userId,
      healthData,
      analysisType
    }, onMessage, onComplete, onError);
  }

  // 文本生成 - 流式输出
  async textGenerationStream(userId: number | string, prompt: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return this.streamRequest('/api/ai/miniprogram/text/generate/stream', {
      userId,
      prompt,
      type: 'creative'
    }, onMessage, onComplete, onError);
  }

  // 情感陪伴 - 流式输出
  async emotionalCompanionStream(userId: number | string, message: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return this.streamRequest('/api/ai/miniprogram/emotion/companion/stream', {
      userId,
      message
    }, onMessage, onComplete, onError);
  }

  // 智能推荐 - 流式输出
  async recommendationStream(userId: number | string, preferences: string, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return this.streamRequest('/api/ai/miniprogram/recommend/stream', {
      userId,
      preferences,
      category: '通用'
    }, onMessage, onComplete, onError);
  }

  // 通用流式输出方法
  private async streamRequest(endpoint: string, data: any, onMessage: (content: string) => void, onComplete: () => void, onError: (error: any) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        const timeoutError = new Error('流式输出超时，请稍后重试');
        onError(timeoutError);
        reject(timeoutError);
      }, 180000); // 3分钟超时

      // 发送POST请求启动流式输出
      fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        signal: controller.signal
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 使用ReadableStream处理SSE响应
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Response body is null');
        }

        const decoder = new TextDecoder();
        let buffer = '';
        let currentEvent = '';

        const processStream = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              buffer += decoder.decode(value, { stream: true });
              const lines = buffer.split('\n');
              buffer = lines.pop() || '';

              for (const line of lines) {
                if (line.trim() === '') continue;

                // 处理标准SSE格式（事件名和数据分两行）
                if (line.startsWith('event:')) {
                  currentEvent = line.slice(6).trim(); // 去掉 'event:'
                } else if (line.startsWith('data:')) {
                  const data = line.slice(5).trim(); // 去掉 'data:'

                  if (currentEvent === 'data') {
                    try {
                      const parsed = JSON.parse(data);

                      if (parsed.type === 'data' && parsed.content) {
                        onMessage(parsed.content);
                      }
                    } catch (e) {
                      console.warn('解析SSE数据失败:', e);
                    }
                  } else if (currentEvent === 'end') {
                    clearTimeout(timeoutId);
                    onComplete();
                    resolve();
                    return;
                  } else if (currentEvent === 'error') {
                    clearTimeout(timeoutId);
                    try {
                      const parsed = JSON.parse(data);
                      const error = new Error(parsed.message || '流式输出错误');
                      onError(error);
                      reject(error);
                    } catch (e) {
                      const error = new Error('流式输出错误');
                      onError(error);
                      reject(error);
                    }
                    return;
                  }
                }
              }
            }

            // 流结束
            clearTimeout(timeoutId);
            onComplete();
            resolve();
          } catch (error) {
            clearTimeout(timeoutId);
            console.error('流式处理错误:', error);
            onError(error);
            reject(error);
          }
        };

        processStream();

      }).catch(error => {
        clearTimeout(timeoutId);
        console.error('启动流式输出失败:', error);

        // 处理超时错误
        if (error.name === 'AbortError') {
          const timeoutError = new Error('流式输出超时，请稍后重试');
          onError(timeoutError);
          reject(timeoutError);
        } else {
          onError(error);
          reject(error);
        }
      });
    });
  }
}

// 创建默认实例
const aiService = new AiService();

export default aiService;
