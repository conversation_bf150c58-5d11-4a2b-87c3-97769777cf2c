<view class="test-container data-v-1950f93b"><view class="test-header data-v-1950f93b"><text class="title data-v-1950f93b">前后端交互测试</text></view><view class="test-section data-v-1950f93b"><text class="section-title data-v-1950f93b">连接状态</text><view class="status-item data-v-1950f93b"><text class="status-label data-v-1950f93b">后端API:</text><text class="{{['status-value', 'data-v-1950f93b', b && 'success', c && 'error']}}">{{a}}</text></view><view class="status-item data-v-1950f93b"><text class="status-label data-v-1950f93b">WebSocket:</text><text class="{{['status-value', 'data-v-1950f93b', e && 'success', f && 'error']}}">{{d}}</text></view></view><view class="test-section data-v-1950f93b"><text class="section-title data-v-1950f93b">基础API测试</text><button bindtap="{{g}}" class="test-btn data-v-1950f93b">测试获取用户列表</button><button bindtap="{{h}}" class="test-btn data-v-1950f93b">测试用户注册</button><button bindtap="{{i}}" class="test-btn data-v-1950f93b">测试用户登录</button><button bindtap="{{j}}" class="test-btn data-v-1950f93b">测试WebSocket</button></view><view class="test-section data-v-1950f93b"><text class="section-title data-v-1950f93b">好友管理测试</text><button bindtap="{{k}}" class="test-btn data-v-1950f93b">测试好友管理API</button><button bindtap="{{l}}" class="test-btn data-v-1950f93b">测试搜索用户</button><button bindtap="{{m}}" class="test-btn data-v-1950f93b">测试好友申请</button></view><view class="test-section data-v-1950f93b"><text class="section-title data-v-1950f93b">测试结果</text><scroll-view class="log-container data-v-1950f93b" scroll-y><view wx:for="{{n}}" wx:for-item="log" wx:key="d" class="log-item data-v-1950f93b"><text class="log-time data-v-1950f93b">{{log.a}}</text><text class="{{['log-message', 'data-v-1950f93b', log.c]}}">{{log.b}}</text></view></scroll-view></view></view>