<template>
  <el-container style="height:100vh;" class="main-container">
    <el-aside width="220px" class="sidebar">
      <div class="logo-section">
        <el-icon size="32" class="logo-icon"><Reading /></el-icon>
        <h3>智慧家教</h3>
      </div>
      <el-menu 
        :key="menuUpdateKey"
        :default-active="activeMenu" 
        router 
        class="sidebar-menu"
        background-color="#2c3e50"
        text-color="#ecf0f1"
        active-text-color="#3498db"
      >
        <el-menu-item index="/main/shou">
          <el-icon><HomeFilled /></el-icon>
          <span>系统首页</span>
        </el-menu-item>
        
        <!-- 系统管理下拉菜单 - 根据权限显示 -->
        <el-sub-menu v-if="hasSystemManagementPermission()" index="system-management">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item v-if="hasPermission('用户管理')" index="/main/user">
            <el-icon><UserFilled /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item v-if="hasPermission('角色管理')" index="/main/role">
            <el-icon><Lock /></el-icon>
            <span>角色管理</span>
          </el-menu-item>
          <el-menu-item v-if="hasPermission('菜单管理')" index="/main/menu">
            <el-icon><Menu /></el-icon>
            <span>菜单管理</span>
          </el-menu-item>
          <el-menu-item v-if="hasPermission('权限管理')" index="/main/permission">
            <el-icon><Key /></el-icon>
            <span>权限管理</span>
          </el-menu-item>
        </el-sub-menu>
        
        <!-- 其他菜单项 -->
        <el-menu-item v-for="item in dynamicMenus" :key="item.menuId" :index="item.path">
          <el-icon><component :is="getMenuIcon(item.menuName)"/></el-icon>
          <span>{{ item.menuName }}</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/main' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ getCurrentPageName() }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
                  <div class="header-right">
            <!-- 权限状态显示 -->
            <div class="permission-status">
              <!-- 用户类型显示 -->
              <el-tag v-if="user.username === 'admin'" type="danger" size="small">
                管理员
              </el-tag>
              <el-tag v-else type="info" size="small">
                普通用户: {{ user.username || '未知' }}
              </el-tag>
              
              <!-- 权限状态显示 -->
              <el-tag v-if="userPermissions.length > 0" type="success" size="small">
                权限: {{ userPermissions.length }}个
              </el-tag>
              <el-tag v-else type="warning" size="small">
                无权限
              </el-tag>
              
              <!-- 系统管理访问状态 -->
              <el-tag v-if="hasSystemManagementPermission()" type="primary" size="small">
                可访问系统管理
              </el-tag>
              <el-tag v-else type="danger" size="small">
                禁止访问系统管理
              </el-tag>
              
              <el-button size="small" text @click="refreshPermissions" style="margin-left: 8px;">
                刷新权限
              </el-button>
              <el-button size="small" text @click="debugPermissions" style="margin-left: 4px;">
                调试权限
              </el-button>
              <el-button size="small" text @click="clearPermissions" style="margin-left: 4px;" type="danger">
                清除权限
              </el-button>
              <el-button size="small" text @click="simulatePermissions" style="margin-left: 4px;" type="success">
                模拟权限
              </el-button>
              <el-button size="small" text @click="forceRefresh" style="margin-left: 4px;" type="warning">
                强制刷新
              </el-button>
              <el-button size="small" text @click="quickTest" style="margin-left: 4px;" type="primary">
                快速测试
              </el-button>
            </div>
            
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="user.avatar || undefined">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ user.nickname || user.username || '用户' }}</span>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>个人信息
                  </el-dropdown-item>
                  <el-dropdown-item command="permissions">
                    <el-icon><Key /></el-icon>查看权限
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>系统设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
      </el-header>
      
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Reading, HomeFilled, User, ArrowDown, Setting, SwitchButton,
  Menu, UserFilled, Lock, Document, Key
} from '@element-plus/icons-vue'

const menus = ref([])
const dynamicMenus = ref([])
const user = ref(JSON.parse(localStorage.getItem('user') || '{}'))
const userPermissions = ref([]) // 用户权限列表
const menuUpdateKey = ref(0) // 强制菜单更新的key
const router = useRouter()
const route = useRoute()
const activeMenu = ref(route.path)

// 监听路由变化
router.afterEach((to) => {
  activeMenu.value = to.path
})

// 检查页面访问权限
const checkPagePermission = (path) => {
  const permissionMap = {
    '/main/user': '用户管理',
    '/main/role': '角色管理', 
    '/main/menu': '菜单管理',
    '/main/permission': '权限管理'
  }
  
  const requiredPermission = permissionMap[path]
  if (requiredPermission) {
    const hasAccess = hasPermission(requiredPermission)
    console.log(`页面访问检查: ${path} -> 需要权限: ${requiredPermission} -> 是否有权限: ${hasAccess}`)
    
    if (!hasAccess) {
      console.log('❌ 用户无权限访问此页面，重定向到首页')
      ElMessage.error(`您没有访问"${requiredPermission}"的权限`)
      router.replace('/main/shou')
      return false
    }
  }
  
  return true
}

// 监听路由变化，检查权限
router.beforeEach((to, from, next) => {
  // 如果页面已经初始化完成，检查权限
  if (userPermissions.value !== undefined) {
    if (checkPagePermission(to.path)) {
      next()
    } else {
      next('/main/shou') // 重定向到首页
    }
  } else {
    next() // 如果权限还未加载完成，先允许访问
  }
})

// 获取菜单图标
const getMenuIcon = (menuName) => {
  const iconMap = {
    '用户管理': UserFilled,
    '角色管理': Lock,
    '菜单管理': Menu,
    '权限管理': Key,
    '系统管理': Setting,
    '课程管理': Document,
    '教师管理': UserFilled,
    '学生管理': User
  }
  return iconMap[menuName] || Menu
}

// 获取当前页面名称
const getCurrentPageName = () => {
  const pathMap = {
    '/main/shou': '系统首页',
    '/main/user': '系统管理 / 用户管理',
    '/main/role': '系统管理 / 角色管理',
    '/main/menu': '系统管理 / 菜单管理',
    '/main/permission': '系统管理 / 权限管理'
  }
  return pathMap[route.path] || '系统管理'
}

// 获取用户权限
const loadUserPermissions = async () => {
  console.log('=== 开始加载用户权限 ===')
  userPermissions.value = [] // 先清空权限
  
  // 第一优先级：从后端获取用户权限
  try {
    if (user.value && user.value.id) {
      console.log(`尝试获取用户 ${user.value.id} 的权限`)
      const res = await axios.get(`/user/${user.value.id}/permissions`)
      if (res.data.code === 200 && res.data.data && res.data.data.length > 0) {
        userPermissions.value = res.data.data
        console.log('✅ 从后端获取用户权限成功:', userPermissions.value)
        // 强制更新菜单
        menuUpdateKey.value++
        return
      }
      console.log('后端用户权限为空或获取失败')
    }
  } catch (error) {
    console.warn('❌ 获取用户权限失败:', error)
  }
  
  // 第二优先级：从角色权限中获取
  try {
    if (user.value && user.value.roleId) {
      console.log(`尝试从角色 ${user.value.roleId} 获取权限`)
      const res = await axios.get(`/role/${user.value.roleId}/permissions`)
      if (res.data.code === 200 && res.data.data && res.data.data.length > 0) {
        userPermissions.value = res.data.data
        console.log('✅ 从角色获取权限成功:', userPermissions.value)
        // 强制更新菜单
        menuUpdateKey.value++
        return
      }
      console.log('角色权限为空或获取失败')
    }
  } catch (roleError) {
    console.warn('❌ 获取角色权限失败:', roleError)
  }
  
  // 第三优先级：从本地存储获取模拟权限数据
  try {
    const mockUserRoles = localStorage.getItem('mockUserRoles')
    const mockRolePermissions = localStorage.getItem('mockRolePermissions')
    
    console.log('本地存储原始数据:')
    console.log('- mockUserRoles:', mockUserRoles)
    console.log('- mockRolePermissions:', mockRolePermissions)
    
    if (mockUserRoles && mockRolePermissions && user.value && user.value.id) {
      console.log('尝试从本地存储获取权限')
      const userRolesData = JSON.parse(mockUserRoles)
      const rolePermData = JSON.parse(mockRolePermissions)
      
      console.log('解析后的数据:')
      console.log('- userRolesData:', userRolesData)
      console.log('- rolePermData:', rolePermData)
      
      const userRoles = userRolesData[user.value.id] || []
      console.log('当前用户的角色:', userRoles)
      
      let allPermissions = []
      userRoles.forEach(role => {
        const roleId = role.roleId || role.id || role
        const rolePerms = rolePermData[roleId] || []
        console.log(`角色 ${roleId} 的权限:`, rolePerms)
        allPermissions = [...allPermissions, ...rolePerms]
      })
      
      console.log('收集到的所有权限ID:', allPermissions)
      
      if (allPermissions.length > 0) {
        // 去重并转换为权限对象格式
        const uniquePermIds = [...new Set(allPermissions)]
        userPermissions.value = uniquePermIds.map(permId => ({
          permissionId: permId,
          permissionName: getPermissionNameById(permId),
          permissionCode: `perm_${permId}`
        }))
        
        console.log('✅ 从本地存储获取权限成功:', userPermissions.value)
        // 强制更新菜单
        menuUpdateKey.value++
        return
      } else {
        console.log('⚠️ 本地存储中没有找到权限数据')
      }
    } else {
      console.log('⚠️ 本地存储数据不完整或用户信息缺失')
    }
  } catch (localError) {
    console.warn('❌ 从本地存储获取权限失败:', localError)
  }
  
  // 如果所有方式都失败，权限列表保持为空
  console.log('❌ 所有权限获取方式都失败，用户无权限')
  userPermissions.value = []
}

// 根据权限ID获取权限名称（模拟数据映射）
const getPermissionNameById = (permId) => {
  const permMap = {
    1: '用户管理', 2: '用户查询', 3: '用户新增', 4: '用户编辑', 5: '用户删除',
    6: '角色管理', 7: '角色查询', 8: '角色新增', 9: '角色编辑', 10: '角色删除',
    11: '菜单管理', 12: '菜单查询', 13: '菜单新增', 14: '菜单编辑', 15: '菜单删除',
    16: '权限管理', 17: '权限查询', 18: '权限新增', 19: '权限编辑', 20: '权限删除'
  }
  return permMap[permId] || `权限${permId}`
}

// 检查用户是否有指定权限
const hasPermission = (permissionName) => {
  console.log(`=== 权限检查: ${permissionName} ===`)
  console.log('当前用户:', user.value)
  console.log('用户权限列表:', userPermissions.value)
  
  // 严格的管理员检查 - 只有用户名是admin才认为是管理员
  const isAdmin = user.value && user.value.username === 'admin'
  console.log('是否为管理员:', isAdmin)
  
  if (isAdmin) {
    console.log('管理员用户，允许访问所有功能')
    return true
  }
  
  // 普通用户必须有明确的权限才能访问
  if (!userPermissions.value || userPermissions.value.length === 0) {
    console.log('普通用户无权限数据，拒绝访问')
    return false
  }
  
  const hasThisPermission = userPermissions.value.some(perm => {
    const match = perm.permissionName === permissionName || 
                  perm.permissionCode === permissionName ||
                  perm.permissionName?.includes(permissionName)
    if (match) {
      console.log(`找到匹配权限:`, perm)
    }
    return match
  })
  
  console.log(`权限检查结果: ${hasThisPermission}`)
  return hasThisPermission
}

// 检查是否有系统管理模块的任何权限
const hasSystemManagementPermission = () => {
  return hasPermission('用户管理') || 
         hasPermission('角色管理') || 
         hasPermission('菜单管理') || 
         hasPermission('权限管理')
}

onMounted(async () => {
  console.log('=== 页面初始化开始 ===')
  console.log('localStorage中的用户信息:', localStorage.getItem('user'))
  console.log('解析后的用户信息:', user.value)
  
  // 监听权限更新事件
  window.addEventListener('permissionsUpdated', (event) => {
    console.log('🔔 收到权限更新通知:', event.detail)
    ElMessage.info('检测到权限更新，正在刷新...')
    setTimeout(() => {
      loadUserPermissions().then(() => {
        // 强制更新菜单
        menuUpdateKey.value++
        ElMessage.success('权限已自动刷新')
      })
    }, 500) // 延迟500ms确保数据已保存
  })
  
  // 先加载用户权限
  await loadUserPermissions()
  
  try {
    // 修复API路径
    const res = await axios.get('/menu/user/current')
    if (res.data.code === 200) {
      menus.value = res.data.data || []
    }
  } catch (error) {
    console.error('获取菜单失败:', error)
    ElMessage.warning('获取菜单失败，使用默认菜单')
  }
  
  // 过滤掉系统管理相关的菜单项，放到dynamicMenus中
  const systemManagementItems = ['用户管理', '角色管理', '菜单管理', '权限管理']
  dynamicMenus.value = menus.value.filter(menu => 
    !systemManagementItems.includes(menu.menuName)
  )
  
  console.log('=== 权限检查总结 ===')
  console.log('当前用户:', user.value)
  console.log('用户权限数量:', userPermissions.value?.length || 0)
  console.log('用户权限列表:', userPermissions.value)
  console.log('系统管理权限检查:', hasSystemManagementPermission())
  console.log('用户管理权限:', hasPermission('用户管理'))
  console.log('角色管理权限:', hasPermission('角色管理'))
  console.log('菜单管理权限:', hasPermission('菜单管理'))
  console.log('权限管理权限:', hasPermission('权限管理'))
  console.log('=== 页面初始化完成 ===')
})

// 刷新权限
const refreshPermissions = async () => {
  ElMessage.info('正在刷新权限...')
  await loadUserPermissions()
  // 强制更新菜单
  menuUpdateKey.value++
  ElMessage.success('权限刷新完成')
}

// 调试权限信息
const debugPermissions = () => {
  // 解析本地存储数据
  let mockUserRoles = {}
  let mockRolePermissions = {}
  
  try {
    mockUserRoles = JSON.parse(localStorage.getItem('mockUserRoles') || '{}')
    mockRolePermissions = JSON.parse(localStorage.getItem('mockRolePermissions') || '{}')
  } catch (e) {
    console.error('解析本地存储失败:', e)
  }
  
  const debugInfo = [
    `📊 === 权限调试信息 ===`,
    ``,
    `👤 用户信息:`,
    `- ID: ${user.value?.id}`,
    `- 用户名: ${user.value?.username}`,
    `- 角色ID: ${user.value?.roleId}`,
    `- 是否管理员: ${user.value?.username === 'admin'}`,
    ``,
    `🔑 当前权限状态:`,
    `- 权限数量: ${userPermissions.value?.length || 0}`,
    `- 权限列表: ${JSON.stringify(userPermissions.value?.map(p => ({id: p.permissionId, name: p.permissionName})), null, 2)}`,
    ``,
    `🧪 权限检查结果:`,
    `- 用户管理: ${hasPermission('用户管理')}`,
    `- 角色管理: ${hasPermission('角色管理')}`,
    `- 菜单管理: ${hasPermission('菜单管理')}`,
    `- 权限管理: ${hasPermission('权限管理')}`,
    `- 系统管理: ${hasSystemManagementPermission()}`,
    ``,
    `💾 本地存储数据:`,
    `- 用户角色分配: ${JSON.stringify(mockUserRoles, null, 2)}`,
    `- 角色权限分配: ${JSON.stringify(mockRolePermissions, null, 2)}`,
    ``,
    `🔍 权限ID映射:`,
    `- 1: 用户管理, 6: 角色管理, 11: 菜单管理, 16: 权限管理`,
    ``,
    `💡 如果权限分配后菜单不显示，请点击"刷新权限"按钮`
  ].join('\n')
  
  ElMessageBox.alert(debugInfo, '权限调试信息', {
    confirmButtonText: '确定',
    cancelButtonText: '刷新权限',
    showCancelButton: true,
    type: 'info',
    customStyle: {
      width: '700px'
    }
  }).then(() => {
    // 点击确定
  }).catch(() => {
    // 点击刷新权限
    refreshPermissions()
  })
  
  console.log('=== 权限调试信息 ===')
  console.log(debugInfo)
}

// 清除权限（用于测试）
const clearPermissions = () => {
  ElMessageBox.confirm('确认要清除当前用户的权限吗？这将模拟普通用户无权限的状态。', '清除权限', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userPermissions.value = []
    localStorage.removeItem('mockUserRoles')
    localStorage.removeItem('mockRolePermissions')
    // 强制更新菜单
    menuUpdateKey.value++
    ElMessage.success('权限已清除，现在是无权限状态')
    console.log('权限已清除')
  }).catch(() => {
    ElMessage.info('已取消')
  })
}

// 模拟权限分配（快速测试）
const simulatePermissions = () => {
  ElMessageBox.prompt('选择要模拟的权限场景 (输入数字):<br/>0-无权限<br/>1-仅用户管理<br/>2-仅角色管理<br/>3-用户+角色管理<br/>4-全部权限', '模拟权限分配', {
    confirmButtonText: '应用',
    cancelButtonText: '取消',
    inputType: 'number',
    inputValue: '3',
    dangerouslyUseHTMLString: true
  }).then(({ value }) => {
    const scenario = parseInt(value)
    
    if (!user.value || !user.value.id) {
      ElMessage.error('用户信息不完整')
      return
    }
    
    // 模拟角色ID
    const roleId = user.value.roleId || 2
    
    // 定义权限场景
    const permissionScenarios = {
      0: [], // 无权限
      1: [1], // 仅用户管理
      2: [6], // 仅角色管理  
      3: [1, 6], // 用户+角色管理
      4: [1, 6, 11, 16] // 全部权限
    }
    
    const permissions = permissionScenarios[scenario]
    if (permissions === undefined) {
      ElMessage.error('无效的场景选择，请输入0-4之间的数字')
      return
    }
    
    // 模拟角色分配
    const mockUserRoles = {
      [user.value.id]: [{ roleId: roleId, roleName: '测试角色' }]
    }
    
    // 模拟权限分配
    const mockRolePermissions = permissions.length > 0 ? {
      [roleId]: permissions
    } : {}
    
    // 保存到localStorage
    localStorage.setItem('mockUserRoles', JSON.stringify(mockUserRoles))
    localStorage.setItem('mockRolePermissions', JSON.stringify(mockRolePermissions))
    
    // 刷新权限
    loadUserPermissions().then(() => {
      // 强制更新菜单
      menuUpdateKey.value++
      
      const permNames = permissions.map(id => getPermissionNameById(id)).join(', ')
      const message = permissions.length > 0 
        ? `✅ 已模拟分配权限: ${permNames}`
        : '❌ 已模拟清除所有权限'
      
      ElMessage.success(message)
      console.log(`权限模拟完成: 场景${scenario}, 权限:`, permissions)
    })
    
  }).catch(() => {
    ElMessage.info('已取消')
  })
}

// 强制刷新权限（清除缓存重新加载）
const forceRefresh = async () => {
  ElMessage.info('正在强制刷新权限和页面...')
  
  // 清除所有缓存
  userPermissions.value = []
  
  // 重新加载权限
  await loadUserPermissions()
  
  // 强制重新渲染（可选）
  window.location.reload()
}

// 快速测试权限系统
const quickTest = async () => {
  ElMessage.info('开始快速测试权限系统...')
  
  // 如果当前没有权限，给用户管理权限
  if (!userPermissions.value || userPermissions.value.length === 0) {
    console.log('当前无权限，自动添加用户管理权限进行测试')
    
    // 模拟分配用户管理权限
    const userId = user.value.id || '1'
    const roleId = user.value.roleId || 2
    
    const mockUserRoles = {
      [userId]: [{ roleId: roleId, roleName: '测试角色' }]
    }
    
    const mockRolePermissions = {
      [roleId]: [1] // 用户管理权限
    }
    
    localStorage.setItem('mockUserRoles', JSON.stringify(mockUserRoles))
    localStorage.setItem('mockRolePermissions', JSON.stringify(mockRolePermissions))
    
    await loadUserPermissions()
    menuUpdateKey.value++
    
    setTimeout(() => {
      ElMessage.success('✅ 测试完成！已自动分配用户管理权限，菜单应该显示了')
      console.log('测试用权限已分配，菜单应该可见')
    }, 500)
  } else {
    ElMessage.success('✅ 当前已有权限，菜单应该正常显示')
    console.log('当前权限状态:', userPermissions.value)
  }
}

// 查看当前用户权限
const showUserPermissions = () => {
  if (userPermissions.value.length === 0) {
    ElMessage.warning('当前用户暂无权限数据')
    return
  }
  
  const permissionList = userPermissions.value.map(perm => 
    `• ${perm.permissionName || perm.permissionCode}`
  ).join('\n')
  
  ElMessageBox.alert(permissionList, '当前用户权限列表', {
    confirmButtonText: '确定',
    type: 'info'
  })
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'permissions':
      showUserPermissions()
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'logout':
      await logout()
      break
  }
}

const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    localStorage.clear()
    ElMessage.success('退出成功')
    router.push('/login')
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.main-container {
  background-color: #f5f7fa;
}

.sidebar {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #ecf0f1;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-icon {
  color: #3498db;
  margin-right: 10px;
}

.logo-section h3 {
  margin: 0;
  font-weight: 600;
  font-size: 18px;
}

.sidebar-menu {
  border: none;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.sidebar-menu .el-menu-item:hover {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #3498db;
  color: #ffffff;
}

.sidebar-menu .el-sub-menu .el-sub-menu__title {
  height: 50px;
  line-height: 50px;
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.sidebar-menu .el-sub-menu .el-sub-menu__title:hover {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.sidebar-menu .el-sub-menu.is-active .el-sub-menu__title {
  background-color: #3498db;
  color: #ffffff;
}

.header {
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.permission-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  flex-wrap: wrap;
  max-width: 480px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f8f9fa;
}

.username {
  margin: 0 8px;
  font-weight: 500;
  color: #2c3e50;
}

.main-content {
  background-color: #f5f7fa;
  padding: 24px;
  overflow-y: auto;
}

:deep(.el-breadcrumb__inner) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-breadcrumb__inner.is-link) {
  color: #409eff;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>