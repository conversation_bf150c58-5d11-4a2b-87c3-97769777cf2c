# AI会员充值服务

## 项目简介

AI会员充值服务是AI教学系统的会员管理模块，提供完整的会员充值、支付处理、会员状态管理等功能。

## 功能特性

### 核心功能
- ✅ 会员充值订单创建
- ✅ 多支付渠道支持（微信支付、支付宝）
- ✅ 支付回调处理
- ✅ 会员时间计算和管理
- ✅ 充值记录查询
- ✅ 会员状态检查

### 管理功能
- ✅ 充值统计数据
- ✅ 即将到期会员提醒
- ✅ 会员时间延长
- ✅ 订单取消处理

## 技术栈

- **框架**: Spring Boot 2.x
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus + JPA
- **构建工具**: Maven
- **支付集成**: 支付宝SDK、微信支付SDK

## 项目结构

```
src/main/java/cn/zhentao/
├── MemberApplication.java          # 启动类
├── common/                         # 通用类
│   └── Result.java                # 统一响应结果
├── config/                        # 配置类
│   └── PaymentConfig.java         # 支付配置
├── controller/                    # 控制器层
│   └── MemberRechargeController.java
├── dto/                          # 数据传输对象
│   ├── RechargeRequest.java      # 充值请求DTO
│   └── RechargeResponse.java     # 充值响应DTO
├── entity/                       # 实体类
│   └── MemberRecharge.java       # 会员充值实体
├── mapper/                       # 数据访问层
│   └── MemberRechargeMapper.java
└── service/                      # 服务层
    ├── MemberRechargeService.java
    ├── PaymentService.java
    └── impl/
        ├── MemberRechargeServiceImpl.java
        └── PaymentServiceImpl.java
```

## 数据库设计

### 会员充值记录表 (member_recharge)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| user_id | bigint | 关联用户ID |
| username | varchar(50) | 用户名 |
| role_id | bigint | 角色ID |
| role_name | varchar(50) | 角色名称 |
| recharge_amount | decimal(10,2) | 充值金额 |
| pay_channel | tinyint | 支付渠道（1-微信，2-支付宝） |
| trade_no | varchar(128) | 支付订单号 |
| pay_status | tinyint | 支付状态（0-未支付，1-已支付，2-支付失败） |
| pay_time | datetime | 支付完成时间 |
| member_start_time | datetime | 会员生效时间 |
| member_end_time | datetime | 会员失效时间 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| remark | varchar(255) | 备注 |

## API接口

### 用户接口

#### 1. 创建充值订单
```
POST /api/member/recharge/create
```

请求参数：
```json
{
  "userId": 1,
  "roleId": 1,
  "rechargeAmount": 99.00,
  "payChannel": 1,
  "memberMonths": 3,
  "remark": "VIP会员充值"
}
```

#### 2. 查询充值记录
```
GET /api/member/recharge/records?userId=1&pageNum=1&pageSize=10
```

#### 3. 查询会员状态
```
GET /api/member/recharge/member/status/{userId}
```

#### 4. 取消充值订单
```
POST /api/member/recharge/cancel/{rechargeId}?userId=1
```

### 支付回调接口

#### 微信支付回调
```
POST /api/member/recharge/callback/wechat
```

#### 支付宝回调
```
POST /api/member/recharge/callback/alipay
```

### 管理员接口

#### 1. 充值统计
```
GET /api/member/recharge/admin/statistics?startTime=2024-01-01 00:00:00&endTime=2024-12-31 23:59:59
```

#### 2. 即将到期会员
```
GET /api/member/recharge/admin/expiring
```

#### 3. 延长会员时间
```
POST /api/member/recharge/admin/extend?userId=1&months=1
```

## 配置说明

### 数据库配置
```yaml
spring:
  datasource:
    url: ************************************
    username: root
    password: 123456
```

### 支付配置
```yaml
payment:
  wechat:
    app-id: wx1234567890abcdef
    mch-id: 1234567890
    api-key: your_wechat_api_key_here
  alipay:
    app-id: 2021000000000000
    private-key: your_alipay_private_key_here
```

## 部署说明

1. **数据库初始化**
   ```sql
   # 执行建表脚本
   source src/main/resources/sql/member_recharge.sql
   ```

2. **配置文件修改**
   - 修改 `application.yml` 中的数据库连接信息
   - 配置支付渠道的相关参数

3. **启动服务**
   ```bash
   mvn spring-boot:run
   ```

4. **访问地址**
   - 服务地址: http://localhost:8083/member
   - 健康检查: http://localhost:8083/member/actuator/health

## 测试

运行测试类：
```bash
mvn test
```

## 注意事项

1. **支付安全**: 生产环境必须配置真实的支付密钥和证书
2. **回调地址**: 支付回调地址必须是公网可访问的HTTPS地址
3. **数据备份**: 充值记录涉及资金，建议定期备份数据
4. **日志监控**: 关注支付相关的日志，及时发现异常

## 扩展功能

- [ ] 支付宝当面付集成
- [ ] 微信小程序支付
- [ ] 会员权益管理
- [ ] 充值优惠券系统
- [ ] 自动续费功能
