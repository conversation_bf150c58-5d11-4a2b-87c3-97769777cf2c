.subtitle {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 4;
  display: flex;
  align-items: center;
  justify-content: center;

  ._inner {
    display: flex;
    padding: 16px;
    margin: 0 32px;
  }

  ._source {
    width: 20px;
    margin: 0 4px;
  }

  ._agent-icon {
    margin-top: 4px;
    width: 20px;
    height: 30px;
    background-image: url(https://img.alicdn.com/imgextra/i1/O1CN01fLZLq91Wpr2yasQMB_!!6000000002838-2-tps-35-40.png);
    background-repeat: no-repeat;
    background-size: 17.5px 20px;
  }

  & ._text {
    flex: 1;
    margin-top: 2px;
    text-align: left;
    font-size: 12px;
    line-height: 18px;
    color: #26244c;
  }

  ._more {
    padding: 0 4px;
  }

  ._detail {
    color: #fff;
    font-size: 14px;
    line-height: 22px;
  }
}

.has-video .subtitle ._text {
  color: #747a8c;
}

.subtitle-mask {
  .adm-button {
    background: transparent;
    border: none;
    outline: none;
  }
  .adm-mask-content {
    max-height: 100%;
    overflow-y: auto;
    ._detail {
      margin: 50px 25px;
      color: #fff;
      font-size: 14px;
      line-height: 22px;
    }
    ._detail-close {
      text-align: right;
      margin-right: -10px;
    }
  }
}

@media screen and (min-width: 768px) {
  .subtitle {
    position: absolute;
    max-height: 100px;
    overflow-y: auto;
    box-sizing: border-box;
    text-align: center;
    margin: 0 auto;

    ._inner {
      max-width: 640px;
    }
  }
}
