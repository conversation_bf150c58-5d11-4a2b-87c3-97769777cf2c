package cn.zhentao.dto;

import lombok.Data;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员充值响应DTO
 */
@Data
@Builder
public class RechargeResponse {

    /**
     * 充值记录ID
     */
    private Long rechargeId;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 支付渠道
     */
    private Integer payChannel;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 支付状态名称
     */
    private String payStatusName;

    /**
     * 支付订单号
     */
    private String tradeNo;

    /**
     * 支付二维码（用于扫码支付）
     */
    private String qrCode;

    /**
     * 支付链接（用于跳转支付）
     */
    private String payUrl;

    /**
     * 会员生效时间
     */
    private LocalDateTime memberStartTime;

    /**
     * 会员失效时间
     */
    private LocalDateTime memberEndTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
