package cn.zhentao.controller;

import cn.zhentao.dto.RechargeRequest;
import cn.zhentao.dto.RechargeResponse;
import cn.zhentao.entity.MemberRecharge;
import cn.zhentao.service.MemberRechargeService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员充值控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/member/recharge")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class MemberRechargeController {

    private final MemberRechargeService memberRechargeService;

    /**
     * 创建充值订单
     */
    @PostMapping("/create")
    public Map<String, Object> createRecharge(@Valid @RequestBody RechargeRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            RechargeResponse response = memberRechargeService.createRechargeOrder(request);
            
            result.put("code", 200);
            result.put("message", "充值订单创建成功");
            result.put("data", response);
            
        } catch (Exception e) {
            log.error("创建充值订单失败", e);
            result.put("code", 500);
            result.put("message", "创建充值订单失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 支付回调接口（微信支付）
     */
    @PostMapping("/callback/wechat")
    public String wechatPayCallback(HttpServletRequest request) {
        try {
            // 获取回调参数
            Map<String, Object> callbackData = new HashMap<>();
            callbackData.put("return_code", request.getParameter("return_code"));
            callbackData.put("result_code", request.getParameter("result_code"));
            callbackData.put("out_trade_no", request.getParameter("out_trade_no"));
            callbackData.put("transaction_id", request.getParameter("transaction_id"));
            
            String tradeNo = request.getParameter("out_trade_no");
            boolean success = memberRechargeService.handlePaymentCallback(
                    tradeNo, MemberRecharge.PayChannel.WECHAT.getCode(), callbackData);
            
            if (success) {
                return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
            } else {
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>";
            }
            
        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>";
        }
    }

    /**
     * 支付回调接口（支付宝）
     */
    @PostMapping("/callback/alipay")
    public String alipayCallback(HttpServletRequest request) {
        try {
            // 获取回调参数
            Map<String, Object> callbackData = new HashMap<>();
            callbackData.put("trade_status", request.getParameter("trade_status"));
            callbackData.put("out_trade_no", request.getParameter("out_trade_no"));
            callbackData.put("trade_no", request.getParameter("trade_no"));
            
            String tradeNo = request.getParameter("out_trade_no");
            boolean success = memberRechargeService.handlePaymentCallback(
                    tradeNo, MemberRecharge.PayChannel.ALIPAY.getCode(), callbackData);
            
            return success ? "success" : "fail";
            
        } catch (Exception e) {
            log.error("支付宝回调处理失败", e);
            return "fail";
        }
    }

    /**
     * 查询用户充值记录
     */
    @GetMapping("/records")
    public Map<String, Object> getRechargeRecords(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer payStatus) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            Page<MemberRecharge> page = new Page<>(pageNum, pageSize);
            IPage<MemberRecharge> records = memberRechargeService.getUserRechargeRecords(page, userId, payStatus);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", records);
            
        } catch (Exception e) {
            log.error("查询充值记录失败", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 查询充值记录详情
     */
    @GetMapping("/detail/{rechargeId}")
    public Map<String, Object> getRechargeDetail(@PathVariable Long rechargeId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            MemberRecharge recharge = memberRechargeService.getById(rechargeId);
            if (recharge == null) {
                result.put("code", 404);
                result.put("message", "充值记录不存在");
                return result;
            }
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", recharge);
            
        } catch (Exception e) {
            log.error("查询充值记录详情失败", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查用户会员状态
     */
    @GetMapping("/member/status/{userId}")
    public Map<String, Object> getMemberStatus(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean isValidMember = memberRechargeService.isValidMember(userId);
            MemberRecharge currentMember = memberRechargeService.getCurrentMemberInfo(userId);
            Double totalAmount = memberRechargeService.getTotalRechargeAmount(userId);
            
            Map<String, Object> data = new HashMap<>();
            data.put("isValidMember", isValidMember);
            data.put("currentMember", currentMember);
            data.put("totalRechargeAmount", totalAmount);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", data);
            
        } catch (Exception e) {
            log.error("查询会员状态失败", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 取消充值订单
     */
    @PostMapping("/cancel/{rechargeId}")
    public Map<String, Object> cancelRecharge(@PathVariable Long rechargeId, @RequestParam Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = memberRechargeService.cancelRechargeOrder(rechargeId, userId);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "取消成功");
            } else {
                result.put("code", 400);
                result.put("message", "取消失败，订单状态不允许取消");
            }
            
        } catch (Exception e) {
            log.error("取消充值订单失败", e);
            result.put("code", 500);
            result.put("message", "取消失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 生成支付二维码
     */
    @GetMapping("/qrcode/{rechargeId}")
    public Map<String, Object> generateQrCode(@PathVariable Long rechargeId, @RequestParam Integer payChannel) {
        Map<String, Object> result = new HashMap<>();

        try {
            String qrCode = memberRechargeService.generatePaymentQrCode(rechargeId, payChannel);

            result.put("code", 200);
            result.put("message", "生成成功");
            result.put("data", qrCode);

        } catch (Exception e) {
            log.error("生成支付二维码失败", e);
            result.put("code", 500);
            result.put("message", "生成失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取充值统计数据（管理员接口）
     */
    @GetMapping("/admin/statistics")
    public Map<String, Object> getRechargeStatistics(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> statistics = memberRechargeService.getRechargeStatistics(startTime, endTime);

            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statistics);

        } catch (Exception e) {
            log.error("查询充值统计失败", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取即将到期的会员列表（管理员接口）
     */
    @GetMapping("/admin/expiring")
    public Map<String, Object> getExpiringMembers() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<MemberRecharge> expiringMembers = memberRechargeService.getExpiringMembers();

            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", expiringMembers);

        } catch (Exception e) {
            log.error("查询即将到期会员失败", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 延长用户会员时间（管理员接口）
     */
    @PostMapping("/admin/extend")
    public Map<String, Object> extendMemberTime(@RequestParam Long userId, @RequestParam Integer months) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = memberRechargeService.extendMemberTime(userId, months);

            if (success) {
                result.put("code", 200);
                result.put("message", "延长成功");
            } else {
                result.put("code", 400);
                result.put("message", "延长失败，用户可能没有有效会员");
            }

        } catch (Exception e) {
            log.error("延长会员时间失败", e);
            result.put("code", 500);
            result.put("message", "延长失败: " + e.getMessage());
        }

        return result;
    }
}
