
	/* 主容器 */
.chat-detail.data-v-02c82538 {
		height: 100vh;
		display: flex;
		flex-direction: column;
		background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
		/* 性能优化 */
		will-change: auto;
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		-webkit-backface-visibility: hidden;
		backface-visibility: hidden;
}

	/* 顶部导航栏 */
.chat-header.data-v-02c82538 {
		background: #393a3f;
		padding-top: constant(safe-area-inset-top);
		padding-top: env(safe-area-inset-top);
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
}
.header-content.data-v-02c82538 {
		display: flex;
		align-items: center;
		height: 88rpx;
		padding: 0 30rpx;
}
.header-left.data-v-02c82538 {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-icon.data-v-02c82538 {
		color: white;
		font-size: 60rpx;
		font-weight: 300;
}
.header-center.data-v-02c82538 {
		flex: 1;
		text-align: center;
}
.chat-title.data-v-02c82538 {
		color: white;
		font-size: 36rpx;
		font-weight: 500;
		display: block;
}
.online-status.data-v-02c82538 {
		color: rgba(255, 255, 255, 0.7);
		font-size: 24rpx;
		display: block;
		margin-top: 4rpx;
}
.header-right.data-v-02c82538 {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.more-icon.data-v-02c82538 {
		color: white;
		font-size: 40rpx;
		font-weight: bold;
}

	/* 连接状态提示 - 已移除 */
	
	/* 消息列表 */
.message-list.data-v-02c82538 {
		flex: 1;
		padding: 10rpx 0;
		margin-top: 88rpx;
		background: transparent;
		height: calc(100vh - 88rpx - 100rpx);
		padding-top: constant(safe-area-inset-top);
		padding-top: env(safe-area-inset-top);
		position: relative;
		/* 优化滚动性能 */
		will-change: scroll-position;
		-webkit-overflow-scrolling: touch;
		contain: layout style paint;
		/* 启用硬件加速 */
		transform: translateZ(0);
		-webkit-transform: translateZ(0);
		/* 优化渲染性能 */
		backface-visibility: hidden;
		-webkit-backface-visibility: hidden;
		/* 减少重绘 */
		overflow-anchor: auto;
}

	/* 聊天背景 */
.chat-background.data-v-02c82538 {
		position: fixed;
		top: 88rpx;
		left: 0;
		right: 0;
		bottom: 100rpx;
		background-image: url('https://img1.baidu.com/it/u=3602773692,1512483864&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500');
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		opacity: 0.3;
		z-index: 0;
}
.load-more.data-v-02c82538 {
		text-align: center;
		padding: 20rpx;
}
.loading-indicator.data-v-02c82538 {
		display: inline-block;
		background: rgba(0, 0, 0, 0.1);
		border-radius: 20rpx;
		padding: 10rpx 20rpx;
}
.loading-text.data-v-02c82538 {
		color: #666;
		font-size: 24rpx;
}
.time-divider.data-v-02c82538 {
		text-align: center;
		margin: 20rpx 0;
}
.time-label.data-v-02c82538 {
		display: inline-block;
		background: rgba(0, 0, 0, 0.08);
		border-radius: 10rpx;
		padding: 6rpx 12rpx;
}
.time-text.data-v-02c82538 {
		color: #888;
		font-size: 20rpx;
}
.message-item.data-v-02c82538 {
		margin-bottom: 8rpx;
		position: relative;
		z-index: 1;
		/* 优化渲染性能 */
		contain: layout style;
		will-change: auto;
		/* 启用硬件加速 */
		transform: translateZ(0);
		-webkit-transform: translateZ(0);
		/* 减少动画，提升滚动性能 */
		/* animation: messageSlideIn 0.2s ease-out; */
}
@-webkit-keyframes messageSlideIn-02c82538 {
from {
			opacity: 0;
			-webkit-transform: translateY(10rpx);
			        transform: translateY(10rpx);
}
to {
			opacity: 1;
			-webkit-transform: translateY(0);
			        transform: translateY(0);
}
}
@keyframes messageSlideIn-02c82538 {
from {
			opacity: 0;
			-webkit-transform: translateY(10rpx);
			        transform: translateY(10rpx);
}
to {
			opacity: 1;
			-webkit-transform: translateY(0);
			        transform: translateY(0);
}
}
.message-wrapper.data-v-02c82538 {
		display: flex;
		align-items: flex-end;
		margin-bottom: 8rpx;
		padding: 0 20rpx;
}

	/* 自己的消息布局：右对齐 */
.own-message-wrapper.data-v-02c82538 {
		justify-content: flex-end;
}

	/* 对方的消息布局：左对齐 */
.message-wrapper.data-v-02c82538:not(.own-message-wrapper) {
		justify-content: flex-start;
}
.message-bubble.data-v-02c82538 {
		max-width: 65%;
		min-width: 80rpx;
		position: relative;
		-webkit-transform: translateZ(0);
		        transform: translateZ(0); /* 启用硬件加速 */
		transition: -webkit-transform 0.15s ease;
		transition: transform 0.15s ease;
		transition: transform 0.15s ease, -webkit-transform 0.15s ease;
}
.message-bubble.data-v-02c82538:active {
		-webkit-transform: scale(0.98) translateZ(0);
		        transform: scale(0.98) translateZ(0);
}
.message-content.data-v-02c82538 {
		padding: 16rpx 20rpx;
		border-radius: 20rpx;
		background: #f8f9fa;
		word-wrap: break-word;
		line-height: 1.35;
		font-size: 28rpx;
		position: relative;
		box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #e9ecef;
		z-index: 1;
		min-height: 40rpx;
		display: flex;
		align-items: center;
}
.own-bubble .message-content.data-v-02c82538 {
		background: linear-gradient(135deg, #007AFF 0%, #4A90E2 100%);
		color: white;
		border: none;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}
	
	/* 消息气泡尖角 */
.message-content.data-v-02c82538::before {
		content: '';
		position: absolute;
		top: 16rpx;
		left: -12rpx;
		width: 0;
		height: 0;
		border: 12rpx solid transparent;
		border-right-color: #f8f9fa;
		-webkit-filter: drop-shadow(-1rpx 0 2rpx rgba(0, 0, 0, 0.06));
		        filter: drop-shadow(-1rpx 0 2rpx rgba(0, 0, 0, 0.06));
}
.own-bubble .message-content.data-v-02c82538::before {
		left: auto;
		right: -12rpx;
		border-right-color: transparent;
		border-left-color: #007AFF;
		-webkit-filter: drop-shadow(1rpx 0 2rpx rgba(0, 122, 255, 0.2));
		        filter: drop-shadow(1rpx 0 2rpx rgba(0, 122, 255, 0.2));
}
.message-text.data-v-02c82538 {
		color: #333;
		font-size: 28rpx;
		line-height: 1.35;
		word-break: break-word;
		font-weight: 400;
		letter-spacing: 0.3rpx;
}
.own-bubble .message-text.data-v-02c82538 {
		color: white;
}
.message-status.data-v-02c82538 {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-top: 4rpx;
		padding: 0 20rpx;
}

	/* 自己消息的状态显示在右侧 */
.message-status.own-status.data-v-02c82538 {
		justify-content: flex-end;
		margin-right: 0; /* 移除头像空间 */
}
.message-time.data-v-02c82538 {
		font-size: 18rpx;
		color: #aaa;
		margin-top: 2rpx;
}
.send-status.data-v-02c82538 {
		font-size: 18rpx;
		margin-left: 8rpx;
}
.status-sending.data-v-02c82538 {
		color: #aaa;
}
.status-failed.data-v-02c82538 {
		color: #ff4757;
}
.status-sent.data-v-02c82538 {
		color: #2ed573;
}
.status-read.data-v-02c82538 {
		color: #667eea;
}
.empty-messages.data-v-02c82538 {
		text-align: center;
		padding: 200rpx 0;
		color: #999;
}
.empty-tip.data-v-02c82538 {
		font-size: 24rpx;
		color: #ccc;
		margin-top: 20rpx;
		display: block;
}
	
	/* 输入区域 */
.input-area.data-v-02c82538 {
		background: rgba(255, 255, 255, 0.98);
		-webkit-backdrop-filter: blur(20rpx);
		        backdrop-filter: blur(20rpx);
		border-top: 1rpx solid rgba(0, 0, 0, 0.08);
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
}
.input-area.panel-open.data-v-02c82538 {
		position: relative;
}
.input-wrapper.data-v-02c82538 {
		display: flex;
		align-items: flex-end;
		padding: 16rpx 20rpx;
		gap: 12rpx;
		-webkit-transform: translateZ(0);
		        transform: translateZ(0); /* 启用硬件加速 */
}
.function-btn.data-v-02c82538 {
		width: 64rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 18rpx;
		background: #f8f9fa;
		border: 1rpx solid #e9ecef;
		flex-shrink: 0;
		transition: all 0.2s ease;
		-webkit-transform: translateZ(0);
		        transform: translateZ(0); /* 启用硬件加速 */
}
.function-btn.data-v-02c82538:active {
		-webkit-transform: scale(0.95) translateZ(0);
		        transform: scale(0.95) translateZ(0);
}
.function-btn.active.data-v-02c82538 {
		background: #007AFF;
		border-color: #007AFF;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.25);
}
.function-btn.active .function-icon.data-v-02c82538 {
		color: white;
}
.function-icon.data-v-02c82538 {
		font-size: 30rpx;
		color: #666;
		font-weight: 500;
}
.input-container.data-v-02c82538 {
		flex: 1;
		background: #f8f9fa;
		border-radius: 18rpx;
		border: 1rpx solid #e9ecef;
		min-height: 64rpx;
		max-height: 160rpx;
		transition: all 0.2s ease;
}
.input-container.data-v-02c82538:focus-within {
		border-color: #007AFF;
		background: white;
		box-shadow: 0 0 0 3rpx rgba(0, 122, 255, 0.08);
}
.message-input.data-v-02c82538 {
		width: 100%;
		min-height: 64rpx;
		max-height: 160rpx;
		padding: 16rpx 18rpx;
		font-size: 28rpx;
		line-height: 1.35;
		background: transparent;
		border: none;
		resize: none;
		color: #333;
		font-weight: 400;
		letter-spacing: 0.3rpx;
		box-sizing: border-box;
}
.send-btn.data-v-02c82538 {
		width: 100rpx;
		height: 64rpx;
		background: linear-gradient(135deg, #007AFF 0%, #4A90E2 100%);
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
		transition: all 0.2s ease;
}
.send-btn.data-v-02c82538:active {
		-webkit-transform: scale(0.95) translateZ(0);
		        transform: scale(0.95) translateZ(0);
}
.send-btn.sending.data-v-02c82538 {
		background: #999;
		box-shadow: none;
}
.send-text.data-v-02c82538 {
		color: white;
		font-size: 24rpx;
		font-weight: 500;
		letter-spacing: 0.5rpx;
}
.voice-btn.data-v-02c82538 {
		width: 64rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: #07c160;
		flex-shrink: 0;
}
.voice-icon.data-v-02c82538 {
		color: white;
		font-size: 32rpx;
}
.emoji-btn.data-v-02c82538 {
		width: 70rpx;
		height: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		border-radius: 50%;
		background: #f5f5f5;
}
.emoji-btn text.data-v-02c82538 {
		font-size: 32rpx;
}
.send-btn.active.data-v-02c82538 {
		background: #667eea;
}
.send-btn.data-v-02c82538:disabled {
		opacity: 0.6;
}
	
	/* 表情面板 */
.emoji-panel.data-v-02c82538 {
		background: #f7f7f7;
		border-top: 1rpx solid #e5e5e5;
		height: 400rpx;
}
.emoji-header.data-v-02c82538 {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #e5e5e5;
}
.emoji-title.data-v-02c82538 {
		font-size: 28rpx;
		color: #666;
}
.emoji-scroll.data-v-02c82538 {
		height: 320rpx;
}
.emoji-grid.data-v-02c82538 {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
		gap: 20rpx;
}
.emoji-item.data-v-02c82538 {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 10rpx;
		background: white;
		transition: all 0.2s;
}
.emoji-text.data-v-02c82538 {
		font-size: 40rpx;
}
.emoji-item.data-v-02c82538:active {
		background: #e5e5e5;
		-webkit-transform: scale(0.95);
		        transform: scale(0.95);
}

	/* 更多功能面板 */
.more-panel.data-v-02c82538 {
		background: #f7f7f7;
		border-top: 1rpx solid #e5e5e5;
		height: 400rpx;
}
.more-header.data-v-02c82538 {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #e5e5e5;
}
.more-title.data-v-02c82538 {
		font-size: 28rpx;
		color: #666;
}
.more-grid.data-v-02c82538 {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 40rpx;
		padding: 40rpx;
}
.more-item.data-v-02c82538 {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
}
.more-icon-wrapper.data-v-02c82538 {
		width: 120rpx;
		height: 120rpx;
		background: white;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.more-icon.data-v-02c82538 {
		font-size: 60rpx;
}
.more-text.data-v-02c82538 {
		font-size: 24rpx;
		color: #666;
}
.more-item:active .more-icon-wrapper.data-v-02c82538 {
		background: #f0f0f0;
		-webkit-transform: scale(0.95);
		        transform: scale(0.95);
}

	/* 聊天菜单弹窗 */
.chat-menu-modal.data-v-02c82538 {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.3);
		z-index: 2000;
		display: flex;
		align-items: flex-start;
		justify-content: flex-end;
		padding: 108rpx 30rpx 0 0;
}
.chat-menu.data-v-02c82538 {
		background: #4c4c4c;
		border-radius: 8rpx;
		overflow: hidden;
		min-width: 240rpx;
}
.chat-menu-item.data-v-02c82538 {
		display: flex;
		align-items: center;
		padding: 24rpx 30rpx;
		gap: 20rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}
.chat-menu-item.data-v-02c82538:last-child {
		border-bottom: none;
}
.chat-menu-item.data-v-02c82538:active {
		background: rgba(255, 255, 255, 0.1);
}
.menu-icon.data-v-02c82538 {
		font-size: 32rpx;
		color: white;
}
.menu-text.data-v-02c82538 {
		color: white;
		font-size: 28rpx;
}
