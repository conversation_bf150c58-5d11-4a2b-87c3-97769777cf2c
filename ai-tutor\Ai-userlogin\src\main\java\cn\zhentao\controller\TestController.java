package cn.zhentao.controller;

import cn.zhentao.service.UserService;
import cn.zhentao.pojo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired(required = false)
    private UserService userService;

    @GetMapping("/ping")
    public Map<String, Object> ping() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "pong");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    @GetMapping("/users")
    public Map<String, Object> getAllUsers() {
        Map<String, Object> result = new HashMap<>();
        try {
            if (userService == null) {
                result.put("error", "UserService is null");
                return result;
            }
            
            List<User> users = userService.list();
            result.put("message", "success");
            result.put("count", users.size());
            result.put("users", users);
        } catch (Exception e) {
            result.put("error", e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }
        return result;
    }

    @GetMapping("/user/{username}")
    public Map<String, Object> getUser(@PathVariable String username) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (userService == null) {
                result.put("error", "UserService is null");
                return result;
            }
            
            User user = userService.getUserByUsername(username);
            result.put("message", "success");
            result.put("user", user);
        } catch (Exception e) {
            result.put("error", e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }
        return result;
    }

    @PostMapping("/simple-login")
    public Map<String, Object> simpleLogin(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            String username = request.get("username");
            String password = request.get("password");
            
            result.put("received_username", username);
            result.put("received_password", password != null ? "***" : null);
            
            if (userService == null) {
                result.put("error", "UserService is null");
                return result;
            }
            
            User user = userService.getUserByUsername(username);
            result.put("user_found", user != null);
            if (user != null) {
                result.put("user_id", user.getId());
                result.put("stored_password", user.getPassword() != null ? "***" : null);
            }
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
            result.put("exception", e.getClass().getSimpleName());
        }
        return result;
    }
} 