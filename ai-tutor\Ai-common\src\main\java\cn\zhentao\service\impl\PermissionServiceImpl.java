package cn.zhentao.service.impl;

import cn.zhentao.mapper.PermissionMapper;
import cn.zhentao.mapper.RolePermissionMapper;
import cn.zhentao.mapper.UserRoleMapper;
import cn.zhentao.pojo.Permission;
import cn.zhentao.pojo.RolePermission;
import cn.zhentao.pojo.UserRole;
import cn.zhentao.service.PermissionService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【permission(权限定义表)】的数据库操作Service实现
* @createDate 2025-07-28 16:25:23
*/
@Service
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission>
    implements PermissionService{

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Override
    public IPage<Permission> getPermissionPage(Page<Permission> page, String permissionName, String permissionCode) {
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();

        if (StringUtils.hasText(permissionName)) {
            wrapper.like("permission_name", permissionName);
        }

        if (StringUtils.hasText(permissionCode)) {
            wrapper.like("permission_code", permissionCode);
        }

        wrapper.orderByAsc("permission_id");

        return permissionMapper.selectPage(page, wrapper);
    }

    @Override
    public Permission getPermissionByCode(String permissionCode) {
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();
        wrapper.eq("permission_code", permissionCode);
        return permissionMapper.selectOne(wrapper);
    }

    @Override
    public Permission getPermissionByApiPath(String apiPath, String method) {
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();
        wrapper.eq("api_path", apiPath);
        wrapper.eq("method", method);
        return permissionMapper.selectOne(wrapper);
    }

    @Override
    public List<Permission> getPermissionTree() {
        // 获取所有权限，按菜单分组构建树结构
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("menu_id", "permission_id");
        return permissionMapper.selectList(wrapper);
    }

    @Override
    public List<Permission> getPermissionsByMenuId(Long menuId) {
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();
        wrapper.eq("menu_id", menuId);
        return permissionMapper.selectList(wrapper);
    }

    @Override
    public boolean existsByPermissionCode(String permissionCode) {
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();
        wrapper.eq("permission_code", permissionCode);
        return permissionMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsByApiPath(String apiPath, String method) {
        QueryWrapper<Permission> wrapper = new QueryWrapper<>();
        wrapper.eq("api_path", apiPath);
        wrapper.eq("method", method);
        return permissionMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean deletePermissionsByIds(List<Long> permissionIds) {
        return permissionMapper.deleteBatchIds(permissionIds) > 0;
    }

    @Override
    public List<Permission> getPermissionsByRoleId(Long roleId) {
        QueryWrapper<RolePermission> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId);
        List<RolePermission> rolePermissions = rolePermissionMapper.selectList(wrapper);

        if (rolePermissions.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> permissionIds = rolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .collect(Collectors.toList());

        return permissionMapper.selectBatchIds(permissionIds);
    }

    @Override
    public List<Permission> getPermissionsByUserId(Long userId) {
        // 获取用户角色
        QueryWrapper<UserRole> userRoleWrapper = new QueryWrapper<>();
        userRoleWrapper.eq("user_id", userId);
        List<UserRole> userRoles = userRoleMapper.selectList(userRoleWrapper);

        if (userRoles.isEmpty()) {
            return new ArrayList<>();
        }

        List<Integer> roleIds = userRoles.stream()
                .map(UserRole::getRoleId)
                .collect(Collectors.toList());

        // 获取角色权限
        QueryWrapper<RolePermission> rolePermWrapper = new QueryWrapper<>();
        rolePermWrapper.in("role_id", roleIds);
        List<RolePermission> rolePermissions = rolePermissionMapper.selectList(rolePermWrapper);

        if (rolePermissions.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> permissionIds = rolePermissions.stream()
                .map(RolePermission::getPermissionId)
                .distinct()
                .collect(Collectors.toList());

        return permissionMapper.selectBatchIds(permissionIds);
    }
}




