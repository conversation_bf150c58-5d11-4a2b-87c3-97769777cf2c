"use strict";
const utils_api = require("../../utils/api.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      apiResponse: "",
      friendList: []
    };
  },
  methods: {
    async testFriendListAPI() {
      try {
        const result = await utils_api.getFriendList();
        this.apiResponse = JSON.stringify(result, null, 2);
        if (result.code === 200) {
          this.friendList = result.data || [];
        }
      } catch (error) {
        this.apiResponse = `错误: ${error.message}`;
      }
    },
    getDisplayName(friend) {
      if (friend.remark && friend.remark.trim()) {
        return friend.remark;
      }
      return friend.nickname || friend.username || "未知用户";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.testFriendListAPI && $options.testFriendListAPI(...args)),
    b: common_vendor.t($data.apiResponse),
    c: common_vendor.f($data.friendList, (friend, k0, i0) => {
      return {
        a: common_vendor.t(friend.userId),
        b: common_vendor.t(friend.username),
        c: common_vendor.t(friend.nickname),
        d: common_vendor.t(friend.remark || "无"),
        e: common_vendor.t($options.getDisplayName(friend)),
        f: friend.userId
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-10e4c8e3"], ["__file", "E:/wolk/study/uniappIm/src/pages/debug/debug.vue"]]);
wx.createPage(MiniProgramPage);
