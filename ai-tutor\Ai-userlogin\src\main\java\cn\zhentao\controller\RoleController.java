package cn.zhentao.controller;

import cn.zhentao.pojo.Role;
import cn.zhentao.pojo.Permission;
import cn.zhentao.service.RoleService;
import cn.zhentao.service.PermissionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/role")
public class RoleController {
    @Autowired
    private RoleService roleService;
    
    @Autowired
    private PermissionService permissionService;

    @GetMapping("/list")
    public Map<String, Object> getRoleList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String status) {
        
        Page<Role> page = new Page<>(pageNum, pageSize);
        IPage<Role> rolePage = roleService.getRolePage(page, roleName, status);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", rolePage);
        return result;
    }

    @GetMapping("/{roleId}")
    public Map<String, Object> getRoleById(@PathVariable Long roleId) {
        Role role = roleService.getById(roleId);
        Map<String, Object> result = new HashMap<>();
        if (role == null) {
            result.put("code", 404);
            result.put("message", "角色不存在");
        } else {
            result.put("code", 200);
            result.put("data", role);
        }
        return result;
    }

    @PostMapping
    public Map<String, Object> addRole(@RequestBody Role role) {
        boolean success = roleService.save(role);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "添加成功");
        } else {
            result.put("code", 500);
            result.put("message", "添加失败");
        }
        return result;
    }

    @PutMapping
    public Map<String, Object> updateRole(@RequestBody Role role) {
        boolean success = roleService.updateById(role);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "修改成功");
        } else {
            result.put("code", 500);
            result.put("message", "修改失败");
        }
        return result;
    }

    @DeleteMapping("/{roleId}")
    public Map<String, Object> deleteRole(@PathVariable Long roleId) {
        boolean success = roleService.removeById(roleId);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "删除成功");
        } else {
            result.put("code", 500);
            result.put("message", "删除失败");
        }
        return result;
    }

    @GetMapping("/{roleId}/permissions")
    public Map<String, Object> getRolePermissions(@PathVariable Long roleId) {
        List<Permission> permissions = roleService.getRolePermissions(roleId);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", permissions);
        return result;
    }

    @PutMapping("/{roleId}/permissions")
    public Map<String, Object> assignPermissionsToRole(@PathVariable Long roleId, @RequestBody List<Long> permissionIds) {
        boolean success = roleService.assignPermissionsToRole(roleId, permissionIds);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "权限分配成功");
        } else {
            result.put("code", 500);
            result.put("message", "权限分配失败");
        }
        return result;
    }

    @GetMapping("/enabled")
    public Map<String, Object> getEnabledRoles() {
        List<Role> roles = roleService.getEnabledRoles();
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", roles);
        return result;
    }
}
