<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhentao.mapper.PermissionMapper">

    <resultMap id="BaseResultMap" type="cn.zhentao.pojo.Permission">
            <id property="permissionId" column="permission_id" jdbcType="BIGINT"/>
            <result property="permissionCode" column="permission_code" jdbcType="VARCHAR"/>
            <result property="permissionName" column="permission_name" jdbcType="VARCHAR"/>
            <result property="method" column="method" jdbcType="VARCHAR"/>
            <result property="apiPath" column="api_path" jdbcType="VARCHAR"/>
            <result property="menuId" column="menu_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        permission_id,permission_code,permission_name,
        method,api_path,menu_id
    </sql>

</mapper>
