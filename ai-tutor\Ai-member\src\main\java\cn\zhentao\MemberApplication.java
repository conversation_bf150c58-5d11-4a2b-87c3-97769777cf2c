package cn.zhentao;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * AI会员服务启动类
 */
@SpringBootApplication
@MapperScan("cn.zhentao.mapper")
@EnableTransactionManagement
@EnableConfigurationProperties
public class MemberApplication {

    public static void main(String[] args) {
        SpringApplication.run(MemberApplication.class, args);
        System.out.println("AI Member Service started successfully!");
    }
}
