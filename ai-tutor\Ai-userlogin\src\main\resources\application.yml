# Spring配置
spring:
  # 应用名称
  application:
    name: ai-userlogin

  # 激活的配置文件
  profiles:
    active: dev

  # 数据库配置 - 强制使用MySQL
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://**************:3306/tutor?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: root
    password: Sunshuo0818
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  aliyun:
    sms:
      host: "https://gyytz.market.alicloudapi.com"
      path: "/sms/smsSend"
      appcode: "b01a78ea776f451091a4e9ef78ff047b"
      smsSignId: "2e65b1bb3d054466b82f0c9d125465e2"
      templateId: "908e94ccf08b4476ba6c876d13f084ad"
  # 数据库初始化
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: true
      platform: mysql
  # 明确禁用H2控制台和自动配置
  h2:
    console:
      enabled: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration

  # Redis配置（开发环境禁用）
  # redis:
  #   host: 127.0.0.1
  #   port: 6379
  #   password:
  #   database: 0
  #   timeout: 3000ms
  #   lettuce:
  #     pool:
  #       max-active: 8
  #       max-idle: 8
  #       min-idle: 0
  #       max-wait: -1ms

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

# 应用配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Spring Security配置
security:
  oauth2:
    resource:
      jwt:
        key-value: ${jwt.secret}

# RBAC权限配置
rbac:
  # 超级管理员角色
  super-admin-role: SUPER_ADMIN
  # 默认角色
  default-role: USER
  # 权限缓存时间（秒）
  permission-cache-time: 3600
  # 角色缓存时间（秒）
  role-cache-time: 3600

# 人脸识别详细配置
face-recognition:
  # 人脸检测模型配置
  detection:
    cascade-file: haarcascade_frontalface_alt.xml
    scale-factor: 1.1
    min-neighbors: 3
    min-size: 30
  # 人脸识别模型配置
  recognition:
    algorithm: LBPH  # 可选: EIGEN, FISHER, LBPH
    threshold: 80.0
    radius: 1
    neighbors: 8
    grid-x: 8
    grid-y: 8
  # 图像处理配置
  image:
    max-width: 800
    max-height: 600
    quality: 0.8
    format: jpg
  # 训练数据配置
  training:
    min-samples: 5
    max-samples: 20
    auto-retrain: true
    retrain-threshold: 0.1



# 跨域配置
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:8080
    - http://127.0.0.1:3000
    - http://127.0.0.1:8080
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true
  max-age: 3600

# 服务器配置
server:
  port: 8083

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      # logic-delete-field: deleted  # 临时禁用逻辑删除
      # logic-delete-value: 1
      # logic-not-delete-value: 0
      table-underline: true
    banner: false
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: cn.zhentao.pojo
  check-config-location: false

# JWT配置
jwt:
  # JWT加解密使用的密钥
  secret: abcdefghijklmnopqrstuvwxyz0123456789
  # JWT的超期限时间(60*60*24*7)
  expiration: 604800000
  header: Authorization
  prefix: Bearer

# 日志配置
logging:
  level:
    cn.zhentao: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
