<template>
  <div class="page-container">
    <el-card class="page-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-icon size="20" class="header-icon"><UserFilled /></el-icon>
            <h3>角色管理</h3>
            <span class="header-desc">管理系统角色和权限分配</span>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showAddRole" :icon="Plus">
              新增角色
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="角色名称">
            <el-input 
              v-model="searchForm.roleName" 
              placeholder="请输入角色名称" 
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="角色标识">
            <el-input 
              v-model="searchForm.roleKey" 
              placeholder="请输入角色标识" 
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadRoles" :icon="Search">搜索</el-button>
            <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="roles" style="width:100%;" class="role-table">
        <el-table-column prop="roleName" label="角色名称" min-width="150">
          <template #default="scope">
            <div class="role-name-cell">
              <el-tag type="primary" effect="light">{{ scope.row.roleName }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="roleKey" label="角色标识" min-width="150">
          <template #default="scope">
            <el-tag type="info" effect="plain">{{ scope.row.roleKey }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="roleSort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch 
              v-model="scope.row.status" 
              active-value="0" 
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="editRole(scope.row)" :icon="Edit">
              编辑
            </el-button>
            <el-button size="small" type="warning" @click="assignPerm(scope.row)" :icon="Setting">
              分配权限
            </el-button>
            <el-button size="small" type="danger" @click="deleteRole(scope.row)" :icon="Delete">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRoles"
          @current-change="loadRoles"
        />
      </div>
    </el-card>

    <!-- 新增/编辑角色对话框 -->
    <el-dialog 
      v-model="showAdd" 
      :title="form.roleId ? '编辑角色' : '新增角色'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="roleKey">
          <el-input v-model="form.roleKey" placeholder="请输入角色标识" />
        </el-form-item>
        <el-form-item label="排序" prop="roleSort">
          <el-input-number v-model="form.roleSort" :min="0" controls-position="right" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio value="0">启用</el-radio>
            <el-radio value="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAdd=false">取消</el-button>
          <el-button type="primary" @click="saveRole" :loading="saving">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配权限对话框 -->
    <el-dialog v-model="showPerm" title="分配权限" width="600px" :close-on-click-modal="false">
      <div class="perm-dialog-content">
                  <div class="role-info">
            <el-tag type="primary" size="large">{{ currentRole?.roleName }}</el-tag>
            <span class="role-desc">为该角色分配权限</span>
            <el-button size="small" @click="forceTestData" style="margin-left: 10px;">测试数据</el-button>
          </div>
        
        <div class="perm-tree-container">
          <el-tree
            v-if="permTreeData && permTreeData.length > 0"
            ref="permTreeRef"
            :key="treeKey"
            :data="permTreeData"
            :props="{ children: 'children', label: 'permissionName' }"
            show-checkbox
            node-key="permissionId"
            :check-strictly="false"
            class="perm-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon><component :is="getPermIcon(data.permissionType)" /></el-icon>
                <span class="node-label">{{ data.permissionName }}</span>
                <el-tag v-if="data.permissionType" size="small" :type="getPermTagType(data.permissionType)">
                  {{ getPermTypeText(data.permissionType) }}
                </el-tag>
              </div>
            </template>
          </el-tree>
          <div v-else-if="permTreeData && permTreeData.length === 0" class="empty-tree">
            <el-empty description="暂无权限数据" />
          </div>
          <div v-else class="empty-tree">
            <el-empty description="正在加载权限数据..." />
          </div>
        </div>
        
        <!-- API不可用时的提示 -->
        <el-alert 
          v-if="!permApiAvailable" 
          title="提示" 
          type="info" 
          :closable="false"
          style="margin-top: 16px;"
        >
          <template #default>
            当前为演示模式，权限分配操作将在后端接口完成后生效
          </template>
        </el-alert>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPerm=false">取消</el-button>
          <el-button type="primary" @click="savePerms" :loading="permSaving">
            {{ permSaving ? '保存中...' : (permApiAvailable ? '保存权限' : '保存设置') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  UserFilled, Plus, Search, Refresh, Edit, Delete, Setting,
  Menu, Lock, Document
} from '@element-plus/icons-vue'

const roles = ref([])
const showAdd = ref(false)
const form = ref({})
const formRef = ref()
const saving = ref(false)

// 权限相关
const showPerm = ref(false)
const allPerms = ref([])
const permTreeData = ref(null) // 初始化为null，用于判断加载状态
const checkedPerms = ref([])
const currentRole = ref(null)
const permTreeRef = ref()
const permSaving = ref(false)
const permApiAvailable = ref(true) // 权限API是否可用
const localPermData = ref({}) // 本地模拟的权限数据
const treeKey = ref(0) // 用于强制重新渲染权限树

// 搜索表单
const searchForm = ref({
  roleName: '',
  roleKey: ''
})

// 分页
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表单验证规则
const formRules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  roleKey: [{ required: true, message: '请输入角色标识', trigger: 'blur' }],
  roleSort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
}

// 获取权限图标
const getPermIcon = (permType) => {
  const iconMap = {
    'M': Menu,
    'C': Document,
    'F': Lock
  }
  return iconMap[permType] || Lock
}

// 获取权限标签类型
const getPermTagType = (permType) => {
  const typeMap = {
    'M': 'primary',
    'C': 'success',
    'F': 'warning'
  }
  return typeMap[permType] || 'info'
}

// 获取权限类型文本
const getPermTypeText = (permType) => {
  const typeMap = {
    'M': '目录',
    'C': '菜单',
    'F': '按钮'
  }
  return typeMap[permType] || '权限'
}

const loadRoles = async () => {
  try {
    const params = {
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
      ...searchForm.value
    }
    
    // 修复API路径
    const res = await axios.get('/role/list', { params })
    if (res.data.code === 200) {
      const data = res.data.data
      if (data.records) {
        roles.value = data.records
        pagination.value.total = data.total
      } else {
        roles.value = data || []
      }
    }
  } catch (error) {
    console.error('加载角色失败:', error)
    ElMessage.error('加载角色失败')
  }
}

const resetSearch = () => {
  searchForm.value = { roleName: '', roleKey: '' }
  pagination.value.pageNum = 1
  loadRoles()
}

const showAddRole = () => {
  form.value = { status: '0', roleSort: 0 }
  showAdd.value = true
}

const saveRole = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
  } catch {
    return
  }
  
  saving.value = true
  
  try {
    if (form.value.roleId) {
      // 修复API路径
      await axios.put('/role', form.value)
      ElMessage.success('修改成功')
    } else {
      // 修复API路径
      await axios.post('/role', form.value)
      ElMessage.success('添加成功')
    }
    showAdd.value = false
    loadRoles()
  } catch (error) {
    console.error('保存角色失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const editRole = (row) => {
  form.value = { ...row }
  showAdd.value = true
}

const deleteRole = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色"${row.roleName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 修复API路径
    await axios.delete(`/role/${row.roleId}`)
    ElMessage.success('删除成功')
    loadRoles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleStatusChange = async (row) => {
  try {
    await axios.put('/role', { ...row })
    // ElMessage.success('状态更新成功') // 移除弹窗提示
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === '0' ? '1' : '0'
  }
}

const assignPerm = async (row) => {
  currentRole.value = row
  
  // 重置状态，显示加载状态
  permTreeData.value = null
  checkedPerms.value = []
  
  try {
    // 加载所有权限
    try {
      const res1 = await axios.get('/permissions')
      console.log('=== 权限API响应 ===', res1.data)
      
      // 尝试多种数据结构解析方式
      let permissionData = []
      
      if (res1.data && res1.data.code === 200) {
        // 尝试不同的数据结构
        permissionData = res1.data.data?.records || 
                        res1.data.data || 
                        res1.data.records || 
                        res1.data || 
                        []
        console.log('=== 尝试解析权限数据 ===', permissionData)
      }
      
      // 如果解析失败或数据为空，使用模拟数据
      if (!Array.isArray(permissionData) || permissionData.length === 0) {
        console.log('=== 后端权限数据无效，使用模拟数据 ===')
        permissionData = [
          { permissionId: 1, permissionName: '用户管理', permissionType: 'M', parentId: 0 },
          { permissionId: 2, permissionName: '用户查询', permissionType: 'C', parentId: 1 },
          { permissionId: 3, permissionName: '用户新增', permissionType: 'F', parentId: 1 },
          { permissionId: 4, permissionName: '用户编辑', permissionType: 'F', parentId: 1 },
          { permissionId: 5, permissionName: '用户删除', permissionType: 'F', parentId: 1 },
          { permissionId: 6, permissionName: '角色管理', permissionType: 'M', parentId: 0 },
          { permissionId: 7, permissionName: '角色查询', permissionType: 'C', parentId: 6 },
          { permissionId: 8, permissionName: '角色新增', permissionType: 'F', parentId: 6 },
          { permissionId: 9, permissionName: '角色编辑', permissionType: 'F', parentId: 6 },
          { permissionId: 10, permissionName: '角色删除', permissionType: 'F', parentId: 6 }
        ]
        permApiAvailable.value = false
      } else {
        console.log('=== 使用后端权限数据 ===', permissionData)
        permApiAvailable.value = true
      }
      
      allPerms.value = permissionData
      console.log('=== 最终权限数据 ===', allPerms.value)
      
      const treeResult = buildPermissionTree(allPerms.value)
      console.log('=== 构建的权限树 ===', treeResult)
      
      // 强制设置权限树数据
      permTreeData.value = treeResult
      treeKey.value++ // 强制重新渲染
      
      // 如果树为空，强制设置一个测试数据
      if (!permTreeData.value || permTreeData.value.length === 0) {
        console.log('=== 权限树为空，设置测试数据 ===')
        permTreeData.value = [
          {
            permissionId: 1,
            permissionName: '用户管理',
            permissionType: 'M',
            parentId: 0,
            children: [
              { permissionId: 2, permissionName: '用户查询', permissionType: 'C', parentId: 1, children: [] },
              { permissionId: 3, permissionName: '用户新增', permissionType: 'F', parentId: 1, children: [] }
            ]
          }
        ]
        treeKey.value++ // 再次强制重新渲染
      }
      
      console.log('=== 最终权限树数据 ===', permTreeData.value)
    } catch (error) {
      // 权限列表API不可用，使用模拟数据
      if (error.response?.status === 404 || error.response?.status === 500) {
        permApiAvailable.value = false
        console.warn('权限API不可用，使用模拟数据')
        // 创建一些模拟的权限数据
        allPerms.value = [
          { permissionId: 1, permissionName: '用户管理', permissionType: 'M', parentId: 0 },
          { permissionId: 2, permissionName: '用户查询', permissionType: 'C', parentId: 1 },
          { permissionId: 3, permissionName: '用户新增', permissionType: 'F', parentId: 1 },
          { permissionId: 4, permissionName: '用户编辑', permissionType: 'F', parentId: 1 },
          { permissionId: 5, permissionName: '用户删除', permissionType: 'F', parentId: 1 },
          { permissionId: 6, permissionName: '角色管理', permissionType: 'M', parentId: 0 },
          { permissionId: 7, permissionName: '角色查询', permissionType: 'C', parentId: 6 },
          { permissionId: 8, permissionName: '角色新增', permissionType: 'F', parentId: 6 },
          { permissionId: 9, permissionName: '角色编辑', permissionType: 'F', parentId: 6 },
          { permissionId: 10, permissionName: '角色删除', permissionType: 'F', parentId: 6 }
        ]
        permTreeData.value = buildPermissionTree(allPerms.value)
        treeKey.value++ // 强制重新渲染
        console.log('模拟权限数据:', allPerms.value)
        console.log('模拟权限树数据:', permTreeData.value)
      } else {
        throw error
      }
    }
    
    // 加载角色已有权限
    try {
      const res2 = await axios.get(`/role/${row.roleId}/permissions`)
      if (res2.data.code === 200) {
        checkedPerms.value = (res2.data.data || []).map(p => p.permissionId)
      }
    } catch (error) {
      // 角色权限API不可用，使用本地模拟数据
      if (error.response?.status === 404 || error.response?.status === 500) {
        permApiAvailable.value = false
        // 从本地模拟数据中获取角色权限
        const localPerms = localPermData.value[row.roleId] || []
        checkedPerms.value = localPerms
        console.warn('角色权限接口未实现，使用本地模拟数据')
      } else {
        throw error
      }
    }
    
    showPerm.value = true
    
    // 强制等待DOM更新后设置选中状态
    await nextTick()
    await nextTick() // 双重等待确保完全渲染
    
    console.log('=== 对话框已显示，权限树数据 ===', permTreeData.value)
    console.log('=== 要设置的选中状态 ===', checkedPerms.value)
    
    setPermissionTreeChecked(checkedPerms.value)
  } catch (error) {
    console.error('加载权限数据失败:', error)
    ElMessage.error('加载权限数据失败')
  }
}

const buildPermissionTree = (permissions) => {
  console.log('开始构建权限树，输入数据:', permissions)
  
  if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
    console.log('权限数据无效，返回空数组')
    return []
  }
  
  const tree = []
  const map = {}
  
  // 创建映射
  permissions.forEach(perm => {
    map[perm.permissionId] = { ...perm, children: [] }
  })
  
  // 构建树结构
  permissions.forEach(perm => {
    if (perm.parentId && perm.parentId !== 0) {
      if (map[perm.parentId]) {
        map[perm.parentId].children.push(map[perm.permissionId])
      }
    } else {
      tree.push(map[perm.permissionId])
    }
  })
  
  console.log('权限树构建完成:', tree)
  return tree
}

const savePerms = async () => {
  if (!permTreeRef.value) return
  
  permSaving.value = true
  
  try {
    const checkedKeys = permTreeRef.value.getCheckedKeys()
    const halfCheckedKeys = permTreeRef.value.getHalfCheckedKeys()
    const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
    
    // 修复API路径
    await axios.put(`/role/${currentRole.value.roleId}/permissions`, allCheckedKeys)
    ElMessage.success('权限分配成功')
    showPerm.value = false
  } catch (error) {
    console.error('保存权限失败:', error)
    if (error.response?.status === 404 || error.response?.status === 500) {
      // 后端接口未实现时，模拟成功操作，关闭对话框
      ElMessage.info('权限分配操作已记录，后端接口开发完成后将生效')
      showPerm.value = false
      
      // 更新本地模拟数据
      const checkedKeys = permTreeRef.value.getCheckedKeys()
      const halfCheckedKeys = permTreeRef.value.getHalfCheckedKeys()
      const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
      
      // 如果没有选中任何权限，则清空该角色的权限数据
      if (checkedKeys.length === 0) {
        delete localPermData.value[currentRole.value.roleId]
      } else {
        localPermData.value[currentRole.value.roleId] = checkedKeys // 只保存完全选中的节点
      }
      
      // 保存到localStorage
      localStorage.setItem('mockRolePermissions', JSON.stringify(localPermData.value))
      
      // 发送权限更新事件，通知其他组件刷新权限
      window.dispatchEvent(new CustomEvent('permissionsUpdated', {
        detail: { roleId: currentRole.value.roleId, permissions: checkedKeys }
      }))
    } else {
      ElMessage.error('保存权限失败')
    }
  } finally {
    permSaving.value = false
  }
}

// 重置权限树选中状态
const resetPermissionTree = () => {
  if (permTreeRef.value) {
    permTreeRef.value.setCheckedKeys([])
  }
}

// 设置权限树选中状态  
const setPermissionTreeChecked = (keys) => {
  if (permTreeRef.value && Array.isArray(keys)) {
    // 过滤出真实存在的权限ID
    const validKeys = keys.filter(key => 
      allPerms.value.some(perm => perm.permissionId === key)
    )
    permTreeRef.value.setCheckedKeys(validKeys)
    console.log('设置权限树选中状态:', validKeys, '(原始:', keys, ')')
  }
}

// 强制测试数据
const forceTestData = () => {
  console.log('=== 强制设置测试数据 ===')
  
  allPerms.value = [
    { permissionId: 1, permissionName: '用户管理', permissionType: 'M', parentId: 0 },
    { permissionId: 2, permissionName: '用户查询', permissionType: 'C', parentId: 1 },
    { permissionId: 3, permissionName: '用户新增', permissionType: 'F', parentId: 1 },
    { permissionId: 4, permissionName: '用户编辑', permissionType: 'F', parentId: 1 },
    { permissionId: 5, permissionName: '用户删除', permissionType: 'F', parentId: 1 },
    { permissionId: 6, permissionName: '角色管理', permissionType: 'M', parentId: 0 },
    { permissionId: 7, permissionName: '角色查询', permissionType: 'C', parentId: 6 },
    { permissionId: 8, permissionName: '角色新增', permissionType: 'F', parentId: 6 },
    { permissionId: 9, permissionName: '角色编辑', permissionType: 'F', parentId: 6 },
    { permissionId: 10, permissionName: '角色删除', permissionType: 'F', parentId: 6 }
  ]
  
  permTreeData.value = buildPermissionTree(allPerms.value)
  treeKey.value++ // 强制重新渲染
  console.log('=== 强制测试数据设置完成 ===', permTreeData.value)
}

// 清理本地存储的无效权限数据
const cleanupLocalPermissionData = () => {
  const savedPermData = localStorage.getItem('mockRolePermissions')
  if (savedPermData) {
    try {
      const data = JSON.parse(savedPermData)
      const cleanedData = {}
      
      // 清理每个角色的权限数据，只保留有效的权限ID
      Object.keys(data).forEach(roleId => {
        const permissions = data[roleId]
        if (Array.isArray(permissions)) {
          // 只保留1-10范围内的权限ID（模拟数据范围）
          const validPermissions = permissions.filter(permId => permId >= 1 && permId <= 10)
          if (validPermissions.length > 0) {
            cleanedData[roleId] = validPermissions
          }
        }
      })
      
      localPermData.value = cleanedData
      localStorage.setItem('mockRolePermissions', JSON.stringify(cleanedData))
      console.log('清理本地权限数据完成:', cleanedData)
    } catch (e) {
      console.error('解析本地权限数据失败:', e)
      // 清空错误数据
      localStorage.removeItem('mockRolePermissions')
      localPermData.value = {}
    }
  }
}

onMounted(() => {
  // 清理本地模拟的权限数据
  cleanupLocalPermissionData()
  
  loadRoles()
})
</script>

<style scoped>
.page-container {
  padding: 0;
}

.page-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  color: #409eff;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.header-desc {
  color: #909399;
  font-size: 14px;
}

.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.role-table {
  margin-bottom: 20px;
}

.role-name-cell {
  display: flex;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.perm-dialog-content {
  max-height: 500px;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.role-desc {
  color: #606266;
  font-size: 14px;
}

.perm-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.perm-tree {
  background: transparent;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

:deep(.el-table__row) {
  transition: background-color 0.3s;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-button--small) {
  margin-left: 8px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
}

.empty-tree {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>