"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_websocket = require("../../utils/websocket.js");
const _sfc_main = {
  data() {
    return {
      userId: null,
      nickname: "",
      userInfo: null,
      messages: [],
      inputMessage: "",
      scrollTop: 0,
      sending: false,
      showEmojiPanel: false,
      showMorePanel: false,
      showChatMenuModal: false,
      hasMoreMessages: false,
      loadingMore: false,
      currentPage: 1,
      pageSize: 20,
      lastMessageTime: null,
      isConnected: false,
      messageListHeight: 600,
      isRecordingVoice: false,
      scrollTimer: null,
      // 滚动防抖定时器
      renderTimer: null,
      // 渲染节流定时器
      emojiList: [
        "😀",
        "😃",
        "😄",
        "😁",
        "😆",
        "😅",
        "😂",
        "🤣",
        "😊",
        "😇",
        "🙂",
        "🙃",
        "😉",
        "😌",
        "😍",
        "🥰",
        "😘",
        "😗",
        "😙",
        "😚",
        "😋",
        "😛",
        "😝",
        "😜",
        "🤪",
        "🤨",
        "🧐",
        "🤓",
        "😎",
        "🤩",
        "🥳",
        "😏",
        "😒",
        "😞",
        "😔",
        "😟",
        "😕",
        "🙁",
        "☹️",
        "😣",
        "😖",
        "😫",
        "😩",
        "🥺",
        "😢",
        "😭",
        "😤",
        "😠",
        "😡",
        "🤬",
        "🤯",
        "😳",
        "🥵",
        "🥶",
        "😱",
        "😨",
        "😰",
        "😥",
        "😓",
        "🤗",
        "🤔",
        "🤭",
        "🤫",
        "🤥",
        "😶",
        "😐",
        "😑",
        "😬",
        "🙄",
        "😯",
        "😦",
        "😧",
        "😮",
        "😲",
        "🥱",
        "😴",
        "🤤",
        "😪",
        "😵",
        "🤐",
        "🥴",
        "🤢",
        "🤮",
        "🤧",
        "😷",
        "🤒",
        "🤕",
        "🤑",
        "🤠",
        "😈",
        "👿",
        "👹",
        "👺",
        "🤡",
        "💩",
        "👻",
        "💀",
        "☠️",
        "👽",
        "👾",
        "🤖",
        "🎃",
        "😺",
        "😸",
        "😹",
        "😻",
        "😼",
        "😽",
        "🙀",
        "😿",
        "😾",
        "👋",
        "🤚",
        "🖐️",
        "✋",
        "🖖",
        "👌",
        "🤏",
        "✌️",
        "🤞",
        "🤟",
        "🤘",
        "🤙",
        "👈",
        "👉",
        "👆",
        "🖕",
        "👇",
        "☝️",
        "👍",
        "👎",
        "👊",
        "✊",
        "🤛",
        "🤜",
        "👏",
        "🙌",
        "👐",
        "🤲",
        "🤝",
        "🙏",
        "✍️",
        "💅",
        "🤳",
        "❤️",
        "🧡",
        "💛",
        "💚",
        "💙",
        "💜",
        "🖤",
        "🤍",
        "🤎",
        "💔",
        "❣️",
        "💕",
        "💞",
        "💓",
        "💗",
        "💖",
        "💘",
        "💝",
        "💟",
        "☮️",
        "✝️",
        "☪️",
        "🕉️",
        "☸️",
        "✡️",
        "🔯",
        "🕎",
        "☯️",
        "☦️",
        "🛐",
        "⛎",
        "♈",
        "♉",
        "♊",
        "♋",
        "♌",
        "♍",
        "♎",
        "♏",
        "♐",
        "♑",
        "♒",
        "♓",
        "🆔",
        "⚛️",
        "🉑",
        "☢️",
        "☣️"
      ]
    };
  },
  computed: {
    // 计算消息列表高度
    computedMessageListHeight() {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        const headerHeight = 88;
        const inputAreaHeight = this.showEmojiPanel || this.showMorePanel ? 400 : 100;
        const statusBarHeight = systemInfo.statusBarHeight || 0;
        return systemInfo.windowHeight - headerHeight - inputAreaHeight - statusBarHeight;
      } catch (error) {
        console.error("计算高度失败:", error);
        return 600;
      }
    }
  },
  onLoad(options) {
    this.userId = parseInt(options.userId);
    this.nickname = decodeURIComponent(options.nickname || "未知用户");
    this.userInfo = common_vendor.index.getStorageSync("userInfo");
    console.log("聊天详情页面参数:", {
      userId: this.userId,
      nickname: this.nickname,
      userInfo: this.userInfo
    });
    common_vendor.index.setNavigationBarTitle({
      title: this.nickname
    });
    this.calculateMessageListHeight();
    this.loadChatHistory();
    this.initWebSocket();
  },
  onUnload() {
    utils_websocket.closeWebSocket();
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }
    if (this.renderTimer) {
      clearTimeout(this.renderTimer);
      this.renderTimer = null;
    }
  },
  onReady() {
    this.calculateMessageListHeight();
  },
  methods: {
    /**
     * 加载聊天历史记录
     */
    async loadChatHistory() {
      try {
        const result = await utils_api.getChatHistory(this.userId);
        if (result.code === 200) {
          const newMessages = result.data || [];
          this.messages = newMessages.sort((a, b) => new Date(a.sendTime) - new Date(b.sendTime));
          this.scrollToBottom();
          this.markAsRead();
        } else {
          console.error("加载聊天记录失败:", result.message);
          common_vendor.index.showToast({
            title: result.message || "加载失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("加载聊天记录失败:", error);
        common_vendor.index.showToast({
          title: "网络错误",
          icon: "none"
        });
      }
    },
    /**
     * 加载更多消息（暂时禁用分页）
     */
    async loadMoreMessages() {
      return;
    },
    initWebSocket() {
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        console.error("未找到token，无法连接WebSocket");
        this.isConnected = true;
        return;
      }
      try {
        utils_websocket.connectWebSocket(token, (message) => {
          if (message.type === "chat") {
            if (message.fromUserId !== this.userInfo.userId) {
              const existingMessage = this.messages.find(
                (m) => m.id === message.messageId || m.content === message.content && m.fromUserId === message.fromUserId && Math.abs(new Date(m.sendTime) - new Date(message.sendTime)) < 1e3
              );
              if (!existingMessage) {
                this.messages.push({
                  id: message.messageId || Date.now(),
                  fromUserId: message.fromUserId,
                  toUserId: message.toUserId,
                  content: message.content,
                  sendTime: message.sendTime || (/* @__PURE__ */ new Date()).toISOString()
                });
                if (this.renderTimer) {
                  clearTimeout(this.renderTimer);
                }
                this.renderTimer = setTimeout(() => {
                  this.scrollToBottom();
                }, 16);
                this.markAsRead();
              }
            }
          } else if (message.type === "auth_success") {
            this.isConnected = true;
          }
        });
        this.isConnected = true;
      } catch (error) {
        console.error("WebSocket连接失败:", error);
        this.isConnected = true;
      }
    },
    /**
     * 重新连接WebSocket
     */
    reconnectWebSocket() {
      this.isConnected = false;
      common_vendor.index.showLoading({
        title: "连接中..."
      });
      setTimeout(() => {
        this.initWebSocket();
        common_vendor.index.hideLoading();
      }, 1e3);
    },
    /**
     * 发送消息
     */
    async sendMessage() {
      if (!this.inputMessage.trim() || this.sending) {
        return;
      }
      const messageContent = this.inputMessage.trim();
      const tempId = Date.now();
      const tempMessage = {
        id: tempId,
        fromUserId: this.userInfo.userId,
        toUserId: this.userId,
        content: messageContent,
        sendTime: (/* @__PURE__ */ new Date()).toISOString(),
        status: "sending"
      };
      this.messages.push(tempMessage);
      this.inputMessage = "";
      this.sending = true;
      this.hideEmojiPanel();
      this.hideMorePanel();
      this.scrollToBottom();
      try {
        const result = await this.sendMessageAPI({
          toUserId: this.userId,
          content: messageContent
        });
        if (result.code === 200) {
          const messageIndex = this.messages.findIndex((m) => m.id === tempId);
          if (messageIndex > -1) {
            this.messages[messageIndex].id = result.data.id;
            this.messages[messageIndex].status = "sent";
            this.messages[messageIndex].sendTime = result.data.sendTime;
          }
        } else {
          throw new Error(result.message || "发送失败");
        }
      } catch (error) {
        console.error("发送消息失败:", error);
        const messageIndex = this.messages.findIndex((m) => m.id === tempId);
        if (messageIndex > -1) {
          this.messages[messageIndex].status = "failed";
        }
        common_vendor.index.showToast({
          title: error.message || "发送失败",
          icon: "none"
        });
      } finally {
        this.sending = false;
      }
    },
    /**
     * 格式化时间（简短）
     */
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      const time = new Date(timeStr);
      return time.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false
      });
    },
    /**
     * 格式化详细时间（用于时间分割线）
     */
    formatDetailTime(timeStr) {
      if (!timeStr)
        return "";
      const time = new Date(timeStr);
      const now = /* @__PURE__ */ new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1e3);
      if (time >= today) {
        return "今天 " + time.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false
        });
      } else if (time >= yesterday) {
        return "昨天 " + time.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false
        });
      } else {
        return time.toLocaleDateString("zh-CN") + " " + time.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false
        });
      }
    },
    /**
     * 判断是否显示时间分割线
     */
    shouldShowTime(message) {
      const messageIndex = this.messages.findIndex((m) => m.id === message.id);
      if (messageIndex === 0)
        return true;
      const prevMessage = this.messages[messageIndex - 1];
      if (!prevMessage)
        return true;
      const currentTime = new Date(message.sendTime);
      const prevTime = new Date(prevMessage.sendTime);
      return currentTime - prevTime > 10 * 60 * 1e3;
    },
    scrollToBottom() {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      this.scrollTimer = setTimeout(() => {
        this.$nextTick(() => {
          const query = common_vendor.index.createSelectorQuery().in(this);
          query.select(".message-list").scrollOffset((res) => {
            if (res) {
              this.scrollTop = res.scrollHeight;
            } else {
              this.scrollTop = 999999;
            }
          }).exec();
        });
      }, 16);
    },
    /**
     * 标记会话为已读
     */
    async markAsRead() {
      try {
        await utils_api.markSessionAsRead(this.userId);
        common_vendor.index.$emit("clearUnreadCount", {
          userId: this.userId
        });
      } catch (error) {
        console.error("标记会话已读失败:", error);
      }
    },
    /**
     * 切换表情面板
     */
    toggleEmojiPanel() {
      this.showEmojiPanel = !this.showEmojiPanel;
    },
    /**
     * 隐藏表情面板
     */
    hideEmojiPanel() {
      this.showEmojiPanel = false;
    },
    /**
     * 隐藏更多面板
     */
    hideMorePanel() {
      this.showMorePanel = false;
    },
    /**
     * 发送消息API调用
     */
    async sendMessageAPI(data) {
      return await utils_api.sendMessage(data);
    },
    /**
     * 插入表情
     */
    insertEmoji(emoji) {
      this.inputMessage += emoji;
    },
    /**
     * 返回上一页
     */
    goBack() {
      common_vendor.index.navigateBack();
    },
    /**
     * 显示聊天菜单
     */
    showChatMenu() {
      this.showChatMenuModal = true;
    },
    /**
     * 隐藏聊天菜单
     */
    hideChatMenu() {
      this.showChatMenuModal = false;
    },
    /**
     * 计算消息列表高度
     */
    calculateMessageListHeight() {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        const headerHeight = 88;
        const inputAreaHeight = 100;
        const statusBarHeight = systemInfo.statusBarHeight || 0;
        this.messageListHeight = systemInfo.windowHeight - headerHeight - inputAreaHeight - statusBarHeight;
      } catch (error) {
        console.error("计算消息列表高度失败:", error);
        this.messageListHeight = 600;
      }
    },
    /**
     * 输入框聚焦
     */
    onInputFocus() {
      this.showEmojiPanel = false;
      this.showMorePanel = false;
      this.calculateMessageListHeight();
      this.sendTypingStatus(true);
    },
    /**
     * 输入框失焦
     */
    onInputBlur() {
      this.calculateMessageListHeight();
      this.sendTypingStatus(false);
    },
    /**
     * 发送输入状态
     */
    sendTypingStatus(isTyping) {
      try {
        const wsMessage = {
          type: "typing",
          toUserId: this.userId,
          fromUserId: this.userInfo.userId,
          isTyping
        };
        utils_websocket.sendWebSocketMessage(wsMessage);
      } catch (error) {
        console.log("发送输入状态失败:", error);
      }
    },
    /**
     * 切换更多功能面板
     */
    toggleMorePanel() {
      this.showMorePanel = !this.showMorePanel;
      this.showEmojiPanel = false;
      this.calculateMessageListHeight();
    },
    /**
     * 切换表情面板
     */
    toggleEmojiPanel() {
      this.showEmojiPanel = !this.showEmojiPanel;
      this.showMorePanel = false;
      this.calculateMessageListHeight();
    },
    /**
     * 开始语音录制
     */
    startVoiceRecord() {
      this.isRecordingVoice = true;
      common_vendor.index.showToast({
        title: "开始录音",
        icon: "none"
      });
    },
    /**
     * 结束语音录制
     */
    endVoiceRecord() {
      this.isRecordingVoice = false;
      common_vendor.index.showToast({
        title: "录音结束",
        icon: "none"
      });
    },
    /**
     * 选择图片
     */
    chooseImage() {
      this.showMorePanel = false;
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          common_vendor.index.showToast({
            title: "图片功能开发中",
            icon: "none"
          });
        }
      });
    },
    /**
     * 拍照
     */
    takePhoto() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "拍照功能开发中",
        icon: "none"
      });
    },
    /**
     * 选择视频
     */
    chooseVideo() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "视频功能开发中",
        icon: "none"
      });
    },
    /**
     * 选择文件
     */
    chooseFile() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "文件功能开发中",
        icon: "none"
      });
    },
    /**
     * 分享位置
     */
    shareLocation() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "位置功能开发中",
        icon: "none"
      });
    },
    /**
     * 发送语音
     */
    sendVoice() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "语音功能开发中",
        icon: "none"
      });
    },
    /**
     * 发送红包
     */
    sendRedPacket() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "红包功能开发中",
        icon: "none"
      });
    },
    /**
     * 转账
     */
    transfer() {
      this.showMorePanel = false;
      common_vendor.index.showToast({
        title: "转账功能开发中",
        icon: "none"
      });
    },
    /**
     * 查看资料
     */
    viewProfile() {
      this.hideChatMenu();
      common_vendor.index.showToast({
        title: "查看资料功能开发中",
        icon: "none"
      });
    },
    /**
     * 清空聊天记录
     */
    clearHistory() {
      this.hideChatMenu();
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有聊天记录吗？",
        success: (res) => {
          if (res.confirm) {
            this.messages = [];
            common_vendor.index.showToast({
              title: "已清空聊天记录",
              icon: "success"
            });
          }
        }
      });
    },
    /**
     * 设置背景
     */
    setBackground() {
      this.hideChatMenu();
      common_vendor.index.showToast({
        title: "设置背景功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.nickname),
    c: common_vendor.t($data.isConnected ? "在线" : "离线"),
    d: common_vendor.o((...args) => $options.showChatMenu && $options.showChatMenu(...args)),
    e: $data.hasMoreMessages
  }, $data.hasMoreMessages ? {
    f: common_vendor.t($data.loadingMore ? "加载中..." : "下拉加载更多")
  } : {}, {
    g: common_vendor.f($data.messages, (message, k0, i0) => {
      return common_vendor.e({
        a: $options.shouldShowTime(message)
      }, $options.shouldShowTime(message) ? {
        b: common_vendor.t($options.formatDetailTime(message.sendTime))
      } : {}, {
        c: common_vendor.t(message.content),
        d: message.fromUserId === $data.userInfo.userId ? 1 : "",
        e: message.fromUserId === $data.userInfo.userId ? 1 : "",
        f: message.fromUserId === $data.userInfo.userId
      }, message.fromUserId === $data.userInfo.userId ? common_vendor.e({
        g: common_vendor.t($options.formatTime(message.sendTime)),
        h: message.status === "sending"
      }, message.status === "sending" ? {} : message.status === "failed" ? {} : {}, {
        i: message.status === "failed"
      }) : {}, {
        j: `msg-${message.id}-${message.sendTime}`,
        k: message.fromUserId === $data.userInfo.userId ? 1 : ""
      });
    }),
    h: $data.messages.length === 0
  }, $data.messages.length === 0 ? {} : {}, {
    i: $data.scrollTop,
    j: common_vendor.o((...args) => $options.loadMoreMessages && $options.loadMoreMessages(...args)),
    k: common_vendor.t($data.showMorePanel ? "⌨️" : "+"),
    l: common_vendor.o((...args) => $options.toggleMorePanel && $options.toggleMorePanel(...args)),
    m: $data.showMorePanel ? 1 : "",
    n: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    o: common_vendor.o((...args) => $options.onInputFocus && $options.onInputFocus(...args)),
    p: common_vendor.o((...args) => $options.onInputBlur && $options.onInputBlur(...args)),
    q: $data.inputMessage,
    r: common_vendor.o(($event) => $data.inputMessage = $event.detail.value),
    s: common_vendor.o((...args) => $options.toggleEmojiPanel && $options.toggleEmojiPanel(...args)),
    t: $data.showEmojiPanel ? 1 : "",
    v: $data.inputMessage.trim()
  }, $data.inputMessage.trim() ? {
    w: common_vendor.t($data.sending ? "..." : "发送"),
    x: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    y: $data.sending ? 1 : ""
  } : {
    z: common_vendor.o((...args) => $options.startVoiceRecord && $options.startVoiceRecord(...args)),
    A: common_vendor.o((...args) => $options.endVoiceRecord && $options.endVoiceRecord(...args))
  }, {
    B: $data.showEmojiPanel
  }, $data.showEmojiPanel ? {
    C: common_vendor.f($data.emojiList, (emoji, k0, i0) => {
      return {
        a: common_vendor.t(emoji),
        b: emoji,
        c: common_vendor.o(($event) => $options.insertEmoji(emoji), emoji)
      };
    })
  } : {}, {
    D: $data.showMorePanel
  }, $data.showMorePanel ? {
    E: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    F: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args)),
    G: common_vendor.o((...args) => $options.chooseVideo && $options.chooseVideo(...args)),
    H: common_vendor.o((...args) => $options.chooseFile && $options.chooseFile(...args)),
    I: common_vendor.o((...args) => $options.shareLocation && $options.shareLocation(...args)),
    J: common_vendor.o((...args) => $options.sendVoice && $options.sendVoice(...args)),
    K: common_vendor.o((...args) => $options.sendRedPacket && $options.sendRedPacket(...args)),
    L: common_vendor.o((...args) => $options.transfer && $options.transfer(...args))
  } : {}, {
    M: $data.showEmojiPanel || $data.showMorePanel ? 1 : "",
    N: $data.showChatMenuModal
  }, $data.showChatMenuModal ? {
    O: common_vendor.o((...args) => $options.viewProfile && $options.viewProfile(...args)),
    P: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    Q: common_vendor.o((...args) => $options.setBackground && $options.setBackground(...args)),
    R: common_vendor.o(() => {
    }),
    S: common_vendor.o((...args) => $options.hideChatMenu && $options.hideChatMenu(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-02c82538"], ["__file", "E:/wolk/study/uniappIm/src/pages/chatDetail/chatDetail.vue"]]);
wx.createPage(MiniProgramPage);
