.header {
  position: relative;
  z-index: 7;
  background-color: #fff;

  display: flex;
  align-items: center;
  justify-content: center;

  height: 44px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;

  ._title {
    position: relative;
    z-index: 7;
  }

  ._back-btn {
    display: none;
  }

  ._no-border-btn {
    position: absolute;
    height: 40px;
    width: 40px;
    top: 2px;
    right: 10px;
    border-width: 0;
    padding: 10px 5px;
    outline: none;
    &::before {
      border-width: 0;
    }

    & > span {
      display: inline-block;
      width: 22px;
      height: 20px;
    }
    svg {
      vertical-align: top;
    }
  }

  ._feedback {
    right: auto;
    left: 10px;
    width: auto;

    ._icon {
      display: none;
    }

    svg {
      vertical-align: -2px;
    }
    & > span {
      font-size: 12px;
      line-height: 20px;
      vertical-align: top;
      width: auto;
      white-space: nowrap;
    }
  }

  ._subtitle-btn {
    position: absolute;
    top: 10px;
    right: 55px;
    z-index: 7;
    padding: 4px 6px;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid #3a3d48;
    font-size: 12px;
    line-height: 15px;
    color: #3a3d48;

    ._icon {
      display: none;
    }

    &.is-visible {
      background-color: #3a3d48;
      color: #fff;
    }
    & > span {
      width: auto;
      height: 15px;
    }
  }

  ._setting-btn ._text {
    display: none;
  }
}

.header-pop {
  --adm-color-primary: #624aff;
  .adm-popup-body {
    background: #f5f5fa;
  }

  ._title {
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #26244c;
    padding: 12px 0;
    border-bottom: 1px solid #e7e8ee;
  }
}

.setting-pop {
  .adm-dialog-content {
    padding: 0 0 20px;
  }
  ul {
    list-style: none;
    padding: 4px 0 10px;
    max-height: 300px;
    max-height: 40vh;
    overflow-y: auto;
  }
  li {
    padding: 0 20px;
  }

  ._itemBox {
    padding: 16px 0;
    display: flex;
    align-items: center;
  }
  ._itemInfo {
    flex: 1;
  }
  ._itemTitle {
    font-size: 14px;
    line-height: 22px;
    color: #26244c;
  }

  ._itemDesc {
    margin-top: 4px;
    font-size: 10px;
    line-height: 16px;
    color: #747a8c;
  }

  ._itemSwitch .adm-switch {
    --width: 36px;
    --height: 18px;
    .adm-switch-handle {
      background: #747a8c;
      box-shadow: none;
    }
    &.adm-switch-checked {
      .adm-switch-handle {
        background: var(--adm-color-text-light-solid);
      }
    }
  }

  ._mode {
    .adm-selector {
      .adm-space {
        width: 100%;
      }
      .adm-space-item {
        flex: 1 1 0;
      }
      .adm-selector-item {
        width: 100%;
        padding: 16px;
        box-sizing: border-box;
        background: #f6f5ff;
        border: 1px solid #d8d9e6;
        border-radius: 8px;
        font-size: 12px;
        line-height: 18px;
        color: #26244c;

        &.adm-selector-item-active {
          border: 1px solid var(--adm-color-primary);
          .adm-selector-check-mark-wrapper {
            display: none;
          }
        }
      }
    }
  }

  ._voiceId {
    .adm-radio-content {
      width: 100%;
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 18px;
      color: #26244c;
      padding-left: 0;
      &::after {
        content: var(--btn-text);
        border: 1px solid #b2b7c4;
        font-size: 12px;
        line-height: 18px;
        color: #26244c;
        padding: 2px 12px;
        border-radius: 12px;
      }
    }
    .adm-radio-icon {
      display: none;
    }
    .adm-space-vertical > .adm-space-item {
      margin-bottom: 16px;
    }
    .adm-radio {
      width: 100%;
    }
    .adm-radio-checked .adm-radio-content {
      &::after {
        content: ' ';
        border: none;
        display: inline-block;
        width: 16px;
        height: 16px;
        padding: 0;
        background-repeat: no-repeat;
        margin-right: 17px;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIGZpbGw9Im5vbmUiIHZlcnNpb249IjEuMSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiB2aWV3Qm94PSIwIDAgMTIgMTIiPgogIDxnPgogICAgPGc+CiAgICAgIDxwYXRoIGQ9Ik0wLC05LjA5NDk0NzAxNzcyOTI4MmUtMTNMMS43MDkyMiwtOS4wOTQ5NDcwMTc3MjkyODJlLTEzTDEuNzA5MjIsMTEuOTYwNDk5OTk5OTk5MDlMMCwxMS45NjA0OTk5OTk5OTkwOUwwLC05LjA5NDk0NzAxNzcyOTI4MmUtMTNaIiBmaWxsPSIjNERDRkUxIiBmaWxsLW9wYWNpdHk9IjEiLz4KICAgIDwvZz4KICAgIDxnPgogICAgICA8cGF0aCBkPSJNMy40MzAyODY4ODQzMDc4NjEzLDUuOTI5NzI4MDMxMTU4NDQ3TDUuMTM5NTA2ODg0MzA3ODYxNSw1LjkyOTcyODAzMTE1ODQ0N0w1LjEzOTUwNjg4NDMwNzg2MTUsMTEuOTA5OTc4MDMxMTU4NDQ3TDMuNDMwMjg2ODg0MzA3ODYxMywxMS45MDk5NzgwMzExNTg0NDdMMy40MzAyODY4ODQzMDc4NjEzLDUuOTI5NzI4MDMxMTU4NDQ3WiIgZmlsbD0iIzREQ0ZFMSIgZmlsbC1vcGFjaXR5PSIxIi8+CiAgICA8L2c+CiAgICA8Zz4KICAgICAgPHBhdGggZD0iTTYuODYwNTEyMjU2NjIyMzE0NSwyLjM1MTQ1NTQ1MDA1Nzk4MzRMOC41Njk3MzIyNTY2MjIzMTUsMi4zNTE0NTU0NTAwNTc5ODM0TDguNTY5NzMyMjU2NjIyMzE1LDExLjk3NTc1NTQ1MDA1Nzk4M0w2Ljg2MDUxMjI1NjYyMjMxNDUsMTEuOTc1NzU1NDUwMDU3OTgzTDYuODYwNTEyMjU2NjIyMzE0NSwyLjM1MTQ1NTQ1MDA1Nzk4MzRaIiBmaWxsPSIjNERDRkUxIiBmaWxsLW9wYWNpdHk9IjEiLz4KICAgIDwvZz4KICAgIDxnPgogICAgICA8cGF0aCBkPSJNMTAuMjkwNzgzODgyMTQxMTEzLDQuODA1MTM1MjUwMDkxNTUzTDEyLjAwMDAwMzg4MjE0MTExMyw0LjgwNTEzNTI1MDA5MTU1M0wxMi4wMDAwMDM4ODIxNDExMTMsMTEuOTk5OTk1MjUwMDkxNTUzTDEwLjI5MDc4Mzg4MjE0MTExMywxMS45OTk5OTUyNTAwOTE1NTNMMTAuMjkwNzgzODgyMTQxMTEzLDQuODA1MTM1MjUwMDkxNTUzWiIgZmlsbD0iIzREQ0ZFMSIgZmlsbC1vcGFjaXR5PSIxIi8+CiAgICA8L2c+CiAgPC9nPgo8L3N2Zz4=');
        border-radius: 0;
      }
    }
  }

  ._voiceIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    margin-right: 12px;
  }
  ._voiceName {
    flex: 1;
  }
}

@media screen and (min-width: 768px) {
  .header {
    position: sticky;
    left: 0;
    right: 0;
    top: 0;
    flex: 0 0 auto;
    margin: 0 24px;
    border-bottom: 1px solid #d8d9e6;
    justify-content: flex-start;

    ._back-btn {
      display: inline-block;
      margin-right: 6px;
      width: 26px;
      height: 26px;
      line-height: 26px;
      border: none;
      padding: 2px;

      & > span {
        display: inline-block;
        width: 22px;
        height: 22px;
      }

      svg {
        width: 22px;
        height: 22px;
      }
    }

    ._gap {
      flex: 1;
    }

    ._no-border-btn,
    ._subtitle-btn {
      margin-left: 22px;
      position: relative;
      width: auto;
      height: auto;
      top: initial;
      bottom: initial;
      left: initial;
      right: initial;
      font-size: 14px;
      line-height: 22px;
      color: #26244c;

      & > span {
        width: auto;
        height: auto;
      }
    }

    ._actions {
      display: flex;
      align-items: center;
      ._icon {
        margin-right: 4px;
        svg {
          width: 16px;
          height: 16px;
        }
      }
    }

    ._subtitle-btn {
      border: none;

      ._icon {
        display: inline-block;
        vertical-align: -3px;
      }

      &.is-visible {
        background-color: var(--background-color);
        color: #3a3d48;
      }
    }

    ._feedback {
      ._icon {
        display: inline-block;
        vertical-align: -2px;
      }

      & > span {
        font-size: 14px;
        line-height: 22px;
        color: #26244c;
      }
    }

    ._setting-btn {
      ._icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 16px;
      }
      ._text {
        display: inline-block;
      }
    }
  }

  .setting-pop ul {
    width: 400px;
    max-height: 300px;
  }
}
