
.test-container.data-v-1950f93b {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}
.test-header.data-v-1950f93b {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-1950f93b {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.test-section.data-v-1950f93b {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.section-title.data-v-1950f93b {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.status-item.data-v-1950f93b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.status-label.data-v-1950f93b {
  font-size: 28rpx;
  color: #666;
}
.status-value.data-v-1950f93b {
  font-size: 28rpx;
  font-weight: bold;
}
.status-value.success.data-v-1950f93b {
  color: #07c160;
}
.status-value.error.data-v-1950f93b {
  color: #fa5151;
}
.test-btn.data-v-1950f93b {
  width: 100%;
  height: 80rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}
.test-btn.data-v-1950f93b:active {
  background: #0056cc;
}
.log-container.data-v-1950f93b {
  height: 400rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 20rpx;
  background: #fafafa;
}
.log-item.data-v-1950f93b {
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #eee;
}
.log-time.data-v-1950f93b {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}
.log-message.data-v-1950f93b {
  font-size: 26rpx;
  line-height: 1.4;
}
.log-message.info.data-v-1950f93b {
  color: #333;
}
.log-message.success.data-v-1950f93b {
  color: #07c160;
}
.log-message.error.data-v-1950f93b {
  color: #fa5151;
}
