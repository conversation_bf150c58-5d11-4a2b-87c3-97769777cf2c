import axios from 'axios'
import { ElMessage } from 'element-plus'

const request = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code === 200) {
      return res
    }
    // 401: 未登录或token过期
    if (res.code === 401) {
      ElMessage.error('请先登录')
      // 清除token并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
      return Promise.reject(new Error('请先登录'))
    }
    ElMessage.error(res.msg || '请求失败')
    return Promise.reject(new Error(res.msg || '请求失败'))
  },
  error => {
    if (error.response) {
      if (error.response.status === 401) {
        ElMessage.error('请先登录')
        localStorage.removeItem('token')
        window.location.href = '/login'
      } else {
        ElMessage.error(error.response.data.msg || '请求失败')
      }
    } else {
      ElMessage.error('网络错误，请稍后重试')
    }
    return Promise.reject(error)
  }
)

export default request 