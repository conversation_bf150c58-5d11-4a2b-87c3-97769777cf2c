package cn.zhentao.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 支付服务接口
 */
public interface PaymentService {

    /**
     * 创建支付订单
     * @param tradeNo 交易号
     * @param amount 金额
     * @param payChannel 支付渠道
     * @param subject 商品描述
     * @return 支付信息（包含二维码、支付链接等）
     */
    Map<String, Object> createPayment(String tradeNo, BigDecimal amount, Integer payChannel, String subject);

    /**
     * 验证支付回调
     * @param payChannel 支付渠道
     * @param callbackData 回调数据
     * @return 验证结果
     */
    boolean verifyPaymentCallback(Integer payChannel, Map<String, Object> callbackData);

    /**
     * 生成支付二维码
     * @param tradeNo 交易号
     * @param payChannel 支付渠道
     * @return 二维码内容
     */
    String generateQrCode(String tradeNo, Integer payChannel);

    /**
     * 查询支付状态
     * @param tradeNo 交易号
     * @param payChannel 支付渠道
     * @return 支付状态
     */
    Map<String, Object> queryPaymentStatus(String tradeNo, Integer payChannel);

    /**
     * 取消支付订单
     * @param tradeNo 交易号
     * @param payChannel 支付渠道
     * @return 取消结果
     */
    boolean cancelPayment(String tradeNo, Integer payChannel);
}
