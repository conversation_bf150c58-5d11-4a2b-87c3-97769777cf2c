
.friend-container.data-v-d48b96ff {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header.data-v-d48b96ff {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.header-content.data-v-d48b96ff {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  height: 88rpx;
}
.title.data-v-d48b96ff {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

/* 标签页样式 */
.tabs.data-v-d48b96ff {
  display: flex;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.95) 100%);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2rpx 20rpx rgba(102, 126, 234, 0.08);
  position: relative;
}
.tabs.data-v-d48b96ff::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}
.tab-item.data-v-d48b96ff {
  flex: 1;
  padding: 32rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  gap: 12rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 16rpx 16rpx 0 0;
  margin: 0 4rpx;
}
.tab-item.data-v-d48b96ff:active {
  -webkit-transform: scale(0.96);
          transform: scale(0.96);
}
.tab-item.active.data-v-d48b96ff {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.tab-item.active.data-v-d48b96ff::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx 3rpx 0 0;
  box-shadow: 0 -2rpx 8rpx rgba(102, 126, 234, 0.4);
}
.tab-text.data-v-d48b96ff {
  font-size: 30rpx;
  font-weight: 500;
  color: #666;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.tab-item.active .tab-text.data-v-d48b96ff {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.tab-badge.data-v-d48b96ff {
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  color: white;
  font-size: 20rpx;
  font-weight: 700;
  padding: 6rpx 10rpx;
  border-radius: 20rpx;
  min-width: 28rpx;
  text-align: center;
  line-height: 1;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  -webkit-animation: pulse-badge-d48b96ff 2s infinite;
          animation: pulse-badge-d48b96ff 2s infinite;
}
@-webkit-keyframes pulse-badge-d48b96ff {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.6);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
}
@keyframes pulse-badge-d48b96ff {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.6);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
}

/* 内容区域 */
.content.data-v-d48b96ff {
  flex: 1;
  background: #f5f5f5;
  padding-bottom: 100rpx; /* 为底部导航栏留出空间 */
}

/* 好友列表样式 */
.friend-list.data-v-d48b96ff, .request-list.data-v-d48b96ff {
  padding: 20rpx;
}
.friend-item.data-v-d48b96ff, .request-item.data-v-d48b96ff, .search-item.data-v-d48b96ff {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.avatar.data-v-d48b96ff {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.avatar-text.data-v-d48b96ff {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}
.friend-info.data-v-d48b96ff, .request-info.data-v-d48b96ff, .user-info.data-v-d48b96ff {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.friend-name.data-v-d48b96ff, .request-name.data-v-d48b96ff, .user-name.data-v-d48b96ff {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.friend-username.data-v-d48b96ff, .user-username.data-v-d48b96ff {
  font-size: 24rpx;
  color: #999;
}
.request-message.data-v-d48b96ff {
  font-size: 26rpx;
  color: #666;
}
.request-time.data-v-d48b96ff {
  font-size: 22rpx;
  color: #999;
}

/* 操作按钮样式 */
.friend-actions.data-v-d48b96ff {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-d48b96ff {
  height: 56rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.remark-btn.data-v-d48b96ff {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 24rpx;
  min-width: 88rpx;
}
.remark-btn.data-v-d48b96ff:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.delete-btn.data-v-d48b96ff {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  padding: 0 24rpx;
  min-width: 88rpx;
}
.delete-btn.data-v-d48b96ff:active {
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.btn-icon.data-v-d48b96ff {
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-text.data-v-d48b96ff {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}
.request-actions.data-v-d48b96ff {
  display: flex;
  gap: 20rpx;
}
.accept-btn.data-v-d48b96ff, .reject-btn.data-v-d48b96ff, .add-btn.data-v-d48b96ff {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  text-align: center;
}
.accept-btn.data-v-d48b96ff, .add-btn.data-v-d48b96ff {
  background: #667eea;
  color: white;
}
.reject-btn.data-v-d48b96ff {
  background: #ff4757;
  color: white;
}
.request-status.data-v-d48b96ff {
  display: flex;
  align-items: center;
}
.status-text.data-v-d48b96ff {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.status-pending.data-v-d48b96ff {
  background: #ffa502;
  color: white;
}
.status-accepted.data-v-d48b96ff {
  background: #2ed573;
  color: white;
}
.status-rejected.data-v-d48b96ff {
  background: #ff4757;
  color: white;
}

/* 空状态样式 */
.empty-state.data-v-d48b96ff {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}
.empty-text.data-v-d48b96ff {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}
.empty-hint.data-v-d48b96ff {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 搜索弹窗样式 */
.search-modal.data-v-d48b96ff {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.search-content.data-v-d48b96ff {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}
.search-header.data-v-d48b96ff {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.search-title.data-v-d48b96ff {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.search-input-group.data-v-d48b96ff {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.search-input.data-v-d48b96ff {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.search-btn.data-v-d48b96ff {
  padding: 20rpx 40rpx;
  background: #667eea;
  color: white;
  border-radius: 10rpx;
  font-size: 28rpx;
  text-align: center;
}
.search-results.data-v-d48b96ff {
  flex: 1;
  padding: 20rpx;
  max-height: 600rpx;
}

/* 备注设置模态框样式 */
.modal-overlay.data-v-d48b96ff {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-animation: fadeIn-d48b96ff 0.3s ease-out;
          animation: fadeIn-d48b96ff 0.3s ease-out;
}
@-webkit-keyframes fadeIn-d48b96ff {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeIn-d48b96ff {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.remark-modal.data-v-d48b96ff {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  -webkit-animation: slideUp-d48b96ff 0.3s ease-out;
          animation: slideUp-d48b96ff 0.3s ease-out;
}
@-webkit-keyframes slideUp-d48b96ff {
from {
    opacity: 0;
    -webkit-transform: translateY(60rpx) scale(0.9);
            transform: translateY(60rpx) scale(0.9);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
@keyframes slideUp-d48b96ff {
from {
    opacity: 0;
    -webkit-transform: translateY(60rpx) scale(0.9);
            transform: translateY(60rpx) scale(0.9);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
.modal-header.data-v-d48b96ff {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
}
.modal-title.data-v-d48b96ff {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.close-btn.data-v-d48b96ff {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(245, 245, 245, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.close-btn.data-v-d48b96ff:active {
  background: rgba(230, 230, 230, 0.8);
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.close-icon.data-v-d48b96ff {
  font-size: 32rpx;
  color: #999;
}
.modal-content.data-v-d48b96ff {
  padding: 40rpx 30rpx;
}
.friend-preview.data-v-d48b96ff {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: rgba(248, 249, 250, 0.8);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.preview-avatar.data-v-d48b96ff {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.preview-avatar-text.data-v-d48b96ff {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}
.preview-info.data-v-d48b96ff {
  flex: 1;
}
.preview-name.data-v-d48b96ff {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.preview-username.data-v-d48b96ff {
  font-size: 24rpx;
  color: #888;
  display: block;
}
.input-section.data-v-d48b96ff {
  margin-bottom: 20rpx;
}
.input-label.data-v-d48b96ff {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}
.remark-input.data-v-d48b96ff {
  width: 100%;
  min-height: 80rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(229, 229, 229, 0.6);
  border-radius: 16rpx;
  font-size: 32rpx;
  background: rgba(255, 255, 255, 1);
  transition: all 0.3s ease;
  box-sizing: border-box;
  color: #333;
  line-height: 1.4;
  resize: none;
}
.remark-input.data-v-d48b96ff:focus {
  border-color: #667eea;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.modal-footer.data-v-d48b96ff {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx;
}
.cancel-btn.data-v-d48b96ff, .confirm-btn.data-v-d48b96ff {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}
.cancel-btn.data-v-d48b96ff {
  background: rgba(245, 245, 245, 0.8);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  color: #666;
  border: 1rpx solid rgba(229, 229, 229, 0.6);
}
.cancel-btn.data-v-d48b96ff:active {
  background: rgba(232, 232, 232, 0.8);
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.confirm-btn.data-v-d48b96ff {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}
.confirm-btn.data-v-d48b96ff:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}
.confirm-btn.data-v-d48b96ff:disabled {
  background: rgba(204, 204, 204, 0.8);
  color: #999;
  box-shadow: none;
}
.friend-remark.data-v-d48b96ff {
  font-size: 24rpx;
  color: #888;
  margin-top: 4rpx;
  display: block;
}

/* 好友搜索样式 */
.search-section.data-v-d48b96ff {
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}
.search-box.data-v-d48b96ff {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  transition: all 0.3s ease;
}
.search-box.data-v-d48b96ff:focus-within {
  background: white;
  box-shadow: 0 2rpx 12rpx rgba(102, 126, 234, 0.15);
  border: 2rpx solid #667eea;
  padding: 14rpx 18rpx;
}
.search-icon.data-v-d48b96ff {
  font-size: 28rpx;
  color: #999;
  margin-right: 16rpx;
}
.search-input.data-v-d48b96ff {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}
.search-input.data-v-d48b96ff::-webkit-input-placeholder {
  color: #999;
}
.search-input.data-v-d48b96ff::placeholder {
  color: #999;
}
.clear-icon.data-v-d48b96ff {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s ease;
}
.clear-icon.data-v-d48b96ff:active {
  background: #ccc;
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
