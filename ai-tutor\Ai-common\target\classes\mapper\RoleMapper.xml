<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhentao.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="cn.zhentao.pojo.Role">
            <id property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
            <result property="roleKey" column="role_key" jdbcType="VARCHAR"/>
            <result property="roleSort" column="role_sort" jdbcType="INTEGER"/>
            <result property="dataScope" column="data_scope" jdbcType="CHAR"/>
            <result property="menuCheckStrictly" column="menu_check_strictly" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id,role_name,role_key,
        role_sort,data_scope,menu_check_strictly,
        status,del_flag,create_by,
        create_time,update_by,update_time,
        remark
    </sql>
</mapper>
