-- 初始化用户数据 (密码都是 123456)
INSERT INTO user (id, username, nickname, password, email, phonenumber, sex, status, del_flag, user_type, deleted, create_time, update_time) VALUES
(1, 'admin', '系统管理员', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '13800138000', '0', 0, '0', '00', 0, NOW(), NOW()),
(2, 'teacher', '张老师', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '13800138001', '0', 0, '0', '01', 0, NOW(), NOW()),
(3, 'student', '李同学', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', '13800138002', '1', 0, '0', '02', 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
username=VALUES(username), nickname=VALUES(nickname), password=VALUES(password), 
email=VALUES(email), phonenumber=VALUES(phonenumber), update_time=NOW();

-- 初始化角色数据  
INSERT INTO role (role_id, role_name, role_description, status, deleted, create_time, update_time) VALUES
(1, 'SUPER_ADMIN', '超级管理员', 0, 0, NOW(), NOW()),
(2, 'ADMIN', '管理员', 0, 0, NOW(), NOW()),
(3, 'TEACHER', '教师', 0, 0, NOW(), NOW()),
(4, 'STUDENT', '学生', 0, 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
role_name=VALUES(role_name), role_description=VALUES(role_description), update_time=NOW();

-- 初始化权限数据
INSERT INTO permission (permission_id, permission_name, permission_description, api_path, method, status, deleted, create_time, update_time) VALUES
(1, 'user:list', '查看用户列表', '/user/list', 'GET', 0, 0, NOW(), NOW()),
(2, 'user:add', '添加用户', '/user', 'POST', 0, 0, NOW(), NOW()),
(3, 'user:edit', '编辑用户', '/user/*', 'PUT', 0, 0, NOW(), NOW()),
(4, 'user:delete', '删除用户', '/user/*', 'DELETE', 0, 0, NOW(), NOW()),
(5, 'role:list', '查看角色列表', '/role/list', 'GET', 0, 0, NOW(), NOW()),
(6, 'role:add', '添加角色', '/role', 'POST', 0, 0, NOW(), NOW()),
(7, 'role:edit', '编辑角色', '/role/*', 'PUT', 0, 0, NOW(), NOW()),
(8, 'role:delete', '删除角色', '/role/*', 'DELETE', 0, 0, NOW(), NOW()),
(9, 'menu:list', '查看菜单列表', '/menu/list', 'GET', 0, 0, NOW(), NOW()),
(10, 'menu:add', '添加菜单', '/menu', 'POST', 0, 0, NOW(), NOW()),
(11, 'menu:edit', '编辑菜单', '/menu/*', 'PUT', 0, 0, NOW(), NOW()),
(12, 'menu:delete', '删除菜单', '/menu/*', 'DELETE', 0, 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
permission_name=VALUES(permission_name), permission_description=VALUES(permission_description), 
api_path=VALUES(api_path), method=VALUES(method), update_time=NOW();

-- 初始化菜单数据
INSERT INTO menu (menu_id, menu_name, parent_id, path, component, menu_type, visible, status, icon, sort_order, deleted, create_time, update_time) VALUES
(1, '系统管理', 0, '/system', 'Layout', 'M', 1, 0, 'system', 1, 0, NOW(), NOW()),
(2, '用户管理', 1, '/system/user', 'system/user/index', 'C', 1, 0, 'user', 1, 0, NOW(), NOW()),
(3, '角色管理', 1, '/system/role', 'system/role/index', 'C', 1, 0, 'role', 2, 0, NOW(), NOW()),
(4, '菜单管理', 1, '/system/menu', 'system/menu/index', 'C', 1, 0, 'menu', 3, 0, NOW(), NOW()),
(5, '教学管理', 0, '/teaching', 'Layout', 'M', 1, 0, 'education', 2, 0, NOW(), NOW()),
(6, '课程管理', 5, '/teaching/course', 'teaching/course/index', 'C', 1, 0, 'course', 1, 0, NOW(), NOW()),
(7, '学生管理', 5, '/teaching/student', 'teaching/student/index', 'C', 1, 0, 'student', 2, 0, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
menu_name=VALUES(menu_name), path=VALUES(path), component=VALUES(component), update_time=NOW();

-- 初始化用户角色关联
INSERT INTO user_role (id, user_id, role_id) VALUES
(1, 1, 1),  -- admin -> SUPER_ADMIN
(2, 2, 3),  -- teacher -> TEACHER  
(3, 3, 4)   -- student -> STUDENT
ON DUPLICATE KEY UPDATE user_id=VALUES(user_id), role_id=VALUES(role_id);

-- 初始化角色权限关联
INSERT INTO role_permission (id, role_id, permission_id) VALUES
-- SUPER_ADMIN 拥有所有权限
(1, 1, 1), (2, 1, 2), (3, 1, 3), (4, 1, 4),
(5, 1, 5), (6, 1, 6), (7, 1, 7), (8, 1, 8),
(9, 1, 9), (10, 1, 10), (11, 1, 11), (12, 1, 12),
-- ADMIN 拥有查看和部分管理权限
(13, 2, 1), (14, 2, 2), (15, 2, 3),
(16, 2, 5), (17, 2, 6), (18, 2, 7),
(19, 2, 9), (20, 2, 10), (21, 2, 11),
-- TEACHER 拥有教学相关权限
(22, 3, 1), (23, 3, 5), (24, 3, 9),
-- STUDENT 拥有基本查看权限  
(25, 4, 1), (26, 4, 5), (27, 4, 9)
ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), permission_id=VALUES(permission_id); 