package cn.zhentao.service;

import cn.zhentao.pojo.Menu;
import cn.zhentao.pojo.Permission;
import cn.zhentao.pojo.Role;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 角色管理服务接口
 * <AUTHOR>
 * @createDate 2025-07-30
 */

public interface RoleService extends IService<Role> {

    /**
     * 分页查询角色
     */
    IPage<Role> getRolePage(Page<Role> page, String roleName, String status);

    /**
     * 根据角色键查询角色
     */
    Role getRoleByKey(String roleKey);

    /**
     * 获取角色的所有权限
     */
    List<Permission> getRolePermissions(Long roleId);

    /**
     * 获取角色的所有菜单
     */
    List<Menu> getRoleMenus(Long roleId);

    /**
     * 分配权限给角色
     */
    boolean assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 移除角色的权限
     */
    boolean removeRolePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 分配菜单给角色
     */
    boolean assignMenusToRole(Long roleId, List<Long> menuIds);

    /**
     * 检查角色名是否存在
     */
    boolean existsByRoleName(String roleName);

    /**
     * 检查角色键是否存在
     */
    boolean existsByRoleKey(String roleKey);

    /**
     * 获取所有启用的角色
     */
    List<Role> getEnabledRoles();

    /**
     * 批量删除角色
     */
    boolean deleteRolesByIds(List<Long> roleIds);

    /**
     * 更新角色状态
     */
    boolean updateRoleStatus(Long roleId, String status);
}
