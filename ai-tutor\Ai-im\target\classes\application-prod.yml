# 生产环境配置
server:
  port: 8081
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    connection-timeout: 20000

spring:
  application:
    name: study-im
  
  # 数据库配置
  datasource:
    url: jdbc:mysql://**************:3306/tutor?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: root
    password: Sunshuo0818
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
        use_sql_comments: false
    open-in-view: false

  # Redis配置
  data:
    redis:
      host: **************
      port: 6379
      password: 2308
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 2
          max-wait: 5000ms

  # 数据初始化
  sql:
    init:
      mode: always
      data-locations: classpath:data.sql
      continue-on-error: false

# JWT配置
jwt:
  secret: mySecretKey123456789abcdefghijklmnopqrstuvwxyz
  expiration: 86400000  # 24小时

# 日志配置
logging:
  level:
    root: INFO
    com.zhentao.studyim: INFO
    org.springframework: WARN
    org.hibernate: WARN
    io.netty: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/im-system.log
    max-size: 100MB
    max-history: 30

# Netty WebSocket服务器配置
netty:
  websocket:
    port: 9998
    boss-threads: 1
    worker-threads: 4
    max-frame-size: 65536

# 系统配置
system:
  # 消息限流配置
  rate-limit:
    message-per-minute: 30
    
  # 缓存配置
  cache:
    recent-message-size: 50
    recent-message-expire-days: 7
    
  # 在线状态配置
  online-status:
    expire-hours: 24
    
  # 定时任务配置
  schedule:
    online-stats-update-minutes: 5
    cleanup-expired-data-hours: 1
