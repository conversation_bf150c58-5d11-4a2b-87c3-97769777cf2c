import { AICallRunConfig } from '@/interface.ts';
// import { AICallAgentConfig } from 'aliyun-auikit-aicall';

// 如果需要自定义 AgentConfig，请参考以下写法
// when you need to customize AgentConfig, please refer to the following code

// const callAgentConfig = new AICallAgentConfig();
// callAgentConfig.agentGreeting = 'Custom Greeting';

const runConfig: AICallRunConfig = {
  // 常见区域: 'cn-shanghai', 'cn-beijing', 'cn-hangzhou', 'cn-shenzhen', 'ap-southeast-1'
  region: 'cn-beijing',
  // 应用服务器地址，格式示例 https://xxxx.domain.com
  // Your Application Server Address
  appServer: 'http://localhost:8082',

  // 您的语音通话智能体id
  // Your Voice Agent Id
  voiceAgentId: '8ed6ed9e6c57469e889a1bc3651ac64b',

  // callAgentConfig: callAgentConfig,
};

export default runConfig;
