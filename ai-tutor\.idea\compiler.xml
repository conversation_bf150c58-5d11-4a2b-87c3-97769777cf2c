<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="Ai-common" />
        <module name="Ai-gateway" />
        <module name="Ai-face" />
        <module name="Ai-member" />
        <module name="Ai-userlogin" />
        <module name="AiApp-service" />
        <module name="Ai-im" />
        <module name="AiPC-service" />
      </profile>
    </annotationProcessing>
  </component>
</project>