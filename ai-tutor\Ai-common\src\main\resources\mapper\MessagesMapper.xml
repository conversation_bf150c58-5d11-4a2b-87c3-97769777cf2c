<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhentao.mapper.MessagesMapper">

    <resultMap id="BaseResultMap" type="cn.zhentao.pojo.Messages">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fromUserId" column="from_user_id" jdbcType="BIGINT"/>
            <result property="toUserId" column="to_user_id" jdbcType="BIGINT"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="OTHER"/>
            <result property="status" column="status" jdbcType="OTHER"/>
            <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
            <result property="readTime" column="read_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,from_user_id,to_user_id,
        content,type,status,
        send_time,read_time
    </sql>
</mapper>
