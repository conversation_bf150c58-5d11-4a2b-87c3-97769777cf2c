"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      friendRequests: [],
      loading: false
    };
  },
  onLoad() {
    this.loadFriendRequests();
  },
  onShow() {
    this.loadFriendRequests();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    async loadFriendRequests() {
      this.loading = true;
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!userInfo) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }
        const result = await utils_api.getReceivedFriendRequests(userInfo.userId);
        console.log("好友申请列表:", result);
        if (result.code === 200) {
          this.friendRequests = result.data;
        } else {
          common_vendor.index.showToast({
            title: result.message || "加载失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("加载好友申请失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    async handleRequest(request, action) {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!userInfo) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }
        const result = await utils_api.handleFriendRequest({
          requestId: request.id,
          action,
          userId: userInfo.userId
        });
        console.log("处理好友申请结果:", result);
        if (result.code === 200) {
          request.status = action;
          this.$forceUpdate();
          common_vendor.index.showToast({
            title: action === "ACCEPTED" ? "已同意好友申请" : "已拒绝好友申请",
            icon: "success"
          });
          if (action === "ACCEPTED") {
            common_vendor.index.$emit("friendListUpdate");
          }
        } else {
          common_vendor.index.showToast({
            title: result.message || "操作失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("处理好友申请失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    getAvatarText(user) {
      return user.nickname ? user.nickname.charAt(0) : user.username.charAt(0);
    },
    formatTime(timeString) {
      if (!timeString)
        return "";
      const time = new Date(timeString);
      const now = /* @__PURE__ */ new Date();
      const diff = now - time;
      if (diff < 6e4) {
        return "刚刚";
      } else if (diff < 36e5) {
        return Math.floor(diff / 6e4) + "分钟前";
      } else if (diff < 864e5) {
        return Math.floor(diff / 36e5) + "小时前";
      } else {
        return time.toLocaleDateString();
      }
    },
    getStatusText(status) {
      switch (status) {
        case "ACCEPTED":
          return "已同意";
        case "REJECTED":
          return "已拒绝";
        case "PENDING":
          return "待处理";
        default:
          return "未知状态";
      }
    },
    getStatusClass(status) {
      switch (status) {
        case "ACCEPTED":
          return "status-accepted";
        case "REJECTED":
          return "status-rejected";
        default:
          return "status-pending";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.friendRequests.length > 0
  }, $data.friendRequests.length > 0 ? {
    c: common_vendor.f($data.friendRequests, (request, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getAvatarText(request.fromUser)),
        b: common_vendor.t(request.fromUser.nickname || request.fromUser.username),
        c: common_vendor.t(request.message || "请求添加您为好友"),
        d: common_vendor.t($options.formatTime(request.createTime)),
        e: request.status === "PENDING"
      }, request.status === "PENDING" ? {
        f: common_vendor.o(($event) => $options.handleRequest(request, "REJECTED"), request.id),
        g: common_vendor.o(($event) => $options.handleRequest(request, "ACCEPTED"), request.id)
      } : {
        h: common_vendor.t($options.getStatusText(request.status)),
        i: common_vendor.n($options.getStatusClass(request.status))
      }, {
        j: request.id
      });
    })
  } : !$data.loading ? {} : {}, {
    d: !$data.loading,
    e: $data.loading
  }, $data.loading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-98a9a758"], ["__file", "E:/wolk/study/uniappIm/src/pages/friendRequests/friendRequests.vue"]]);
wx.createPage(MiniProgramPage);
