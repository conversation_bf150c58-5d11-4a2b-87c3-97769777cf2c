package cn.zhentao.service;

import cn.zhentao.pojo.Permission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 权限管理服务接口
 * <AUTHOR>
 * @createDate 2025-07-30
 */
public interface PermissionService extends IService<Permission> {

    /**
     * 分页查询权限
     */
    IPage<Permission> getPermissionPage(Page<Permission> page, String permissionName, String permissionCode);

    /**
     * 根据权限标识查询权限
     */
    Permission getPermissionByCode(String permissionCode);

    /**
     * 根据API路径和方法查询权限
     */
    Permission getPermissionByApiPath(String apiPath, String method);

    /**
     * 获取所有权限树结构
     */
    List<Permission> getPermissionTree();

    /**
     * 根据菜单ID获取权限列表
     */
    List<Permission> getPermissionsByMenuId(Long menuId);

    /**
     * 检查权限标识是否存在
     */
    boolean existsByPermissionCode(String permissionCode);

    /**
     * 检查API路径和方法是否存在
     */
    boolean existsByApiPath(String apiPath, String method);

    /**
     * 批量删除权限
     */
    boolean deletePermissionsByIds(List<Long> permissionIds);

    /**
     * 根据角色ID获取权限列表
     */
    List<Permission> getPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID获取权限列表
     */
    List<Permission> getPermissionsByUserId(Long userId);
}
