<template>
  <div class="ai-features-page">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="AI智能助手"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
      class="custom-nav-bar"
    />

    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h2>你好，小明同学！</h2>
          <p>我是你的AI智能助手，随时为你服务</p>
        </div>
        <div class="welcome-avatar">🤖</div>
      </div>
      <div class="quick-stats">
        <div class="stat-item">
          <span class="stat-number">{{ todayUsage }}</span>
          <span class="stat-label">今日使用</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ features.length }}</span>
          <span class="stat-label">功能模块</span>
        </div>
      </div>
    </div>

    <!-- 功能网格 -->
    <div class="features-container">
      <h3 class="section-title">🌟 AI功能大全</h3>
      <div class="features-grid">
        <div
          v-for="feature in features"
          :key="feature.id"
          :class="['feature-card', feature.gradient]"
          @click="navigateToFeature(feature)"
        >
          <div class="feature-header">
            <div class="feature-icon">{{ feature.icon }}</div>
            <div class="feature-badge" v-if="feature.isNew">NEW</div>
          </div>
          <div class="feature-content">
            <h4 class="feature-title">{{ feature.title }}</h4>
            <p class="feature-desc">{{ feature.description }}</p>
            <div class="feature-example">{{ feature.example }}</div>
            <div class="feature-tags">
              <span v-for="tag in feature.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
          </div>
          <div class="feature-arrow">
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
    </div>

    <!-- 快速入口 -->
    <div class="quick-actions">
      <h3 class="section-title">⚡ 快速入口</h3>
      <div class="quick-buttons">
        <van-button
          type="primary"
          icon="volume-o"
          size="large"
          round
          @click="quickVoiceChat"
          class="quick-btn"
        >
          语音对话
        </van-button>
        <van-button
          type="success"
          icon="chat-o"
          size="large"
          round
          @click="quickQA"
          class="quick-btn"
        >
          快速问答
        </van-button>
      </div>
    </div>

    <!-- 使用统计 -->
    <div class="usage-stats">
      <h3 class="section-title">📊 使用统计</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">🎯</div>
          <div class="stat-info">
            <div class="stat-value">{{ favoriteFeature }}</div>
            <div class="stat-desc">最爱功能</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">⏰</div>
          <div class="stat-info">
            <div class="stat-value">{{ totalUsage }}</div>
            <div class="stat-desc">总使用次数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🔥</div>
          <div class="stat-info">
            <div class="stat-value">{{ streakDays }}</div>
            <div class="stat-desc">连续使用天数</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 功能列表 - 根据需求定制
const features = ref([
  {
    id: 'knowledge-qa',
    title: '知识问答',
    description: '知识渊博的AI助手，回答各种问题',
    example: '例如："地球的直径是多少？"',
    icon: '🧠',
    tags: ['百科', '学习', '问答'],
    gradient: 'gradient-blue',
    route: '/ai-features/knowledge-qa',
    isNew: false
  },
  {
    id: 'information-query',
    title: '信息查询',
    description: '查询天气、电话等各类信息',
    example: '例如："查询明天保定的天气"',
    icon: '🔍',
    tags: ['查询', '天气', '实时'],
    gradient: 'gradient-green',
    route: '/ai-features/information-query',
    isNew: false
  },
  {
    id: 'text-generation',
    title: '文本生成',
    description: '写作文、故事、诗歌等创意内容',
    example: '例如："写一首关于春天的诗"',
    icon: '✍️',
    tags: ['写作', '创意', '文学'],
    gradient: 'gradient-purple',
    route: '/ai-features/text-generation',
    isNew: false
  },
  {
    id: 'translation',
    title: '语言翻译',
    description: '多语言即时翻译，准确流畅',
    example: '例如："把\'我喜欢振涛\'翻译成英语"',
    icon: '🌐',
    tags: ['翻译', '语言', '国际'],
    gradient: 'gradient-orange',
    route: '/ai-features/translation',
    isNew: false
  },
  {
    id: 'emotional-companion',
    title: '情感陪伴',
    description: '情感识别与温暖回应',
    example: '识别情绪状态，给予安慰和支持',
    icon: '💝',
    tags: ['情感', '陪伴', '心理'],
    gradient: 'gradient-pink',
    route: '/ai-features/emotional-companion',
    isNew: false
  },
  {
    id: 'recommendation',
    title: '智能推荐',
    description: '个性化内容推荐服务',
    example: '推荐音乐、书籍、服务等',
    icon: '🎯',
    tags: ['推荐', '个性化', '智能'],
    gradient: 'gradient-cyan',
    route: '/ai-features/recommendation',
    isNew: false
  },

  {
    id: 'game-entertainment',
    title: '游戏娱乐',
    description: '语音游戏互动，寓教于乐',
    example: '猜谜语、成语接龙、问答游戏',
    icon: '🎮',
    tags: ['游戏', '娱乐', '互动'],
    gradient: 'gradient-red',
    route: '/ai-features/game-entertainment',
    isNew: false
  },
  {
    id: 'health-management',
    title: '健康管理',
    description: '健康数据分析与建议',
    example: '运动、饮食、睡眠健康评估',
    icon: '🏥',
    tags: ['健康', '运动', '养生'],
    gradient: 'gradient-teal',
    route: '/ai-features/health-management',
    isNew: false
  },
  {
    id: 'image-generation',
    title: 'AI文生图',
    description: '文字描述生成精美图片',
    example: '一只可爱的小猫在花园里玩耍',
    icon: '🎨',
    tags: ['图片', '创作', '艺术'],
    gradient: 'gradient-purple',
    route: '/ai-features/image-generation',
    isNew: true
  }
])

// 统计数据
const totalUsage = ref(0)
const todayUsage = ref(0)
const favoriteFeature = ref('知识问答')
const streakDays = ref(0)

// 导航到功能页面
const navigateToFeature = (feature: any) => {
  showToast({
    type: 'loading',
    message: '正在加载...',
    duration: 800
  })

  setTimeout(() => {
    router.push(feature.route)
  }, 800)
}

// 快速语音对话
const quickVoiceChat = () => {
  showToast({
    type: 'loading',
    message: '启动语音对话...',
    duration: 1000
  })

  setTimeout(() => {
    router.push('/voice-call')
  }, 1000)
}

// 快速问答
const quickQA = () => {
  showToast({
    type: 'loading',
    message: '进入问答模式...',
    duration: 800
  })

  setTimeout(() => {
    router.push('/ai-features/knowledge-qa')
  }, 800)
}

// 加载统计数据
const loadStats = () => {
  // 模拟数据，实际应该从API获取
  totalUsage.value = Math.floor(Math.random() * 500) + 100
  todayUsage.value = Math.floor(Math.random() * 20) + 5
  streakDays.value = Math.floor(Math.random() * 30) + 1
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.ai-features-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 20px;
}

.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-banner {
  margin: 20px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.welcome-text h2 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.welcome-text p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.welcome-avatar {
  font-size: 48px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.quick-stats {
  display: flex;
  gap: 20px;
}

.quick-stats .stat-item {
  text-align: center;
  flex: 1;
}

.quick-stats .stat-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #667eea;
}

.quick-stats .stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.features-container {
  padding: 0 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 20px 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-bottom: 32px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.feature-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
}

.feature-badge {
  background: #ff4757;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.feature-content {
  margin-bottom: 16px;
}

.feature-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.feature-desc {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.feature-example {
  font-size: 12px;
  color: #95a5a6;
  font-style: italic;
  margin-bottom: 12px;
  line-height: 1.3;
}

.feature-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.feature-arrow {
  color: #bdc3c7;
  font-size: 16px;
}

/* 渐变色定义 */
.gradient-blue { --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.gradient-purple { --gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.gradient-green { --gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
.gradient-pink { --gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.gradient-orange { --gradient: linear-gradient(135deg, #f12711 0%, #f5af19 100%); }
.gradient-cyan { --gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.gradient-yellow { --gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
.gradient-red { --gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
.gradient-teal { --gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

.quick-actions {
  padding: 0 16px;
}

.quick-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}

.quick-btn {
  flex: 1;
  height: 48px;
}

.usage-stats {
  padding: 0 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-desc {
  font-size: 12px;
  color: #7f8c8d;
}
</style>
