"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_websocket = require("../../utils/websocket.js");
const _sfc_main = {
  data() {
    return {
      apiStatus: false,
      wsStatus: false,
      logs: []
    };
  },
  onLoad() {
    this.addLog("测试页面加载完成", "info");
    this.addLog("API地址: http://localhost:8080/api", "info");
    this.addLog("WebSocket地址: ws://localhost:9999/ws", "info");
  },
  methods: {
    addLog(message, type = "info") {
      const now = /* @__PURE__ */ new Date();
      const time = now.toLocaleTimeString();
      this.logs.unshift({
        time,
        message,
        type
      });
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    async testUserList() {
      this.addLog("开始测试获取用户列表...", "info");
      try {
        const result = await utils_api.getUserList();
        if (result.code === 200) {
          this.apiStatus = true;
          this.addLog("✅ 获取用户列表成功", "success");
          this.addLog(`用户数量: ${result.data.length}`, "info");
          result.data.forEach((user) => {
            this.addLog(`用户: ${user.username} (${user.nickname})`, "info");
          });
        } else {
          this.addLog(`❌ 获取用户列表失败: ${result.message}`, "error");
        }
      } catch (error) {
        this.apiStatus = false;
        this.addLog(`❌ 获取用户列表失败: ${error.message}`, "error");
      }
    },
    async testRegister() {
      this.addLog("开始测试用户注册...", "info");
      const randomSuffix = Math.floor(Math.random() * 1e4);
      const testUser = {
        username: `test${randomSuffix}`,
        password: "test123",
        nickname: `测试用户${randomSuffix}`
      };
      try {
        const result = await utils_api.register(testUser);
        if (result.code === 200) {
          this.addLog("✅ 用户注册成功", "success");
          this.addLog(`用户: ${testUser.username}`, "info");
          this.addLog(
            `Token: ${result.data.token.substring(0, 20)}...`,
            "info"
          );
        } else {
          this.addLog(`❌ 用户注册失败: ${result.message}`, "error");
        }
      } catch (error) {
        this.addLog(`❌ 用户注册失败: ${error.message}`, "error");
      }
    },
    async testLogin() {
      this.addLog("开始测试用户登录...", "info");
      try {
        const result = await utils_api.login({
          username: "test",
          password: "test123"
        });
        if (result.code === 200) {
          this.addLog("✅ 用户登录成功", "success");
          this.addLog(`用户: ${result.data.user.username}`, "info");
          this.addLog(
            `Token: ${result.data.token.substring(0, 20)}...`,
            "info"
          );
        } else {
          this.addLog(`❌ 用户登录失败: ${result.message}`, "error");
        }
      } catch (error) {
        this.addLog(`❌ 用户登录失败: ${error.message}`, "error");
      }
    },
    testWebSocket() {
      this.addLog("开始测试WebSocket连接...", "info");
      const token = common_vendor.index.getStorageSync("token") || "test-token";
      try {
        utils_websocket.connectWebSocket(token, (message) => {
          this.addLog(
            `📨 收到WebSocket消息: ${JSON.stringify(message)}`,
            "success"
          );
        });
        setTimeout(() => {
          this.wsStatus = true;
          this.addLog("✅ WebSocket连接成功", "success");
        }, 2e3);
      } catch (error) {
        this.wsStatus = false;
        this.addLog(`❌ WebSocket连接失败: ${error.message}`, "error");
      }
    },
    async testFriendAPI() {
      this.addLog("开始测试好友管理API...", "info");
      try {
        const friendResult = await utils_api.getFriendList();
        if (friendResult.code === 200) {
          this.addLog(`✅ 获取好友列表成功，共${friendResult.data.length}个好友`, "success");
          friendResult.data.forEach((friend) => {
            this.addLog(`好友: ${friend.username} (${friend.nickname})`, "info");
          });
        } else {
          this.addLog(`❌ 获取好友列表失败: ${friendResult.message}`, "error");
        }
        const requestResult = await utils_api.getReceivedFriendRequests();
        if (requestResult.code === 200) {
          this.addLog(`✅ 获取好友申请成功，共${requestResult.data.length}个申请`, "success");
        } else {
          this.addLog(`❌ 获取好友申请失败: ${requestResult.message}`, "error");
        }
      } catch (error) {
        this.addLog(`❌ 好友管理API测试失败: ${error.message}`, "error");
      }
    },
    async testSearchUsers() {
      this.addLog("开始测试搜索用户...", "info");
      try {
        const result = await utils_api.searchUsers("test");
        if (result.code === 200) {
          this.addLog(`✅ 搜索用户成功，找到${result.data.length}个用户`, "success");
          result.data.forEach((user) => {
            this.addLog(`用户: ${user.username} (${user.nickname})`, "info");
          });
        } else {
          this.addLog(`❌ 搜索用户失败: ${result.message}`, "error");
        }
      } catch (error) {
        this.addLog(`❌ 搜索用户失败: ${error.message}`, "error");
      }
    },
    async testFriendRequest() {
      this.addLog("开始测试好友申请...", "info");
      try {
        const userResult = await utils_api.getUserList();
        if (userResult.code === 200 && userResult.data.length > 0) {
          const targetUser = userResult.data[0];
          const result = await utils_api.sendFriendRequest({
            toUserId: targetUser.userId,
            message: "测试好友申请"
          });
          if (result.code === 200) {
            this.addLog(`✅ 发送好友申请成功`, "success");
          } else {
            this.addLog(`❌ 发送好友申请失败: ${result.message}`, "error");
          }
        } else {
          this.addLog(`❌ 无法获取用户列表进行测试`, "error");
        }
      } catch (error) {
        this.addLog(`❌ 好友申请测试失败: ${error.message}`, "error");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.apiStatus ? "连接正常" : "连接失败"),
    b: $data.apiStatus ? 1 : "",
    c: !$data.apiStatus ? 1 : "",
    d: common_vendor.t($data.wsStatus ? "连接正常" : "连接失败"),
    e: $data.wsStatus ? 1 : "",
    f: !$data.wsStatus ? 1 : "",
    g: common_vendor.o((...args) => $options.testUserList && $options.testUserList(...args)),
    h: common_vendor.o((...args) => $options.testRegister && $options.testRegister(...args)),
    i: common_vendor.o((...args) => $options.testLogin && $options.testLogin(...args)),
    j: common_vendor.o((...args) => $options.testWebSocket && $options.testWebSocket(...args)),
    k: common_vendor.o((...args) => $options.testFriendAPI && $options.testFriendAPI(...args)),
    l: common_vendor.o((...args) => $options.testSearchUsers && $options.testSearchUsers(...args)),
    m: common_vendor.o((...args) => $options.testFriendRequest && $options.testFriendRequest(...args)),
    n: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log.time),
        b: common_vendor.t(log.message),
        c: common_vendor.n(log.type),
        d: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1950f93b"], ["__file", "E:/wolk/study/uniappIm/src/pages/test/test.vue"]]);
wx.createPage(MiniProgramPage);
