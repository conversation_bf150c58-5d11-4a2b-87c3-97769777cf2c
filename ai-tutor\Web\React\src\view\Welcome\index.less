.welcome {
  height: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
  .adm-tabs-header {
    margin-top: 10px;
    border-bottom: none;
  }

  .adm-tabs-tab {
    padding: 2px 0 4px;
    font-size: 12px;
    line-height: 22px;
  }

  .adm-swiper {
    position: relative;
    height: 100%;
  }

  .welcome-type-tab {
    margin: 16px 20px;
    padding: 2px;
    border-radius: 24px;
    border: 1px solid #b2b7c4;
    .adm-tabs-header {
      margin-top: 0;
      border-radius: 21px;
      overflow: hidden;
    }
    .adm-tabs-tab-line {
      height: 42px;
      border-radius: 21px;
    }
    .adm-tabs-tab-active {
      color: #fff;
    }
    .adm-tabs-header-mask {
      display: none;
    }
    .adm-tabs-tab {
      padding: 0;
      font-size: 14px;
      line-height: 42px;
      border-radius: 21px;
    }
  }
}

.welcome-swiper {
  position: relative;
  flex: 1;
  min-height: 0;

  .adm-button {
    display: none;
  }

  ._for-mobile {
    height: 100%;
  }

  ._for-desktop {
    display: none;
  }
}

.welcome-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;

  height: 44px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
}

.welcome-body {
  min-height: 0;
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
}

.welcome-img-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 50px 0 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.welcome-btn {
  display: flex;
  margin: 0 36px 36px;

  .adm-button {
    border-radius: 22px;
  }
}

@media screen and (min-width: 768px) {
  .welcome {
    align-items: center;

    .adm-tabs-tab {
      padding: 12px 0 16px;
      font-size: 16px;
      line-height: 24px;
    }
    .welcome-type-tab {
      margin-top: 60px;
      margin-bottom: 40px;
      max-width: 336px;
    }
  }
  .welcome-header {
    display: none;
  }

  .welcome-body {
    width: 100%;
    max-width: 640px;
    align-items: center;
  }

  .adm-tabs {
    width: 100%;
  }

  .welcome-swiper {
    max-width: 640px;

    ._for-mobile {
      display: none;
    }

    ._for-desktop {
      display: inline-block;
    }

    .adm-button {
      display: inline-block;
      border: none;
      width: 40px;
      height: 40px;
      position: absolute;
      top: 50%;
      z-index: 2;
      padding: 10px;
      margin-top: -20px;
      background: transparent;

      &._left {
        left: 0;
      }
      &._right {
        right: 0;
      }

      span {
        display: inline-block;
        width: 20px;
        height: 20px;
      }

      svg {
        vertical-align: top;
      }
    }
  }

  .welcome-btn {
    width: 300px;
    margin-bottom: 100px;
  }

  .welcome-img-box {
    padding-top: 30px;
  }
}
