
.custom-tab-bar.data-v-a845e0be {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.tab-item.data-v-a845e0be {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}
.tab-item.active.data-v-a845e0be {
  color: #3cc51f;
}
.tab-icon.data-v-a845e0be {
  margin-bottom: 4rpx;
}
.icon-text.data-v-a845e0be {
  font-size: 40rpx;
  line-height: 1;
}
.tab-text.data-v-a845e0be {
  font-size: 20rpx;
  color: #7A7E83;
  line-height: 1;
}
.tab-item.active .tab-text.data-v-a845e0be {
  color: #3cc51f;
  font-weight: 500;
}

/* 为页面内容留出底部空间 */
.page-content.data-v-a845e0be {
  padding-bottom: 100rpx;
}
