# AI智能助手 - Vue3版本

这是AI智能助手的Vue3实现版本，提供了完整的AI功能体验，包括语音通话、图像生成、文本处理等多种AI服务。

## 🚀 功能特性

### 核心功能
- **🎙️ AI语音通话** - 实时语音对话，支持智能打断和对讲模式
- **🎨 图像生成** - AI图像创作，支持1-4张图片同时生成
- **📚 知识问答** - AI百科全书，回答各种问题
- **✍️ 文本生成** - 创意写作助手，支持多种文本类型
- **🌐 智能翻译** - 多语言实时翻译
- **🔍 信息查询** - 天气、电话、资讯查询
- **💝 情感陪伴** - 心理支持和情感交流
- **🎯 智能推荐** - 个性化内容推荐
- **🎮 游戏娱乐** - 互动游戏和娱乐
- **🏥 健康管理** - 健康咨询和管理建议

### 技术特性
- **Vue 3** + **TypeScript** - 现代化前端框架
- **Vue Router** - 单页面应用路由
- **Pinia** - 状态管理
- **Vant 4** - 移动端UI组件库
- **Vite** - 快速构建工具
- **Less** - CSS预处理器
- **响应式设计** - 支持移动端和桌面端

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd Web/Vue3
npm install
```

### 开发模式
```bash
npm run dev
```
访问 http://localhost:5175

### 生产构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 🏗️ 项目结构

```
Web/Vue3/
├── src/
│   ├── views/                 # 页面组件
│   │   ├── HomePage/          # 主页
│   │   ├── VoiceCall/         # 语音通话
│   │   ├── ImageGeneration/   # 图像生成
│   │   ├── KnowledgeQA/       # 知识问答
│   │   ├── TextGeneration/    # 文本生成
│   │   ├── Translation/       # 智能翻译
│   │   ├── InfoQuery/         # 信息查询
│   │   ├── EmotionalCompanion/# 情感陪伴
│   │   ├── Recommendation/    # 智能推荐
│   │   ├── GameEntertainment/ # 游戏娱乐
│   │   └── HealthManagement/  # 健康管理
│   ├── services/              # API服务
│   │   └── aiService.ts       # AI服务接口
│   ├── router/                # 路由配置
│   │   └── index.ts
│   ├── styles/                # 全局样式
│   │   └── index.less
│   ├── App.vue                # 根组件
│   ├── main.ts                # 入口文件
│   └── env.d.ts               # 类型声明
├── package.json               # 项目配置
├── vite.config.ts             # Vite配置
├── tsconfig.json              # TypeScript配置
└── index.html                 # HTML模板
```

## 🔧 配置说明

### 后端服务配置
在 `vite.config.ts` 中配置后端代理：

```typescript
server: {
  port: 5175,
  proxy: {
    '/api': {
      target: 'http://localhost:8082',
      changeOrigin: true,
      secure: false,
    }
  }
}
```

### 语音通话配置
语音通话功能需要配置真实的智能体ID和认证信息：

```typescript
// 在 VoiceCall/index.vue 中
await callEngine.start({
  agentType: AICallAgentType.VoiceAgent,
  region: 'cn-shanghai',
  userId: `user_${Date.now()}`,
  userToken: 'your_real_token', // 需要从服务器获取
  agentId: 'your_agent_id'      // 需要配置真实的智能体ID
})
```

## 🎯 路由说明

| 路径 | 组件 | 功能 |
|------|------|------|
| `/` | HomePage | 主页 |
| `/voice-call` | VoiceCall | 语音通话 |
| `/image-generation` | ImageGeneration | 图像生成 |
| `/knowledge-qa` | KnowledgeQA | 知识问答 |
| `/text-generation` | TextGeneration | 文本生成 |
| `/translation` | Translation | 智能翻译 |
| `/info-query` | InfoQuery | 信息查询 |
| `/emotional-companion` | EmotionalCompanion | 情感陪伴 |
| `/recommendation` | Recommendation | 智能推荐 |
| `/game-entertainment` | GameEntertainment | 游戏娱乐 |
| `/health-management` | HealthManagement | 健康管理 |

## 🔌 API接口

所有AI功能都通过 `aiService` 调用后端API：

```typescript
// 知识问答
await aiService.knowledgeQA(userId, question)

// 图像生成
await aiService.imageGeneration(userId, prompt)

// 文本生成
await aiService.textGeneration(userId, prompt)

// 智能翻译
await aiService.translation(userId, text)
```

## 🎨 样式定制

项目使用Less作为CSS预处理器，支持主题定制：

```less
// 在 styles/index.less 中定义全局样式
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

## 📱 移动端适配

项目完全支持移动端，使用Vant组件库确保良好的移动端体验：

- 响应式布局
- 触摸友好的交互
- 移动端优化的组件
- 适配不同屏幕尺寸

## 🔍 开发调试

### 开发工具
- Vue DevTools - Vue组件调试
- Vite DevTools - 构建工具调试
- TypeScript - 类型检查

### 日志调试
```typescript
// 在组件中添加调试日志
console.log('API响应:', response)
```

## 🚀 部署说明

### 构建生产版本
```bash
npm run build
```

### 部署到服务器
将 `dist` 目录部署到Web服务器即可。

### 环境变量
可以通过环境变量配置不同环境的API地址：

```bash
# .env.production
VITE_API_BASE_URL=https://your-api-server.com
```

## 🤝 与React版本的对比

| 特性 | React版本 | Vue3版本 |
|------|-----------|----------|
| 框架 | React 18 | Vue 3 |
| 状态管理 | Zustand | Pinia |
| UI组件 | Antd Mobile | Vant |
| 路由 | 手动管理 | Vue Router |
| 构建工具 | Vite | Vite |
| 语言 | TypeScript | TypeScript |

## 📄 许可证

本项目采用 MIT 许可证。
