import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5176,
    historyApiFallback: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8082',
        changeOrigin: true,
        secure: false,
        timeout: 180000, // 3分钟超时
        proxyTimeout: 180000, // 代理超时
      },
      '/ai': {
        target: 'http://localhost:8082',
        changeOrigin: true,
        secure: false,
        timeout: 180000, // 3分钟超时
        proxyTimeout: 180000, // 代理超时
      },
    },
  },
  build: {
    target: 'es2015',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          vant: ['vant'],
          aicall: ['aliyun-auikit-aicall']
        }
      }
    }
  }
})
