package com.zhentao.studyim.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 系统配置类
 * 
 * 统一管理系统的各项配置参数：
 * - 消息限流配置
 * - 缓存配置
 * - 在线状态配置
 * - 定时任务配置
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-22
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "system")
public class SystemConfig {

    /**
     * 消息限流配置
     */
    private RateLimit rateLimit = new RateLimit();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 在线状态配置
     */
    private OnlineStatus onlineStatus = new OnlineStatus();

    /**
     * 定时任务配置
     */
    private Schedule schedule = new Schedule();

    @Data
    public static class RateLimit {
        /**
         * 每分钟最大消息数
         */
        private int messagePerMinute = 30;
    }

    @Data
    public static class Cache {
        /**
         * 最近消息缓存数量
         */
        private int recentMessageSize = 50;

        /**
         * 最近消息过期天数
         */
        private int recentMessageExpireDays = 7;
    }

    @Data
    public static class OnlineStatus {
        /**
         * 在线状态过期小时数
         */
        private int expireHours = 24;
    }

    @Data
    public static class Schedule {
        /**
         * 在线统计更新间隔（分钟）
         */
        private int onlineStatsUpdateMinutes = 5;

        /**
         * 清理过期数据间隔（小时）
         */
        private int cleanupExpiredDataHours = 1;
    }
}
