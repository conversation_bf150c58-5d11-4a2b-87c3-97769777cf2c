package cn.zhentao.controller;

import cn.zhentao.pojo.User;
import cn.zhentao.pojo.Role;
import cn.zhentao.service.UserService;
import cn.zhentao.service.RoleService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserService userService;
    
    @Autowired
    private RoleService roleService;

    @GetMapping("/list")
    public Map<String, Object> getUserList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String status) {
        
        Page<User> page = new Page<>(pageNum, pageSize);
        IPage<User> userPage = userService.getUserPage(page, username, status);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", userPage);
        return result;
    }

    @GetMapping("/{userId}")
    public Map<String, Object> getUserById(@PathVariable Long userId) {
        User user = userService.getById(userId);
        Map<String, Object> result = new HashMap<>();
        if (user == null) {
            result.put("code", 404);
            result.put("message", "用户不存在");
        } else {
            result.put("code", 200);
            result.put("data", user);
        }
        return result;
    }

    @PostMapping
    public Map<String, Object> addUser(@RequestBody User user) {
        if (userService.getUserByUsername(user.getUsername()) != null) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 409);
            result.put("message", "用户名已存在");
            return result;
        }
        
        boolean success = userService.save(user);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "添加成功");
        } else {
            result.put("code", 500);
            result.put("message", "添加失败");
        }
        return result;
    }

    @PutMapping
    public Map<String, Object> updateUser(@RequestBody User user) {
        boolean success = userService.updateById(user);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "修改成功");
        } else {
            result.put("code", 500);
            result.put("message", "修改失败");
        }
        return result;
    }

    @DeleteMapping("/{userId}")
    public Map<String, Object> deleteUser(@PathVariable Long userId) {
        boolean success = userService.removeById(userId);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "删除成功");
        } else {
            result.put("code", 500);
            result.put("message", "删除失败");
        }
        return result;
    }

    @GetMapping("/{userId}/roles")
    public Map<String, Object> getUserRoles(@PathVariable Long userId) {
        List<Role> roles = userService.getUserRoles(userId);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", roles);
        return result;
    }

    @PutMapping("/{userId}/roles")
    public Map<String, Object> assignRolesToUser(@PathVariable Long userId, @RequestBody List<Long> roleIds) {
        boolean success = userService.assignRolesToUser(userId, roleIds);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "角色分配成功");
        } else {
            result.put("code", 500);
            result.put("message", "角色分配失败");
        }
        return result;
    }
} 