package cn.zhentao.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Configuration
public class DatabaseConfig {
    
    // 移除@Value注解，完全硬编码MySQL配置

    @Bean
    @Primary
    @ConditionalOnMissingBean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        // 完全硬编码MySQL配置，不依赖任何配置文件
        config.setJdbcUrl("****************************************************************************************************************************************************");
        config.setUsername("root");
        config.setPassword("Sunshuo0818");
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // Hikari连接池配置
        config.setMaximumPoolSize(20);
        config.setMinimumIdle(5);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        // MySQL特定配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        
        System.out.println("✅ 创建MySQL数据源: **************************************");
        System.out.println("✅ 驱动类: com.mysql.cj.jdbc.Driver");
        System.out.println("✅ 用户名: root");
        return new HikariDataSource(config);
    }
} 