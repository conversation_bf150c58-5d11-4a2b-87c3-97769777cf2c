<template>
  <div class="translation-page">
    <van-nav-bar
      title="语言翻译"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
      class="custom-nav-bar"
    />

    <div class="feature-intro">
      <div class="intro-content">
        <div class="intro-icon">🌐</div>
        <div class="intro-text">
          <h3>AI智能翻译</h3>
          <p>多语言即时翻译，准确流畅</p>
        </div>
      </div>
    </div>

    <div class="translation-container">
      <div class="input-section">
        <van-field
          v-model="inputText"
          placeholder="请输入要翻译的文本..."
          type="textarea"
          rows="4"
          autosize
        />
        <div class="language-selector">
          <van-button size="small" @click="swapLanguages">
            {{ fromLang }} → {{ toLang }} 🔄
          </van-button>
        </div>
        <van-button 
          type="primary" 
          block
          :loading="isLoading"
          @click="translate"
          :disabled="!inputText.trim()"
        >
          翻译
        </van-button>
      </div>

      <div v-if="result" class="result-section">
        <div class="result-text">{{ result }}</div>
        <van-button size="small" @click="copyResult">复制结果</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'
import aiService from '@/services/aiService'

const inputText = ref('')
const result = ref('')
const isLoading = ref(false)
const fromLang = ref('中文')
const toLang = ref('英文')

const translate = async () => {
  if (!inputText.value.trim()) return
  
  isLoading.value = true
  try {
    const response = await aiService.translation(12345, inputText.value, fromLang.value, toLang.value)
    if (response.code === 200) {
      result.value = response.data
    }
  } catch (error) {
    showToast('翻译失败')
  } finally {
    isLoading.value = false
  }
}

const swapLanguages = () => {
  [fromLang.value, toLang.value] = [toLang.value, fromLang.value]
}

const copyResult = async () => {
  try {
    await navigator.clipboard.writeText(result.value)
    showToast('已复制')
  } catch {
    showToast('复制失败')
  }
}
</script>

<style scoped>
.translation-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f12711 0%, #f5af19 100%);
}

.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.feature-intro {
  margin: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.intro-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(241, 39, 17, 0.1);
  border-radius: 16px;
}

.intro-text h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.intro-text p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.translation-container {
  padding: 16px;
}

.input-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
}

.language-selector {
  text-align: center;
  margin: 16px 0;
}

.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.result-text {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}
</style>
