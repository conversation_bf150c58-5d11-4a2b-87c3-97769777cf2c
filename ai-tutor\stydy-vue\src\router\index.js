import { createRouter, createWebHistory } from 'vue-router'


const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../components/login.vue'),
    },
    {
      path: '/main',
      name: 'main',
      component: () => import('../components/main.vue'),
      children: [
        {
          path: '/main/shou',
          name: 'shou',
          component: () => import('../components/shou.vue'),
        },
        {
          path: '/main/user',
          name: 'user',
          component: () => import('../components/user.vue'),
        },
        {
          path: '/main/menu',
          name: 'menu',
          component: () => import('../components/menu.vue'),
        },
        {
          path: '/main/role',
          name: 'role',
          component: () => import('../components/role.vue'),
        },
        {
          path: '/main/permission',
          name: 'permission',
          component: () => import('../components/permission.vue'),
        },
        {
          // 默认重定向到首页
          path: '',
          name: 'dashboard',
          redirect: '/main/shou'
        }
      ]
    },
  ],
})

export default router
