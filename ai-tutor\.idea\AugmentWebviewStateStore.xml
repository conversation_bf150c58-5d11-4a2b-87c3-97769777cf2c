<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;c4dab56e-ffe5-4293-998a-21a1119238c3&quot;,&quot;conversations&quot;:{&quot;5a6d0160-a7f2-41fa-bbd6-c5e07acd8dd0&quot;:{&quot;id&quot;:&quot;5a6d0160-a7f2-41fa-bbd6-c5e07acd8dd0&quot;,&quot;createdAtIso&quot;:&quot;2025-08-07T01:09:38.178Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-07T01:09:38.178Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;4241f3a7-c150-4c1a-8ca1-dda3769ea8c7&quot;},&quot;c4dab56e-ffe5-4293-998a-21a1119238c3&quot;:{&quot;id&quot;:&quot;c4dab56e-ffe5-4293-998a-21a1119238c3&quot;,&quot;createdAtIso&quot;:&quot;2025-08-07T01:09:43.125Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-07T01:09:51.242Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;9c1d649a-b77a-4baa-b709-b7c153b2ffc6&quot;,&quot;request_message&quot;:&quot;你好&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;你好&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;**⚠️ Your subscription <NAME_EMAIL> is inactive. Please update your plan [here](https://app.augmentcode.com/account?utm_source=vscode&amp;utm_medium=in_agent_conversation&amp;utm_campaign=inactive_subscription_reminder&amp;utm_content=here) to continue using Augment. ⚠️**&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;你好&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;E:/wolk/study/ai-tutor&quot;,&quot;folderRoot&quot;:&quot;E:/wolk/study/ai-tutor&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-07T01:09:51.247Z&quot;,&quot;response_text&quot;:&quot;**⚠️ Your subscription <NAME_EMAIL> is inactive. Please update your plan [here](https://app.augmentcode.com/account?utm_source=vscode&amp;utm_medium=in_agent_conversation&amp;utm_campaign=inactive_subscription_reminder&amp;utm_content=here) to continue using Augment. ⚠️**&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-6116795d-1825-4c21-93ab-45095a1e6fbf&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;3523df34-07c1-41ca-b109-3457cec6fba1&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>