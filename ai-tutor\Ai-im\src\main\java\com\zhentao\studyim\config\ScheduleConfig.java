package com.zhentao.studyim.config;

import com.zhentao.studyim.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务配置
 * 定期执行系统维护任务
 */
@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class ScheduleConfig {

    private final RedisService redisService;

    /**
     * 每5分钟更新一次在线用户统计
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void updateOnlineUserStats() {
        try {
            redisService.updateOnlineUserCount();
            log.debug("在线用户统计已更新");
        } catch (Exception e) {
            log.error("更新在线用户统计失败: {}", e.getMessage());
        }
    }

    /**
     * 每小时清理一次过期数据
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanExpiredData() {
        try {
            redisService.cleanExpiredData();
            log.info("过期数据清理完成");
        } catch (Exception e) {
            log.error("清理过期数据失败: {}", e.getMessage());
        }
    }

    /**
     * 每天凌晨2点执行系统维护
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyMaintenance() {
        try {
            log.info("开始执行每日系统维护...");
            
            // 清理过期数据
            redisService.cleanExpiredData();
            
            // 更新统计信息
            redisService.updateOnlineUserCount();
            
            log.info("每日系统维护完成");
        } catch (Exception e) {
            log.error("每日系统维护失败: {}", e.getMessage());
        }
    }
}
