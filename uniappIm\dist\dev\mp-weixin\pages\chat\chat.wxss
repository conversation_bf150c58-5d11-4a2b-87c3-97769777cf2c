
.chat-container.data-v-a041b13f {
  height: 100vh;
  background: #f5f5f5;
}
.header.data-v-a041b13f {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}
.header-content.data-v-a041b13f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  height: 88rpx;
}
.title.data-v-a041b13f {
  color: white;
  font-size: 36rpx;
  font-weight: 500;
}
.add-icon.data-v-a041b13f {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}
.icon.data-v-a041b13f {
  color: white;
  font-size: 32rpx;
}
.chat-list.data-v-a041b13f {
  margin-top: 108rpx;
  padding-top: 30rpx; /* 增加顶部内边距 */
  height: calc(100vh - 108rpx - 100rpx - 30rpx); /* 调整高度以适应内边距 */
  background: white;
  padding-bottom: 20rpx;
  /* 优化滚动性能 */
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;
  contain: layout style paint;
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  /* 优化渲染性能 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
.chat-item.data-v-a041b13f {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
  /* 优化渲染性能 */
  contain: layout style;
  will-change: auto;
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}
.avatar.data-v-a041b13f {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.avatar-text.data-v-a041b13f {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

/* 未读消息红点 */
.unread-badge.data-v-a041b13f {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 36rpx;
  height: 36rpx;
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
  -webkit-animation: pulse-red-a041b13f 2s infinite;
          animation: pulse-red-a041b13f 2s infinite;
}
.unread-count.data-v-a041b13f {
  color: white;
  font-size: 20rpx;
  font-weight: 700;
  line-height: 1;
  padding: 0 6rpx;
}
@-webkit-keyframes pulse-red-a041b13f {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.6);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
}
@keyframes pulse-red-a041b13f {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
50% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.6);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.4);
}
}
.chat-content.data-v-a041b13f {
  flex: 1;
}
.chat-header.data-v-a041b13f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.friend-name.data-v-a041b13f {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.chat-time.data-v-a041b13f {
  font-size: 24rpx;
  color: #999;
}
.message-row.data-v-a041b13f {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.last-message.data-v-a041b13f {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.unread-message.data-v-a041b13f {
  color: #333;
  font-weight: 600;
}
.mute-icon.data-v-a041b13f {
  margin-left: 16rpx;
  font-size: 24rpx;
  opacity: 0.6;
}
.empty-state.data-v-a041b13f {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
.add-menu-modal.data-v-a041b13f {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 108rpx 30rpx 0 0;
}
.add-menu.data-v-a041b13f {
  background: #2c2c2c;
  border-radius: 12rpx;
  overflow: hidden;
  min-width: 240rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  -webkit-animation: slideIn-a041b13f 0.2s ease-out;
          animation: slideIn-a041b13f 0.2s ease-out;
}
@-webkit-keyframes slideIn-a041b13f {
from {
    opacity: 0;
    -webkit-transform: translateY(-20rpx) scale(0.95);
            transform: translateY(-20rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
@keyframes slideIn-a041b13f {
from {
    opacity: 0;
    -webkit-transform: translateY(-20rpx) scale(0.95);
            transform: translateY(-20rpx) scale(0.95);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0) scale(1);
            transform: translateY(0) scale(1);
}
}
.add-menu-item.data-v-a041b13f {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.08);
  transition: background-color 0.2s ease;
}
.add-menu-item.data-v-a041b13f:last-child {
  border-bottom: none;
}
.add-menu-item.data-v-a041b13f:active {
  background: rgba(255, 255, 255, 0.1);
}
.menu-item-content.data-v-a041b13f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 24rpx;
}
.add-menu-icon.data-v-a041b13f {
  font-size: 36rpx;
  color: #ffffff;
  width: 40rpx;
  text-align: center;
}
.add-menu-text.data-v-a041b13f {
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 400;
  flex: 1;
}

/* 加载状态 */
.loading-state.data-v-a041b13f {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}

/* 空状态样式 */
.empty-state.data-v-a041b13f {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}
.empty-text.data-v-a041b13f {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  display: block;
}
.empty-hint.data-v-a041b13f {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}
.badge.data-v-a041b13f {
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 24rpx;
  min-width: 36rpx;
  text-align: center;
  line-height: 1;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}
.notification-badge.data-v-a041b13f {
  position: absolute;
  top: -5rpx;
  right: 10rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
  min-width: 24rpx;
  text-align: center;
  line-height: 1.2;
}


