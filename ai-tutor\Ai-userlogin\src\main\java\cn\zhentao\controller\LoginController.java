package cn.zhentao.controller;

import cn.zhentao.common.Result;
import cn.zhentao.pojo.User;
import cn.zhentao.service.UserService;
import cn.zhentao.util.HttpUtils;
import jakarta.servlet.http.HttpSession;
import org.apache.http.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/login")
public class LoginController {
    @Autowired
    private UserService userService;

    @Autowired(required = false)
    private PasswordEncoder passwordEncoder;

    // 阿里云短信服务配置
    private static final String HOST = "https://gyytz.market.alicloudapi.com";
    private static final String PATH = "/sms/smsSend";
    private static final String METHOD = "POST";
    private static final String APPCODE = "b01a78ea776f451091a4e9ef78ff047b";
    private static final String SMS_SIGN_ID = "2e65b1bb3d054466b82f0c9d125465e2";
    private static final String TEMPLATE_ID = "908e94ccf08b4476ba6c876d13f084ad";

    @PostMapping
    public Map<String, Object> login(@RequestBody User loginUser) {
        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (loginUser.getUsername() == null || loginUser.getPassword() == null) {
            result.put("code", 400);
            result.put("message", "用户名和密码不能为空");
            return result;
        }

        // 查询用户
        User user = userService.getUserByUsername(loginUser.getUsername());
        if (user == null) {
            result.put("code", 401);
            result.put("message", "用户不存在");
            return result;
        }

        // 验证密码
        boolean passwordMatch = false;
        if (passwordEncoder != null) {
            passwordMatch = passwordEncoder.matches(loginUser.getPassword(), user.getPassword());
        } else {
            passwordMatch = loginUser.getPassword().equals(user.getPassword());
        }

        if (!passwordMatch) {
            result.put("code", 401);
            result.put("message", "密码错误");
            return result;
        }

        // 生成简单的mock token
        String jwtToken = "mock-jwt-token-" + user.getUsername() + "-" + System.currentTimeMillis();

        // 构建返回数据
        result.put("code", 200);
        result.put("message", "登录成功");
        Map<String, Object> data = new HashMap<>();
        data.put("token", jwtToken);
        user.setPassword(null);
        data.put("user", user);
        result.put("data", data);

        return result;
    }

    // 发送手机验证码
    @PostMapping("/code")
    public Result sendCode(@RequestParam("phone") String phonenumber, HttpSession session) {
        // 1.校验手机号
        if (!isValidPhoneNumber(phonenumber)) {
            return Result.error("手机号格式不正确");
        }

        // 2.生成验证码
        String code = generateVerificationCode();

        try {
            // 3.调用阿里云API发送验证码
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "APPCODE " + APPCODE);

            Map<String, String> querys = new HashMap<>();
            querys.put("mobile", phonenumber);
            querys.put("param", "**code**:" + code + ",**minute**:5");
            querys.put("smsSignId", SMS_SIGN_ID);
            querys.put("templateId", TEMPLATE_ID);

            Map<String, String> bodys = new HashMap<>();

            HttpResponse response = HttpUtils.doPost(HOST, PATH, METHOD, headers, querys, bodys);

            if (response.getStatusLine().getStatusCode() == 200) {
                // 4.将验证码保存到session
                session.setAttribute("verifyCode:" + phonenumber, code);
                session.setMaxInactiveInterval(300); // 5分钟有效期
                return Result.success("验证码发送成功");
            } else {
                return Result.error("验证码发送失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("验证码发送失败: " + e.getMessage());
        }
    }

    // 手机验证码登录
    @PostMapping("/phone")
    public Map<String, Object> phoneLogin(@RequestParam("phone") String phone,
                                          @RequestParam("code") String code,
                                          HttpSession session) {
        Map<String, Object> result = new HashMap<>();

        // 1.验证码校验
        String savedCode = (String) session.getAttribute("verifyCode:" + phone);
        if (savedCode == null || !savedCode.equals(code)) {
            result.put("code", 401);
            result.put("message", "验证码错误或已过期");
            return result;
        }

        // 2.查询用户
        User user = userService.getUserByPhone(phone);
        if (user == null) {
            result.put("code", 401);
            result.put("message", "该手机号未注册");
            return result;
        }

        // 3.生成token
        String jwtToken = "mock-jwt-token-" + user.getUsername() + "-" + System.currentTimeMillis();

        // 4.清除验证码
        session.removeAttribute("verifyCode:" + phone);

        // 5.构建返回数据
        result.put("code", 200);
        result.put("message", "登录成功");
        Map<String, Object> data = new HashMap<>();
        data.put("token", jwtToken);
        user.setPassword(null);
        data.put("user", user);
        result.put("data", data);

        return result;
    }

    // 生成6位随机验证码
    private String generateVerificationCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    // 校验手机号格式
    private boolean isValidPhoneNumber(String phone) {
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }

    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody User user) {
        Map<String, Object> result = new HashMap<>();

        if (user.getUsername() == null || user.getPassword() == null) {
            result.put("code", 400);
            result.put("message", "用户名和密码不能为空");
            return result;
        }

        if (userService.getUserByUsername(user.getUsername()) != null) {
            result.put("code", 409);
            result.put("message", "用户名已存在");
            return result;
        }

        // 密码加密
        if (passwordEncoder != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        // 设置默认值
        if (user.getStatus() == null) user.setStatus(0);
        if (user.getDelFlag() == null) user.setDelFlag("0");

        boolean success = userService.save(user);
        if (success) {
            result.put("code", 200);
            result.put("message", "注册成功");
        } else {
            result.put("code", 500);
            result.put("message", "注册失败");
        }
        return result;
    }

    @PostMapping("/reset-test-passwords")
    public Map<String, Object> resetTestPasswords() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 重置测试用户密码为 123456
            String[] usernames = {"admin", "teacher", "student"};
            String newPassword = "123456";

            for (String username : usernames) {
                User user = userService.getUserByUsername(username);
                if (user != null) {
                    if (passwordEncoder != null) {
                        user.setPassword(passwordEncoder.encode(newPassword));
                    } else {
                        user.setPassword(newPassword);
                    }
                    userService.updateById(user);
                }
            }

            result.put("code", 200);
            result.put("message", "密码重置成功，所有测试用户密码已设置为: 123456");
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "密码重置失败: " + e.getMessage());
        }

        return result;
    }
}
