package cn.zhentao.mapper;

import cn.zhentao.entity.MemberRecharge;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员充值记录Mapper接口
 */
@Mapper
public interface MemberRechargeMapper extends BaseMapper<MemberRecharge> {

    /**
     * 分页查询用户充值记录
     * @param page 分页参数
     * @param userId 用户ID
     * @param payStatus 支付状态
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT * FROM member_recharge " +
            "WHERE 1=1 " +
            "<if test='userId != null'> AND user_id = #{userId} </if>" +
            "<if test='payStatus != null'> AND pay_status = #{payStatus} </if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<MemberRecharge> selectRechargePageByCondition(
            Page<MemberRecharge> page,
            @Param("userId") Long userId,
            @Param("payStatus") Integer payStatus
    );

    /**
     * 根据交易号查询充值记录
     * @param tradeNo 交易号
     * @return 充值记录
     */
    @Select("SELECT * FROM member_recharge WHERE trade_no = #{tradeNo}")
    MemberRecharge selectByTradeNo(@Param("tradeNo") String tradeNo);

    /**
     * 查询用户最新的有效会员记录
     * @param userId 用户ID
     * @return 会员记录
     */
    @Select("SELECT * FROM member_recharge " +
            "WHERE user_id = #{userId} AND pay_status = 1 " +
            "AND member_end_time > NOW() " +
            "ORDER BY member_end_time DESC LIMIT 1")
    MemberRecharge selectLatestValidMember(@Param("userId") Long userId);

    /**
     * 查询指定时间范围内的充值统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 充值记录列表
     */
    @Select("SELECT * FROM member_recharge " +
            "WHERE pay_status = 1 " +
            "AND pay_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY pay_time DESC")
    List<MemberRecharge> selectRechargeByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询用户充值总金额
     * @param userId 用户ID
     * @return 总金额
     */
    @Select("SELECT COALESCE(SUM(recharge_amount), 0) FROM member_recharge " +
            "WHERE user_id = #{userId} AND pay_status = 1")
    Double selectTotalRechargeAmount(@Param("userId") Long userId);

    /**
     * 查询即将到期的会员（7天内到期）
     * @return 即将到期的会员记录
     */
    @Select("SELECT * FROM member_recharge " +
            "WHERE pay_status = 1 " +
            "AND member_end_time > NOW() " +
            "AND member_end_time <= DATE_ADD(NOW(), INTERVAL 7 DAY) " +
            "ORDER BY member_end_time ASC")
    List<MemberRecharge> selectExpiringMembers();
}
