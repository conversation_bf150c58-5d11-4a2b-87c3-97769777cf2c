
.login-container.data-v-cdfe2409 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx;
  position: relative;
  overflow: hidden;
}
.login-container.data-v-cdfe2409::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}
.login-header.data-v-cdfe2409 {
  text-align: center;
  margin-bottom: 80rpx;
  position: relative;
  z-index: 2;
}
.logo-section.data-v-cdfe2409 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}
.logo-container.data-v-cdfe2409 {
  position: relative;
  margin-bottom: 30rpx;
}
.logo-icon.data-v-cdfe2409 {
  font-size: 120rpx;
  position: relative;
  z-index: 2;
  -webkit-animation: logoFloat-cdfe2409 3s ease-in-out infinite;
          animation: logoFloat-cdfe2409 3s ease-in-out infinite;
}
.logo-glow.data-v-cdfe2409 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 160rpx;
  height: 160rpx;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
  border-radius: 50%;
  -webkit-animation: glow-cdfe2409 2s ease-in-out infinite alternate;
          animation: glow-cdfe2409 2s ease-in-out infinite alternate;
}
.brand-name.data-v-cdfe2409 {
  font-size: 64rpx;
  font-weight: 800;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0,0,0,0.3);
  letter-spacing: 4rpx;
}
.brand-slogan.data-v-cdfe2409 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 2rpx;
}
.floating-elements.data-v-cdfe2409 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.float-item.data-v-cdfe2409 {
  position: absolute;
  font-size: 32rpx;
  color: rgba(255,255,255,0.6);
  -webkit-animation: float-cdfe2409 4s ease-in-out infinite;
          animation: float-cdfe2409 4s ease-in-out infinite;
}
.float-1.data-v-cdfe2409 {
  top: 20%;
  left: 15%;
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.float-2.data-v-cdfe2409 {
  top: 30%;
  right: 20%;
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
}
.float-3.data-v-cdfe2409 {
  top: 60%;
  left: 10%;
  -webkit-animation-delay: 2s;
          animation-delay: 2s;
}
@-webkit-keyframes logoFloat-cdfe2409 {
0%, 100% { -webkit-transform: translateY(0); transform: translateY(0);
}
50% { -webkit-transform: translateY(-10rpx); transform: translateY(-10rpx);
}
}
@keyframes logoFloat-cdfe2409 {
0%, 100% { -webkit-transform: translateY(0); transform: translateY(0);
}
50% { -webkit-transform: translateY(-10rpx); transform: translateY(-10rpx);
}
}
@-webkit-keyframes glow-cdfe2409 {
0% { opacity: 0.5; -webkit-transform: translate(-50%, -50%) scale(1); transform: translate(-50%, -50%) scale(1);
}
100% { opacity: 0.8; -webkit-transform: translate(-50%, -50%) scale(1.1); transform: translate(-50%, -50%) scale(1.1);
}
}
@keyframes glow-cdfe2409 {
0% { opacity: 0.5; -webkit-transform: translate(-50%, -50%) scale(1); transform: translate(-50%, -50%) scale(1);
}
100% { opacity: 0.8; -webkit-transform: translate(-50%, -50%) scale(1.1); transform: translate(-50%, -50%) scale(1.1);
}
}
@-webkit-keyframes float-cdfe2409 {
0%, 100% { -webkit-transform: translateY(0) rotate(0deg); transform: translateY(0) rotate(0deg); opacity: 0.6;
}
50% { -webkit-transform: translateY(-20rpx) rotate(180deg); transform: translateY(-20rpx) rotate(180deg); opacity: 1;
}
}
@keyframes float-cdfe2409 {
0%, 100% { -webkit-transform: translateY(0) rotate(0deg); transform: translateY(0) rotate(0deg); opacity: 0.6;
}
50% { -webkit-transform: translateY(-20rpx) rotate(180deg); transform: translateY(-20rpx) rotate(180deg); opacity: 1;
}
}
.login-form.data-v-cdfe2409 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}
.form-title.data-v-cdfe2409 {
  text-align: center;
  margin-bottom: 50rpx;
}
.welcome-text.data-v-cdfe2409 {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
}
.welcome-subtitle.data-v-cdfe2409 {
  display: block;
  font-size: 28rpx;
  color: #666;
  font-weight: 300;
}
.input-group.data-v-cdfe2409 {
  margin-bottom: 32rpx;
  position: relative;
}
.input-icon.data-v-cdfe2409 {
  position: absolute;
  left: 24rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  font-size: 32rpx;
  z-index: 2;
}
.input-field.data-v-cdfe2409 {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 0 24rpx 0 72rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background: rgba(248, 249, 250, 0.8);
  transition: all 0.3s ease;
}
.input-field.data-v-cdfe2409:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.login-btn.data-v-cdfe2409 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.login-btn.data-v-cdfe2409:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.login-btn.data-v-cdfe2409:disabled {
  opacity: 0.6;
  -webkit-transform: none;
          transform: none;
}
.btn-content.data-v-cdfe2409 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
.loading-icon.data-v-cdfe2409 {
  -webkit-animation: spin-cdfe2409 1s linear infinite;
          animation: spin-cdfe2409 1s linear infinite;
}
.divider.data-v-cdfe2409 {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}
.divider-line.data-v-cdfe2409 {
  flex: 1;
  height: 1rpx;
  background: #e8e8e8;
}
.divider-text.data-v-cdfe2409 {
  padding: 0 24rpx;
  color: #999;
  font-size: 24rpx;
}
.register-link.data-v-cdfe2409 {
  text-align: center;
}
.register-link text.data-v-cdfe2409 {
  color: #667eea;
  font-size: 28rpx;
  font-weight: 500;
  text-decoration: underline;
}
@-webkit-keyframes spin-cdfe2409 {
0% { -webkit-transform: rotate(0deg); transform: rotate(0deg);
}
100% { -webkit-transform: rotate(360deg); transform: rotate(360deg);
}
}
@keyframes spin-cdfe2409 {
0% { -webkit-transform: rotate(0deg); transform: rotate(0deg);
}
100% { -webkit-transform: rotate(360deg); transform: rotate(360deg);
}
}
.modal-overlay.data-v-cdfe2409 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-animation: fadeIn-cdfe2409 0.3s ease;
          animation: fadeIn-cdfe2409 0.3s ease;
}
.register-popup.data-v-cdfe2409 {
  width: 640rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 50rpx 40rpx;
  margin: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  -webkit-animation: slideUp-cdfe2409 0.3s ease;
          animation: slideUp-cdfe2409 0.3s ease;
}
.popup-header.data-v-cdfe2409 {
  text-align: center;
  margin-bottom: 50rpx;
}
.popup-icon.data-v-cdfe2409 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}
.popup-title.data-v-cdfe2409 {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.popup-subtitle.data-v-cdfe2409 {
  font-size: 26rpx;
  color: #666;
  display: block;
}
.popup-buttons.data-v-cdfe2409 {
  display: flex;
  gap: 20rpx;
  margin-top: 50rpx;
}
.cancel-btn.data-v-cdfe2409,
.confirm-btn.data-v-cdfe2409 {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}
.cancel-btn.data-v-cdfe2409 {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}
.cancel-btn.data-v-cdfe2409:active {
  background: #e9ecef;
}
.confirm-btn.data-v-cdfe2409 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}
.confirm-btn.data-v-cdfe2409:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
}
.confirm-icon.data-v-cdfe2409 {
  font-size: 24rpx;
}
@-webkit-keyframes fadeIn-cdfe2409 {
0% { opacity: 0;
}
100% { opacity: 1;
}
}
@keyframes fadeIn-cdfe2409 {
0% { opacity: 0;
}
100% { opacity: 1;
}
}
@-webkit-keyframes slideUp-cdfe2409 {
0% { -webkit-transform: translateY(60rpx); transform: translateY(60rpx); opacity: 0;
}
100% { -webkit-transform: translateY(0); transform: translateY(0); opacity: 1;
}
}
@keyframes slideUp-cdfe2409 {
0% { -webkit-transform: translateY(60rpx); transform: translateY(60rpx); opacity: 0;
}
100% { -webkit-transform: translateY(0); transform: translateY(0); opacity: 1;
}
}


