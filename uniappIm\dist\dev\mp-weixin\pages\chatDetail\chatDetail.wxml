<view class="chat-detail data-v-02c82538"><view class="chat-header data-v-02c82538"><view class="header-content data-v-02c82538"><view class="header-left data-v-02c82538" bindtap="{{a}}"><text class="back-icon data-v-02c82538">‹</text></view><view class="header-center data-v-02c82538"><text class="chat-title data-v-02c82538">{{b}}</text><text class="online-status data-v-02c82538">{{c}}</text></view><view class="header-right data-v-02c82538"><view class="more-icon data-v-02c82538" bindtap="{{d}}"><text class="data-v-02c82538">⋯</text></view></view></view></view><scroll-view class="message-list data-v-02c82538" scroll-y scroll-top="{{i}}" scroll-with-animation="{{false}}" bindscrolltoupper="{{j}}" enable-back-to-top="{{false}}" enhanced="{{true}}" bounces="{{true}}" show-scrollbar="{{false}}" scroll-anchoring="{{true}}" refresher-enabled="{{false}}" fast-deceleration="{{false}}"><view class="chat-background data-v-02c82538"></view><view wx:if="{{e}}" class="load-more data-v-02c82538"><view class="loading-indicator data-v-02c82538"><text class="loading-text data-v-02c82538">{{f}}</text></view></view><view wx:for="{{g}}" wx:for-item="message" wx:key="j" class="{{['message-item', 'data-v-02c82538', message.k && 'own-message']}}"><view wx:if="{{message.a}}" class="time-divider data-v-02c82538"><view class="time-label data-v-02c82538"><text class="time-text data-v-02c82538">{{message.b}}</text></view></view><view class="{{['message-wrapper', 'data-v-02c82538', message.e && 'own-message-wrapper']}}"><view class="{{['message-bubble', 'data-v-02c82538', message.d && 'own-bubble']}}"><view class="message-content data-v-02c82538"><text class="message-text data-v-02c82538">{{message.c}}</text></view></view></view><view wx:if="{{message.f}}" class="message-status own-status data-v-02c82538"><text class="message-time data-v-02c82538">{{message.g}}</text><view class="send-status data-v-02c82538"><text wx:if="{{message.h}}" class="status-sending data-v-02c82538">发送中</text><text wx:elif="{{message.i}}" class="status-failed data-v-02c82538">发送失败</text><text wx:else class="status-sent data-v-02c82538">✓</text></view></view></view><view wx:if="{{h}}" class="empty-messages data-v-02c82538"><view class="empty-icon data-v-02c82538">💬</view><text class="empty-text data-v-02c82538">暂无聊天记录</text><text class="empty-tip data-v-02c82538">开始你们的第一次对话吧</text></view></scroll-view><view class="{{['input-area', 'data-v-02c82538', M && 'panel-open']}}"><view class="input-wrapper data-v-02c82538"><view bindtap="{{l}}" class="{{['function-btn', 'data-v-02c82538', m && 'active']}}"><text class="function-icon data-v-02c82538">{{k}}</text></view><view class="input-container data-v-02c82538"><block wx:if="{{r0}}"><textarea placeholder="输入消息..." class="message-input data-v-02c82538" auto-height="{{true}}" maxlength="{{500}}" bindconfirm="{{n}}" bindfocus="{{o}}" bindblur="{{p}}" adjust-position="{{false}}" show-confirm-bar="{{false}}" value="{{q}}" bindinput="{{r}}"/></block></view><view bindtap="{{s}}" class="{{['function-btn', 'data-v-02c82538', t && 'active']}}"><text class="function-icon data-v-02c82538">😊</text></view><view wx:if="{{v}}" bindtap="{{x}}" class="{{['send-btn', 'data-v-02c82538', y && 'sending']}}"><text class="send-text data-v-02c82538">{{w}}</text></view><view wx:else class="voice-btn data-v-02c82538" bindtouchstart="{{z}}" bindtouchend="{{A}}"><text class="voice-icon data-v-02c82538">🎤</text></view></view><view wx:if="{{B}}" class="emoji-panel data-v-02c82538"><view class="emoji-header data-v-02c82538"><text class="emoji-title data-v-02c82538">表情</text></view><scroll-view class="emoji-scroll data-v-02c82538" scroll-y><view class="emoji-grid data-v-02c82538"><view wx:for="{{C}}" wx:for-item="emoji" wx:key="b" class="emoji-item data-v-02c82538" bindtap="{{emoji.c}}"><text class="emoji-text data-v-02c82538">{{emoji.a}}</text></view></view></scroll-view></view><view wx:if="{{D}}" class="more-panel data-v-02c82538"><view class="more-header data-v-02c82538"><text class="more-title data-v-02c82538">更多功能</text></view><view class="more-grid data-v-02c82538"><view class="more-item data-v-02c82538" bindtap="{{E}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">📷</text></view><text class="more-text data-v-02c82538">相册</text></view><view class="more-item data-v-02c82538" bindtap="{{F}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">📸</text></view><text class="more-text data-v-02c82538">拍照</text></view><view class="more-item data-v-02c82538" bindtap="{{G}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">🎥</text></view><text class="more-text data-v-02c82538">视频</text></view><view class="more-item data-v-02c82538" bindtap="{{H}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">📁</text></view><text class="more-text data-v-02c82538">文件</text></view><view class="more-item data-v-02c82538" bindtap="{{I}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">📍</text></view><text class="more-text data-v-02c82538">位置</text></view><view class="more-item data-v-02c82538" bindtap="{{J}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">🎤</text></view><text class="more-text data-v-02c82538">语音</text></view><view class="more-item data-v-02c82538" bindtap="{{K}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">🧧</text></view><text class="more-text data-v-02c82538">红包</text></view><view class="more-item data-v-02c82538" bindtap="{{L}}"><view class="more-icon-wrapper data-v-02c82538"><text class="more-icon data-v-02c82538">💰</text></view><text class="more-text data-v-02c82538">转账</text></view></view></view></view><view wx:if="{{N}}" class="chat-menu-modal data-v-02c82538" bindtap="{{S}}"><view class="chat-menu data-v-02c82538" catchtap="{{R}}"><view class="chat-menu-item data-v-02c82538" bindtap="{{O}}"><text class="menu-icon data-v-02c82538">👤</text><text class="menu-text data-v-02c82538">查看资料</text></view><view class="chat-menu-item data-v-02c82538" bindtap="{{P}}"><text class="menu-icon data-v-02c82538">🗑️</text><text class="menu-text data-v-02c82538">清空聊天记录</text></view><view class="chat-menu-item data-v-02c82538" bindtap="{{Q}}"><text class="menu-icon data-v-02c82538">🖼️</text><text class="menu-text data-v-02c82538">设置背景</text></view></view></view></view>