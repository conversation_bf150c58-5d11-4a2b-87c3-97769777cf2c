package cn.zhentao.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.util.Collections;

/**
 * MyBatis Plus 代码生成器
 * 
 * <AUTHOR>
 * @since 2024-07-28
 */
public class CodeGenerator {

    /**
     * 数据库连接配置
     */
    private static final String URL = "**************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "Sunshuo0818";

    /**
     * 包配置
     */
    private static final String PACKAGE_NAME = "cn.zhentao";
    private static final String MODULE_NAME = "ai-common";

    /**
     * 作者
     */
    private static final String AUTHOR = "zhentao";

    /**
     * 输出目录
     */
    private static final String OUTPUT_DIR = System.getProperty("user.dir") + "/Ai-common/src/main/java";

    public static void main(String[] args) {
        FastAutoGenerator.create(URL, USERNAME, PASSWORD)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author(AUTHOR) // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .fileOverride() // 覆盖已生成文件
                            .outputDir(OUTPUT_DIR) // 指定输出目录
                            .dateType(DateType.TIME_PACK) // 使用 java.time 包
                            .commentDate("yyyy-MM-dd"); // 注释日期格式
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent(PACKAGE_NAME) // 设置父包名
                            .moduleName(MODULE_NAME) // 设置父包模块名
                            .entity("pojo") // 设置实体类包名
                            .service("service") // 设置服务类包名
                            .serviceImpl("service.impl") // 设置服务实现类包名
                            .mapper("mapper") // 设置 Mapper 接口包名
                            .xml("mapper.xml") // 设置 Mapper XML 文件包名
                            .controller("controller") // 设置控制器包名
                            .pathInfo(Collections.singletonMap(OutputFile.xml, OUTPUT_DIR + "/../resources/mapper/")); // 设置 XML 文件输出目录
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude("user", "role", "permission", "user_role", "role_permission", "menu", "messages") // 设置需要生成的表名
                            .addTablePrefix("t_", "c_") // 设置过滤表前缀
                            // 实体策略配置
                            .entityBuilder()
                            .enableLombok() // 开启 lombok 模式
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .idType(IdType.AUTO) // 主键策略
                            .addTableFills(new Column("create_time", FieldFill.INSERT)) // 自动填充配置
                            .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("create_by", FieldFill.INSERT))
                            .addTableFills(new Column("update_by", FieldFill.INSERT_UPDATE))
                            .addTableFills(new Column("deleted", FieldFill.INSERT))
                            .addTableFills(new Column("version", FieldFill.INSERT))
                            .enableFileOverride() // 覆盖已生成文件
                            // 控制器策略配置
                            .controllerBuilder()
                            .enableRestStyle() // 开启生成@RestController 控制器
                            .enableHyphenStyle() // 开启驼峰转连字符
                            .enableFileOverride() // 覆盖已生成文件
                            // 服务策略配置
                            .serviceBuilder()
                            .formatServiceFileName("%sService") // 格式化 service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl") // 格式化 service 实现类文件名称
                            .enableFileOverride() // 覆盖已生成文件
                            // Mapper策略配置
                            .mapperBuilder()
                            .enableMapperAnnotation() // 开启 @Mapper 注解
                            .enableBaseResultMap() // 启用 BaseResultMap 生成
                            .enableBaseColumnList() // 启用 BaseColumnList
                            .enableFileOverride(); // 覆盖已生成文件
                })
                // 模板引擎配置，默认 Velocity 可选模板引擎 Beetl 或 Freemarker
                //.templateEngine(new BeetlTemplateEngine())
                //.templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
