package cn.zhentao.filter;

import cn.zhentao.service.impl.UserServiceImpl;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
public class TokenFilter extends OncePerRequestFilter {

    @Autowired(required = false)
    private UserServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String requestPath = request.getRequestURI();
        
        // 跳过登录和注册相关的路径
        if (requestPath.equals("/login") || requestPath.startsWith("/login/") ||
            requestPath.equals("/register") || requestPath.startsWith("/register/") ||
            requestPath.startsWith("/public/") || requestPath.startsWith("/actuator/") ||
            requestPath.startsWith("/h2-console/") || requestPath.equals("/error")) {
            filterChain.doFilter(request, response);
            return;
        }

        String authHeader = request.getHeader("Authorization");
        String token = null;
        String username = null;

        // 暂时跳过JWT验证，允许所有请求通过
        // TODO: 实现完整的JWT验证后启用
        /*
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
            // JWT解析逻辑
        }
        */

        filterChain.doFilter(request, response);
    }
} 