package cn.zhentao.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import javax.crypto.SecretKey;

/**
 * JWT工具类
 * 用于生成和验证JWT token
 */
@Component
public class JwtService {

    // 设置token过期时间30分钟
    private static final long EXPIRE_TIME = 30 * 60 * 1000;
    // 加密KEY
    private static final String TOKEN_SECRET = "MDk4ZjZi789y438h9jikog5tfr4dew7gg5ft4689iy6t5f4rd3y679uiy6t5f4ry9uyg5tf4rh9jikog5tf4rd38h9ujiyg5fr4d38h9juig5tf4rd3h9juiyg5ft4r9jigt5f4rY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjY";
    
    // 获取签名密钥
    private static SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(TOKEN_SECRET.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 生成TOKEN
     */
    public static String createToken(Map<String, Object> claimMaps) {
        long currentTime = System.currentTimeMillis();
        return Jwts.builder()
                .setId(UUID.randomUUID().toString())
                .setIssuedAt(new Date(currentTime)) // 签发时间
                .setSubject("zhentao.bms.token") // 说明
                .setIssuer("zhentao.bms") // 签发者信息
                .setAudience("zhentao.bms.pc.b") // 接收用户
                .signWith(getSigningKey(), SignatureAlgorithm.HS256) // 加密方式
                .setExpiration(new Date(currentTime + EXPIRE_TIME)) // 过期时间戳
                .addClaims(claimMaps) // cla信息
                .compact();
    }

    /**
     * 获取payload body信息
     */
    private static Claims getClaimsBody(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取payload中claims的集合
     */
    public static Map<String, Object> getClaimsMap(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            return new HashMap<>(claims);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取header body信息
     */
    public static JwsHeader getHeaderBody(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getHeader();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 是否有效
     * @param token
     * @return 1：有效，-3：token为空，-2：token过期，-1：token无效
     */
    public static int verifyToken(String token) {
        if(token == null || token.trim().isEmpty()){//token为空
            return -3;
        }
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token);
            return 1;
        } catch (ExpiredJwtException e) {//token过期
            return -2;
        } catch (JwtException e){//token被篡改
            return -1;
        }
    }

    /**
     * 验证token并获取用户ID
     * @param token JWT token
     * @return 用户ID，如果token无效返回-1
     */
    public static int getUserIdFromToken(String token) {
        try {
            Claims claims = getClaimsBody(token);
            if (claims != null) {
                Object userIdObj = claims.get("userId");
                if (userIdObj != null) {
                    return Integer.parseInt(userIdObj.toString());
                }
            }
        } catch (Exception e) {
            System.err.println("Error extracting userId from token: " + e.getMessage());
        }
        return -1;
    }

    /**
     * 从token中获取用户名
     */
    public static String getUsernameFromToken(String token) {
        try {
            Claims claims = getClaimsBody(token);
            if (claims != null) {
                return (String) claims.get("username");
            }
        } catch (Exception e) {
            System.err.println("Error extracting username from token: " + e.getMessage());
        }
        return null;
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("userId", "1");
        map.put("username", "admin");

        String token = createToken(map);
        System.out.println("Generated token: " + token);

        int i = verifyToken(token);
        System.out.println("Verify result: " + i);

        Map<String, Object> claimsMap = getClaimsMap(token);
        System.out.println("Claims: " + claimsMap);
    }
} 