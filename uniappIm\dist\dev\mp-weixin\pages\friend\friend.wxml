<view class="friend-container data-v-d48b96ff"><view class="header data-v-d48b96ff"><view class="header-content data-v-d48b96ff"><text class="title data-v-d48b96ff">好友管理</text></view></view><view class="tabs data-v-d48b96ff"><view wx:for="{{a}}" wx:for-item="tab" wx:key="d" class="{{['tab-item', 'data-v-d48b96ff', tab.e && 'active']}}" bindtap="{{tab.f}}"><text class="tab-text data-v-d48b96ff">{{tab.a}}</text><text wx:if="{{tab.b}}" class="tab-badge data-v-d48b96ff">{{tab.c}}</text></view></view><scroll-view class="content data-v-d48b96ff" scroll-y="true"><view wx:if="{{b}}" class="friend-list data-v-d48b96ff"><view class="search-section data-v-d48b96ff"><view class="search-box data-v-d48b96ff"><view class="search-icon data-v-d48b96ff">🔍</view><input class="search-input data-v-d48b96ff" placeholder="搜索好友（昵称/备注/用户名）" bindinput="{{c}}" value="{{d}}"/><view wx:if="{{e}}" class="clear-icon data-v-d48b96ff" bindtap="{{f}}"><text class="data-v-d48b96ff">×</text></view></view></view><view wx:for="{{g}}" wx:for-item="friend" wx:key="h" class="friend-item data-v-d48b96ff" bindtap="{{friend.i}}"><view class="avatar data-v-d48b96ff"><text class="avatar-text data-v-d48b96ff">{{friend.a}}</text></view><view class="friend-info data-v-d48b96ff"><text class="friend-name data-v-d48b96ff">{{friend.b}}</text><text class="friend-username data-v-d48b96ff">@{{friend.c}}</text><text wx:if="{{friend.d}}" class="friend-remark data-v-d48b96ff">备注: {{friend.e}}</text></view><view class="friend-actions data-v-d48b96ff"><view class="action-btn remark-btn data-v-d48b96ff" catchtap="{{friend.f}}"><view class="btn-icon data-v-d48b96ff"><text class="icon-text data-v-d48b96ff">备注</text></view></view><view class="action-btn delete-btn data-v-d48b96ff" catchtap="{{friend.g}}"><view class="btn-icon data-v-d48b96ff"><text class="icon-text data-v-d48b96ff">删除</text></view></view></view></view><view wx:if="{{h}}" class="empty-state data-v-d48b96ff"><text class="empty-text data-v-d48b96ff">{{i}}</text><text class="empty-hint data-v-d48b96ff">{{j}}</text></view></view><view wx:if="{{k}}" class="request-list data-v-d48b96ff"><view wx:for="{{l}}" wx:for-item="request" wx:key="g" class="request-item data-v-d48b96ff"><view class="avatar data-v-d48b96ff"><text class="avatar-text data-v-d48b96ff">{{request.a}}</text></view><view class="request-info data-v-d48b96ff"><text class="request-name data-v-d48b96ff">{{request.b}}</text><text class="request-message data-v-d48b96ff">{{request.c}}</text><text class="request-time data-v-d48b96ff">{{request.d}}</text></view><view class="request-actions data-v-d48b96ff"><view class="accept-btn data-v-d48b96ff" bindtap="{{request.e}}"><text class="data-v-d48b96ff">同意</text></view><view class="reject-btn data-v-d48b96ff" bindtap="{{request.f}}"><text class="data-v-d48b96ff">拒绝</text></view></view></view><view wx:if="{{m}}" class="empty-state data-v-d48b96ff"><text class="empty-text data-v-d48b96ff">暂无好友申请</text></view></view><view wx:if="{{n}}" class="request-list data-v-d48b96ff"><view wx:for="{{o}}" wx:for-item="request" wx:key="g" class="request-item data-v-d48b96ff"><view class="avatar data-v-d48b96ff"><text class="avatar-text data-v-d48b96ff">{{request.a}}</text></view><view class="request-info data-v-d48b96ff"><text class="request-name data-v-d48b96ff">{{request.b}}</text><text class="request-message data-v-d48b96ff">{{request.c}}</text><text class="request-time data-v-d48b96ff">{{request.d}}</text></view><view class="request-status data-v-d48b96ff"><text class="{{['status-text', 'data-v-d48b96ff', request.f]}}">{{request.e}}</text></view></view><view wx:if="{{p}}" class="empty-state data-v-d48b96ff"><text class="empty-text data-v-d48b96ff">暂无发出的申请</text></view></view></scroll-view><view wx:if="{{q}}" class="search-modal data-v-d48b96ff" bindtap="{{z}}"><view class="search-content data-v-d48b96ff" catchtap="{{y}}"><view class="search-header data-v-d48b96ff"><text class="search-title data-v-d48b96ff">搜索用户</text><view class="close-btn data-v-d48b96ff" bindtap="{{r}}"><text class="data-v-d48b96ff">✕</text></view></view><view class="search-input-group data-v-d48b96ff"><input placeholder="输入用户名或昵称" class="search-input data-v-d48b96ff" bindinput="{{s}}" value="{{t}}"/><view class="search-btn data-v-d48b96ff" bindtap="{{v}}"><text class="data-v-d48b96ff">搜索</text></view></view><scroll-view class="search-results data-v-d48b96ff" scroll-y="true"><view wx:for="{{w}}" wx:for-item="user" wx:key="e" class="search-item data-v-d48b96ff"><view class="avatar data-v-d48b96ff"><text class="avatar-text data-v-d48b96ff">{{user.a}}</text></view><view class="user-info data-v-d48b96ff"><text class="user-name data-v-d48b96ff">{{user.b}}</text><text class="user-username data-v-d48b96ff">@{{user.c}}</text></view><view class="add-btn data-v-d48b96ff" bindtap="{{user.d}}"><text class="data-v-d48b96ff">添加</text></view></view><view wx:if="{{x}}" class="empty-state data-v-d48b96ff"><text class="empty-text data-v-d48b96ff">未找到相关用户</text></view></scroll-view></view></view><view wx:if="{{A}}" class="modal-overlay data-v-d48b96ff" bindtap="{{O}}"><view class="remark-modal data-v-d48b96ff" catchtap="{{N}}"><view class="modal-header data-v-d48b96ff"><text class="modal-title data-v-d48b96ff">设置备注</text><view class="close-btn data-v-d48b96ff" bindtap="{{B}}"><text class="close-icon data-v-d48b96ff">×</text></view></view><view class="modal-content data-v-d48b96ff"><view class="friend-preview data-v-d48b96ff"><view class="preview-avatar data-v-d48b96ff"><text class="preview-avatar-text data-v-d48b96ff">{{C}}</text></view><view class="preview-info data-v-d48b96ff"><text class="preview-name data-v-d48b96ff">{{D}}</text><text class="preview-username data-v-d48b96ff">@{{E}}</text></view></view><view class="input-section data-v-d48b96ff"><text class="input-label data-v-d48b96ff">备注名称</text><block wx:if="{{r0}}"><textarea class="remark-input data-v-d48b96ff" placeholder="请输入备注名称" maxlength="20" auto-height show-confirm-bar="{{false}}" bindinput="{{F}}" bindfocus="{{G}}" bindblur="{{H}}" value="{{I}}"/></block></view></view><view class="modal-footer data-v-d48b96ff"><button class="cancel-btn data-v-d48b96ff" bindtap="{{J}}">取消</button><button class="confirm-btn data-v-d48b96ff" bindtap="{{L}}" disabled="{{M}}">{{K}}</button></view></view></view><tab-bar wx:if="{{Q}}" class="data-v-d48b96ff" bindchange="{{P}}" u-i="d48b96ff-0" bind:__l="__l" u-p="{{Q}}"/></view>