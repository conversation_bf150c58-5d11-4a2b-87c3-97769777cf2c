"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_websocket = require("../../utils/websocket.js");
const TabBar = () => "../../components/TabBar/TabBar.js";
const _sfc_main = {
  components: {
    TabBar
  },
  data() {
    return {
      userInfo: null,
      userList: [],
      showAddMenuModal: false,
      pendingRequestCount: 0,
      loading: false
    };
  },
  onLoad() {
    this.checkLogin();
    this.loadUserInfo();
    this.loadFriendList();
    this.loadPendingRequestCount();
    this.initWebSocket();
  },
  onUnload() {
    utils_websocket.closeWebSocket();
  },
  onShow() {
    this.loadFriendList();
    this.loadPendingRequestCount();
  },
  methods: {
    checkLogin() {
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        common_vendor.index.reLaunch({
          url: "/pages/login/login"
        });
      }
    },
    loadUserInfo() {
      this.userInfo = common_vendor.index.getStorageSync("userInfo");
    },
    /**
     * 加载好友列表
     */
    async loadFriendList() {
      this.loading = true;
      try {
        console.log("=== 开始加载好友列表 ===");
        const result = await utils_api.getFriendList();
        console.log("好友列表API响应:", result);
        if (result.code === 200) {
          const friendData = result.data || [];
          console.log("原始好友数据:", friendData);
          this.userList = friendData.map((user) => ({
            ...user,
            id: user.userId || user.id,
            userId: user.userId || user.id,
            nickname: user.nickname || user.username,
            username: user.username,
            remark: user.remark,
            // 保留备注字段
            lastMessage: "点击开始聊天",
            lastMessageTime: /* @__PURE__ */ new Date(),
            unreadCount: 0,
            // 初始化为0，后续通过API获取真实数据
            isMuted: false
          }));
          this.loadUnreadCounts();
          console.log("处理后的好友列表:", this.userList);
        } else {
          console.error("获取好友列表失败:", result.message);
          common_vendor.index.showToast({
            title: result.message || "获取好友列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取好友列表失败:", error);
        common_vendor.index.showToast({
          title: "网络错误: " + error.message,
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    /**
     * 加载待处理好友申请数量
     */
    async loadPendingRequestCount() {
      try {
        const result = await utils_api.getPendingRequestCount();
        if (result.code === 200) {
          this.pendingRequestCount = result.data || 0;
        }
      } catch (error) {
        console.error("获取待处理申请数量失败:", error);
      }
    },
    initWebSocket() {
      const token = common_vendor.index.getStorageSync("token");
      if (token) {
        utils_websocket.connectWebSocket(token, (message) => {
          console.log("收到消息:", message);
          if (message.type === "chat") {
            const userIndex = this.userList.findIndex(
              (user) => (user.userId || user.id) === message.fromUserId
            );
            if (userIndex !== -1) {
              const user = this.userList[userIndex];
              user.lastMessage = message.content;
              user.lastMessageTime = new Date(message.sendTime || /* @__PURE__ */ new Date());
              user.unreadCount = (user.unreadCount || 0) + 1;
              this.saveUnreadCount(user.userId || user.id, user.unreadCount);
              this.userList.splice(userIndex, 1);
              this.userList.unshift(user);
              const preview = message.content.length > 20 ? message.content.substring(0, 20) + "..." : message.content;
              common_vendor.index.showToast({
                title: `${this.getDisplayName(user)}: ${preview}`,
                icon: "none",
                duration: 2e3
              });
            }
          }
        });
      }
    },
    async openChat(user) {
      const userId = user.userId || user.id;
      const displayName = this.getDisplayName(user);
      console.log("打开聊天:", { userId, displayName, user });
      try {
        await utils_api.markSessionAsRead(userId);
        user.unreadCount = 0;
        this.saveUnreadCount(userId, 0);
      } catch (error) {
        console.error("标记会话已读失败:", error);
        user.unreadCount = 0;
        this.saveUnreadCount(userId, 0);
      }
      common_vendor.index.navigateTo({
        url: `/pages/chatDetail/chatDetail?userId=${userId}&nickname=${encodeURIComponent(displayName)}`
      });
    },
    /**
     * 加载所有好友的未读消息数量
     */
    async loadUnreadCounts() {
      try {
        for (let user of this.userList) {
          const storageKey = `unread_${user.userId || user.id}`;
          const unreadCount = common_vendor.index.getStorageSync(storageKey) || 0;
          user.unreadCount = unreadCount;
        }
      } catch (error) {
        console.error("加载未读消息数量失败:", error);
      }
    },
    /**
     * 保存未读消息数量到本地存储
     */
    saveUnreadCount(userId, count) {
      try {
        const storageKey = `unread_${userId}`;
        common_vendor.index.setStorageSync(storageKey, count);
      } catch (error) {
        console.error("保存未读消息数量失败:", error);
      }
    },
    showAddMenu() {
      this.showAddMenuModal = true;
    },
    hideAddMenu() {
      this.showAddMenuModal = false;
    },
    /**
     * 处理底部导航栏切换
     */
    onTabChange(index) {
      console.log("切换到标签:", index);
    },
    getDisplayName(user) {
      if (user.remark && user.remark.trim()) {
        return user.remark;
      }
      return user.nickname || user.username || "未知用户";
    },
    openTest() {
      this.hideAddMenu();
      common_vendor.index.navigateTo({
        url: "/pages/test/test"
      });
    },
    addFriend() {
      this.hideAddMenu();
      common_vendor.index.navigateTo({
        url: "/pages/addFriend/addFriend"
      });
    },
    openFriendManage() {
      this.hideAddMenu();
      common_vendor.index.navigateTo({
        url: "/pages/friendRequests/friendRequests"
      });
    },
    /**
     * 测试未读消息功能
     */
    testUnreadMessage() {
      this.hideAddMenu();
      if (this.userList.length > 0) {
        const user = this.userList[0];
        user.unreadCount = (user.unreadCount || 0) + 1;
        user.lastMessage = "这是一条测试消息";
        user.lastMessageTime = /* @__PURE__ */ new Date();
        this.saveUnreadCount(user.userId || user.id, user.unreadCount);
        this.userList.splice(0, 1);
        this.userList.unshift(user);
        common_vendor.index.showToast({
          title: "测试消息已添加",
          icon: "success"
        });
      } else {
        common_vendor.index.showToast({
          title: "没有好友可以测试",
          icon: "none"
        });
      }
    },
    logout() {
      this.hideAddMenu();
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("token");
            common_vendor.index.removeStorageSync("userInfo");
            common_vendor.index.reLaunch({
              url: "/pages/login/login"
            });
          }
        }
      });
    },
    /**
     * 格式化时间显示
     */
    formatTime(time) {
      if (!time)
        return "";
      const now = /* @__PURE__ */ new Date();
      const msgTime = new Date(time);
      const diff = now - msgTime;
      if (diff < 6e4)
        return "刚刚";
      if (diff < 36e5)
        return Math.floor(diff / 6e4) + "分钟前";
      if (diff < 864e5)
        return Math.floor(diff / 36e5) + "小时前";
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1e3);
      if (msgTime >= today) {
        return msgTime.toLocaleTimeString("zh-CN", { hour: "2-digit", minute: "2-digit" });
      } else if (msgTime >= yesterday) {
        return "昨天";
      } else {
        return msgTime.toLocaleDateString("zh-CN", { month: "2-digit", day: "2-digit" });
      }
    }
  }
};
if (!Array) {
  const _easycom_TabBar2 = common_vendor.resolveComponent("TabBar");
  _easycom_TabBar2();
}
const _easycom_TabBar = () => "../../components/TabBar/TabBar.js";
if (!Math) {
  _easycom_TabBar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.showAddMenu && $options.showAddMenu(...args)),
    b: common_vendor.f($data.userList, (user, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getDisplayName(user).charAt(0)),
        b: user.unreadCount > 0
      }, user.unreadCount > 0 ? {
        c: common_vendor.t(user.unreadCount > 99 ? "99+" : user.unreadCount)
      } : {}, {
        d: common_vendor.t($options.getDisplayName(user)),
        e: common_vendor.t($options.formatTime(user.lastMessageTime)),
        f: common_vendor.t(user.lastMessage || "点击开始聊天"),
        g: user.unreadCount > 0 ? 1 : "",
        h: user.isMuted
      }, user.isMuted ? {} : {}, {
        i: user.id,
        j: common_vendor.o(($event) => $options.openChat(user), user.id)
      });
    }),
    c: $data.loading
  }, $data.loading ? {} : {}, {
    d: !$data.loading && $data.userList.length === 0
  }, !$data.loading && $data.userList.length === 0 ? {} : {}, {
    e: common_vendor.o($options.onTabChange),
    f: common_vendor.p({
      current: 0
    }),
    g: $data.showAddMenuModal
  }, $data.showAddMenuModal ? common_vendor.e({
    h: common_vendor.o((...args) => $options.addFriend && $options.addFriend(...args)),
    i: $data.pendingRequestCount > 0
  }, $data.pendingRequestCount > 0 ? {
    j: common_vendor.t($data.pendingRequestCount > 99 ? "99+" : $data.pendingRequestCount)
  } : {}, {
    k: common_vendor.o((...args) => $options.openFriendManage && $options.openFriendManage(...args)),
    l: common_vendor.o((...args) => $options.testUnreadMessage && $options.testUnreadMessage(...args)),
    m: common_vendor.o((...args) => $options.logout && $options.logout(...args)),
    n: common_vendor.o(() => {
    }),
    o: common_vendor.o((...args) => $options.hideAddMenu && $options.hideAddMenu(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a041b13f"], ["__file", "E:/wolk/study/uniappIm/src/pages/chat/chat.vue"]]);
wx.createPage(MiniProgramPage);
