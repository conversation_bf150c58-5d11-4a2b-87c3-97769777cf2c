server:
  port: 8083
  servlet:
    context-path: /member

spring:
  application:
    name: ai-member-service
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************
    username: root
    password: 123456
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 支付配置
payment:
  wechat:
    app-id: wx1234567890abcdef
    mch-id: 1234567890
    api-key: your_wechat_api_key_here
    cert-path: classpath:cert/wechat_cert.p12
    notify-url: http://your-domain.com/member/api/member/recharge/callback/wechat
    gateway-url: https://api.mch.weixin.qq.com
  alipay:
    app-id: 2021000000000000
    private-key: your_alipay_private_key_here
    alipay-public-key: your_alipay_public_key_here
    notify-url: http://your-domain.com/member/api/member/recharge/callback/alipay
    gateway-url: https://openapi.alipay.com/gateway.do
    sign-type: RSA2
    charset: UTF-8
    format: json

# 日志配置
logging:
  level:
    cn.zhentao: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/member-service.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
