package cn.zhentao.service.impl;

import cn.zhentao.dto.RechargeRequest;
import cn.zhentao.dto.RechargeResponse;
import cn.zhentao.entity.MemberRecharge;
import cn.zhentao.mapper.MemberRechargeMapper;
import cn.zhentao.service.MemberRechargeService;
import cn.zhentao.service.PaymentService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 会员充值服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberRechargeServiceImpl extends ServiceImpl<MemberRechargeMapper, MemberRecharge> 
        implements MemberRechargeService {

    private final MemberRechargeMapper memberRechargeMapper;
    private final PaymentService paymentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RechargeResponse createRechargeOrder(RechargeRequest request) {
        try {
            // 1. 创建充值记录
            MemberRecharge recharge = new MemberRecharge();
            recharge.setUserId(request.getUserId());
            recharge.setRoleId(request.getRoleId());
            recharge.setRechargeAmount(request.getRechargeAmount());
            recharge.setPayChannel(request.getPayChannel());
            recharge.setPayStatus(MemberRecharge.PayStatus.UNPAID.getCode());
            recharge.setRemark(request.getRemark());
            
            // 生成唯一交易号
            String tradeNo = generateTradeNo();
            recharge.setTradeNo(tradeNo);
            
            // 计算会员时间
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime memberStartTime = now;
            LocalDateTime memberEndTime = now.plusMonths(request.getMemberMonths());
            
            // 如果用户已有有效会员，从当前会员结束时间开始计算
            MemberRecharge currentMember = getCurrentMemberInfo(request.getUserId());
            if (currentMember != null && currentMember.getMemberEndTime().isAfter(now)) {
                memberStartTime = currentMember.getMemberEndTime();
                memberEndTime = memberStartTime.plusMonths(request.getMemberMonths());
            }
            
            recharge.setMemberStartTime(memberStartTime);
            recharge.setMemberEndTime(memberEndTime);
            recharge.setCreateTime(now);
            recharge.setUpdateTime(now);
            
            // 保存充值记录
            save(recharge);
            
            // 2. 调用支付服务创建支付订单
            Map<String, Object> paymentResult = paymentService.createPayment(
                    tradeNo, 
                    request.getRechargeAmount(), 
                    request.getPayChannel(),
                    "会员充值-" + request.getMemberMonths() + "个月"
            );
            
            // 3. 构建响应
            return RechargeResponse.builder()
                    .rechargeId(recharge.getId())
                    .rechargeAmount(recharge.getRechargeAmount())
                    .payChannel(recharge.getPayChannel())
                    .payChannelName(MemberRecharge.PayChannel.getByCode(recharge.getPayChannel()).getDesc())
                    .payStatus(recharge.getPayStatus())
                    .payStatusName(MemberRecharge.PayStatus.getByCode(recharge.getPayStatus()).getDesc())
                    .tradeNo(tradeNo)
                    .qrCode((String) paymentResult.get("qrCode"))
                    .payUrl((String) paymentResult.get("payUrl"))
                    .memberStartTime(memberStartTime)
                    .memberEndTime(memberEndTime)
                    .createTime(recharge.getCreateTime())
                    .build();
                    
        } catch (Exception e) {
            log.error("创建充值订单失败", e);
            throw new RuntimeException("创建充值订单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentCallback(String tradeNo, Integer payChannel, Map<String, Object> callbackData) {
        try {
            // 1. 查询充值记录
            MemberRecharge recharge = getRechargeByTradeNo(tradeNo);
            if (recharge == null) {
                log.warn("未找到交易号为 {} 的充值记录", tradeNo);
                return false;
            }
            
            // 2. 验证支付结果
            boolean paymentSuccess = paymentService.verifyPaymentCallback(payChannel, callbackData);
            if (!paymentSuccess) {
                log.warn("支付验证失败，交易号: {}", tradeNo);
                recharge.setPayStatus(MemberRecharge.PayStatus.FAILED.getCode());
                updateById(recharge);
                return false;
            }
            
            // 3. 更新充值记录状态
            recharge.setPayStatus(MemberRecharge.PayStatus.PAID.getCode());
            recharge.setPayTime(LocalDateTime.now());
            recharge.setUpdateTime(LocalDateTime.now());
            
            updateById(recharge);
            
            log.info("充值成功，用户ID: {}, 金额: {}, 交易号: {}", 
                    recharge.getUserId(), recharge.getRechargeAmount(), tradeNo);
            
            return true;
            
        } catch (Exception e) {
            log.error("处理支付回调失败，交易号: {}", tradeNo, e);
            return false;
        }
    }

    @Override
    public IPage<MemberRecharge> getUserRechargeRecords(Page<MemberRecharge> page, Long userId, Integer payStatus) {
        return memberRechargeMapper.selectRechargePageByCondition(page, userId, payStatus);
    }

    @Override
    public MemberRecharge getRechargeByTradeNo(String tradeNo) {
        return memberRechargeMapper.selectByTradeNo(tradeNo);
    }

    @Override
    public boolean isValidMember(Long userId) {
        MemberRecharge currentMember = getCurrentMemberInfo(userId);
        return currentMember != null && currentMember.getMemberEndTime().isAfter(LocalDateTime.now());
    }

    @Override
    public MemberRecharge getCurrentMemberInfo(Long userId) {
        return memberRechargeMapper.selectLatestValidMember(userId);
    }

    @Override
    public Double getTotalRechargeAmount(Long userId) {
        return memberRechargeMapper.selectTotalRechargeAmount(userId);
    }

    @Override
    public List<MemberRecharge> getExpiringMembers() {
        return memberRechargeMapper.selectExpiringMembers();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean extendMemberTime(Long userId, Integer months) {
        try {
            MemberRecharge currentMember = getCurrentMemberInfo(userId);
            if (currentMember == null) {
                return false;
            }

            LocalDateTime newEndTime = currentMember.getMemberEndTime().plusMonths(months);
            currentMember.setMemberEndTime(newEndTime);
            currentMember.setUpdateTime(LocalDateTime.now());

            return updateById(currentMember);
        } catch (Exception e) {
            log.error("延长会员时间失败，用户ID: {}", userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelRechargeOrder(Long rechargeId, Long userId) {
        try {
            MemberRecharge recharge = getById(rechargeId);
            if (recharge == null || !recharge.getUserId().equals(userId)) {
                return false;
            }

            // 只能取消未支付的订单
            if (!MemberRecharge.PayStatus.UNPAID.getCode().equals(recharge.getPayStatus())) {
                return false;
            }

            recharge.setPayStatus(MemberRecharge.PayStatus.FAILED.getCode());
            recharge.setUpdateTime(LocalDateTime.now());
            recharge.setRemark(recharge.getRemark() + " [用户取消]");

            return updateById(recharge);
        } catch (Exception e) {
            log.error("取消充值订单失败，订单ID: {}", rechargeId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getRechargeStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        List<MemberRecharge> recharges = memberRechargeMapper.selectRechargeByTimeRange(startTime, endTime);

        Map<String, Object> statistics = new HashMap<>();

        // 总充值金额
        BigDecimal totalAmount = recharges.stream()
                .map(MemberRecharge::getRechargeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 充值次数
        int totalCount = recharges.size();

        // 按支付渠道统计
        Map<String, Integer> channelStats = new HashMap<>();
        Map<String, BigDecimal> channelAmountStats = new HashMap<>();

        for (MemberRecharge recharge : recharges) {
            String channelName = MemberRecharge.PayChannel.getByCode(recharge.getPayChannel()).getDesc();
            channelStats.merge(channelName, 1, Integer::sum);
            channelAmountStats.merge(channelName, recharge.getRechargeAmount(), BigDecimal::add);
        }

        statistics.put("totalAmount", totalAmount);
        statistics.put("totalCount", totalCount);
        statistics.put("channelStats", channelStats);
        statistics.put("channelAmountStats", channelAmountStats);
        statistics.put("startTime", startTime);
        statistics.put("endTime", endTime);

        return statistics;
    }

    @Override
    public String generatePaymentQrCode(Long rechargeId, Integer payChannel) {
        try {
            MemberRecharge recharge = getById(rechargeId);
            if (recharge == null) {
                throw new RuntimeException("充值记录不存在");
            }

            return paymentService.generateQrCode(recharge.getTradeNo(), payChannel);
        } catch (Exception e) {
            log.error("生成支付二维码失败，充值ID: {}", rechargeId, e);
            throw new RuntimeException("生成支付二维码失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一交易号
     */
    private String generateTradeNo() {
        return "MR" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
