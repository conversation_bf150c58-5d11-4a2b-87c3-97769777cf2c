package cn.zhentao.controller;

import cn.zhentao.common.Result;
import cn.zhentao.pojo.Permission;
import cn.zhentao.service.PermissionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制层
 * 处理权限相关的HTTP请求
 */
@RestController
@RequestMapping("/system/permissions")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 分页查询权限列表
     */
    @GetMapping
    public Result<IPage<Permission>> getPermissionPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String permissionName,
            @RequestParam(required = false) String permissionCode) {
        
        Page<Permission> page = new Page<>(pageNum, pageSize);
        IPage<Permission> permissionPage = permissionService.getPermissionPage(page, permissionName, permissionCode);
        return Result.success(permissionPage);
    }

    /**
     * 根据权限编码查询权限
     */
    @GetMapping("/code/{permissionCode}")
    public Result<Permission> getPermissionByCode(@PathVariable String permissionCode) {
        Permission permission = permissionService.getPermissionByCode(permissionCode);
        return permission != null ? Result.success(permission) : Result.error("权限不存在");
    }

    /**
     * 根据API路径和请求方法查询权限
     */
    @GetMapping("/api")
    public Result<Permission> getPermissionByApiPath(
            @RequestParam String apiPath,
            @RequestParam String method) {
        
        Permission permission = permissionService.getPermissionByApiPath(apiPath, method);
        return permission != null ? Result.success(permission) : Result.error("权限不存在");
    }

    /**
     * 获取权限树形结构
     */
    @GetMapping("/tree")
    public Result<List<Permission>> getPermissionTree() {
        List<Permission> permissions = permissionService.getPermissionTree();
        return Result.success(permissions);
    }

    /**
     * 根据菜单ID查询权限
     */
    @GetMapping("/menu/{menuId}")
    public Result<List<Permission>> getPermissionsByMenuId(@PathVariable Long menuId) {
        List<Permission> permissions = permissionService.getPermissionsByMenuId(menuId);
        return Result.success(permissions);
    }

    /**
     * 检查权限编码是否已存在
     */
    @GetMapping("/check/code")
    public Result<Boolean> existsByPermissionCode(@RequestParam String permissionCode) {
        boolean exists = permissionService.existsByPermissionCode(permissionCode);
        return Result.success(exists);
    }

    /**
     * 检查API路径和方法是否已绑定权限
     */
    @GetMapping("/check/api")
    public Result<Boolean> existsByApiPath(
            @RequestParam String apiPath,
            @RequestParam String method) {
        
        boolean exists = permissionService.existsByApiPath(apiPath, method);
        return Result.success(exists);
    }

    /**
     * 批量删除权限
     */
    @DeleteMapping
    public Result<Boolean> deletePermissionsByIds(@RequestBody List<Long> permissionIds) {
        boolean success = permissionService.deletePermissionsByIds(permissionIds);
        return success ? Result.success(true) : Result.error("删除失败");
    }

    /**
     * 根据角色ID查询权限
     */
    @GetMapping("/role/{roleId}")
    public Result<List<Permission>> getPermissionsByRoleId(@PathVariable Long roleId) {
        List<Permission> permissions = permissionService.getPermissionsByRoleId(roleId);
        return Result.success(permissions);
    }

    /**
     * 根据用户ID查询权限
     */
    @GetMapping("/user/{userId}")
    public Result<List<Permission>> getPermissionsByUserId(@PathVariable Long userId) {
        List<Permission> permissions = permissionService.getPermissionsByUserId(userId);
        return Result.success(permissions);
    }

    /**
     * 新增权限
     */
    @PostMapping
    public Result<Boolean> addPermission(@RequestBody Permission permission) {
        boolean success = permissionService.save(permission);
        return success ? Result.success(true) : Result.error("添加失败");
    }

    /**
     * 修改权限
     */
    @PutMapping
    public Result<Boolean> updatePermission(@RequestBody Permission permission) {
        boolean success = permissionService.updateById(permission);
        return success ? Result.success(true) : Result.error("修改失败");
    }
}
