
.container.data-v-98a9a758 {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar.data-v-98a9a758 {
  background-color: #fff;
  padding-top: var(--status-bar-height, 44px);
  border-bottom: 1px solid #e5e5e5;
}
.navbar-content.data-v-98a9a758 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.navbar-left.data-v-98a9a758 {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon.data-v-98a9a758 {
  font-size: 20px;
  color: #333;
}
.navbar-title.data-v-98a9a758 {
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-98a9a758 {
  width: 60px;
}

/* 好友申请列表 */
.request-list.data-v-98a9a758 {
  background-color: #fff;
}
.request-item.data-v-98a9a758 {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.user-avatar.data-v-98a9a758 {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #007aff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.avatar-text.data-v-98a9a758 {
  color: white;
  font-size: 18px;
  font-weight: 600;
}
.request-info.data-v-98a9a758 {
  flex: 1;
}
.user-name.data-v-98a9a758 {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}
.request-message.data-v-98a9a758 {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}
.request-time.data-v-98a9a758 {
  font-size: 12px;
  color: #999;
}
.action-buttons.data-v-98a9a758 {
  display: flex;
  gap: 10px;
}
.reject-btn.data-v-98a9a758, .accept-btn.data-v-98a9a758 {
  height: 32px;
  padding: 0 15px;
  border: none;
  border-radius: 16px;
  font-size: 14px;
}
.reject-btn.data-v-98a9a758 {
  background-color: #f5f5f5;
  color: #666;
}
.accept-btn.data-v-98a9a758 {
  background-color: #007aff;
  color: white;
}
.status-text.data-v-98a9a758 {
  padding: 0 15px;
}
.status-accepted.data-v-98a9a758 {
  color: #4cd964;
}
.status-rejected.data-v-98a9a758 {
  color: #ff3b30;
}
.status-pending.data-v-98a9a758 {
  color: #ff9500;
}

/* 空状态 */
.empty-state.data-v-98a9a758 {
  text-align: center;
  padding: 100px 20px;
}
.empty-icon.data-v-98a9a758 {
  font-size: 48px;
  margin-bottom: 20px;
  display: block;
}
.empty-text.data-v-98a9a758 {
  color: #666;
  font-size: 16px;
}

/* 加载状态 */
.loading.data-v-98a9a758 {
  text-align: center;
  padding: 50px 20px;
  color: #666;
}
