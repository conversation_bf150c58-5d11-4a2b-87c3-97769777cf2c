<template>
  <div class="knowledge-qa-page">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="知识问答"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="delete-o" @click="clearHistory" />
      </template>
    </van-nav-bar>

    <!-- 功能介绍 -->
    <div class="feature-intro">
      <div class="intro-content">
        <div class="intro-icon">🧠</div>
        <div class="intro-text">
          <h3>AI知识问答</h3>
          <p>我是你的知识渊博的AI助手，可以回答历史、科学、技术、文化等各方面的问题</p>
        </div>
      </div>
      <div class="intro-examples">
        <div class="example-title">💡 试试这些问题：</div>
        <div class="example-tags">
          <span
            v-for="example in examples"
            :key="example"
            class="example-tag"
            @click="askExample(example)"
          >
            {{ example }}
          </span>
        </div>
      </div>
    </div>

    <!-- 对话区域 -->
    <div class="chat-container" ref="chatContainer">
      <div v-if="messages.length === 0" class="empty-chat">
        <div class="empty-icon">🤔</div>
        <p>请输入你的问题，我来为你解答</p>
      </div>

      <div
        v-for="(message, index) in messages"
        :key="index"
        :class="['message-item', message.type]"
      >
        <div class="message-avatar">
          {{ message.type === 'user' ? '👤' : '🤖' }}
        </div>
        <div class="message-content">
          <div class="message-text" v-html="formatMessage(message.content)"></div>
          <div class="message-time">{{ formatTime(message.timestamp) }}</div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="message-item ai loading">
        <div class="message-avatar">🤖</div>
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <div class="input-wrapper">
        <van-field
          v-model="inputText"
          placeholder="请输入你的问题..."
          type="textarea"
          rows="1"
          autosize
          @keyup.enter="sendMessage"
          class="message-input"
        />
        <van-button
          type="primary"
          size="large"
          :loading="isLoading"
          @click="sendMessage"
          class="send-btn"
          :disabled="!inputText.trim()"
        >
          <van-icon name="send" />
        </van-button>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <van-button
          size="small"
          type="default"
          icon="volume-o"
          @click="toggleVoiceInput"
        >
          语音输入
        </van-button>
        <van-button
          size="small"
          type="default"
          icon="photo-o"
          @click="uploadImage"
        >
          图片识别
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import aiService from '@/services/aiService'

// 响应式数据
const inputText = ref('')
const messages = ref<Array<{
  type: 'user' | 'ai'
  content: string
  timestamp: number
}>>([])
const isLoading = ref(false)
const chatContainer = ref<HTMLElement>()

// 示例问题
const examples = ref([
  '地球的直径是多少？',
  '中国有多少个省份？',
  '什么是人工智能？',
  '太阳系有几颗行星？',
  '长城有多长？'
])

// 发送消息
const sendMessage = async () => {
  const question = inputText.value.trim()
  if (!question || isLoading.value) return

  // 添加用户消息
  messages.value.push({
    type: 'user',
    content: question,
    timestamp: Date.now()
  })

  inputText.value = ''
  isLoading.value = true

  await scrollToBottom()

  // 添加AI消息占位符
  const aiMessageIndex = messages.value.length
  messages.value.push({
    type: 'ai',
    content: '',
    timestamp: Date.now()
  })

  let accumulatedText = ''

  try {
    // 使用流式输出
    await aiService.knowledgeQAStream(
      12345,
      question,
      // onMessage回调
      (content: string) => {
        accumulatedText += content
        messages.value[aiMessageIndex].content = accumulatedText
        scrollToBottom()
      },
      // onComplete回调
      () => {
        isLoading.value = false
        scrollToBottom()
      },
      // onError回调
      async (error: any) => {
        console.error('流式输出失败，尝试非流式输出:', error)

        // 流式输出失败，尝试非流式输出
        try {
          const response = await aiService.knowledgeQA(12345, question)
          if (response.code === 200) {
            messages.value[aiMessageIndex].content = response.data || '回答获取成功，但内容为空。'
          } else {
            messages.value[aiMessageIndex].content = '抱歉，我现在无法回答你的问题。请稍后再试。'
          }
        } catch (fallbackError) {
          console.error('非流式输出也失败:', fallbackError)
          messages.value[aiMessageIndex].content = '抱歉，我现在无法回答你的问题。请稍后再试。'
        }

        isLoading.value = false
        scrollToBottom()
      }
    )

  } catch (error) {
    console.error('知识问答失败:', error)

    // 备选方案：直接调用非流式API
    try {
      const response = await aiService.knowledgeQA(12345, question)
      if (response.code === 200) {
        messages.value[aiMessageIndex].content = response.data || '回答获取成功，但内容为空。'
      } else {
        messages.value[aiMessageIndex].content = '抱歉，我现在无法回答你的问题。请稍后再试。'
      }
    } catch (fallbackError) {
      console.error('备选方案失败:', fallbackError)
      messages.value[aiMessageIndex].content = '抱歉，我现在无法回答你的问题。请稍后再试。'
    }

    isLoading.value = false
    await scrollToBottom()
  }
}

// 点击示例问题
const askExample = (example: string) => {
  inputText.value = example
  sendMessage()
}

// 格式化消息内容
const formatMessage = (content: string) => {
  // 简单的markdown格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight
  }
}

// 清空历史
const clearHistory = async () => {
  try {
    await showConfirmDialog({
      title: '确认清空',
      message: '确定要清空所有对话记录吗？'
    })

    messages.value = []
    showToast('对话记录已清空')
  } catch {
    // 用户取消
  }
}

// 语音输入
const toggleVoiceInput = () => {
  showToast('语音输入功能开发中...')
}

// 图片上传
const uploadImage = () => {
  showToast('图片识别功能开发中...')
}

onMounted(() => {
  // 添加欢迎消息
  messages.value.push({
    type: 'ai',
    content: '你好！我是AI知识问答助手。我可以回答历史、科学、技术、文化等各方面的问题。请随时向我提问！',
    timestamp: Date.now()
  })
})
</script>

<style scoped>
.knowledge-qa-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-intro {
  margin: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.intro-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 16px;
}

.intro-text h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.intro-text p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

.example-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.example-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.example-tag:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-chat {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 85%;
}

.message-item.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-item.ai {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.9);
  flex-shrink: 0;
}

.message-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 12px 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-item.user .message-content {
  background: rgba(102, 126, 234, 0.9);
  color: white;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-time {
  font-size: 12px;
  color: #95a5a6;
  text-align: right;
}

.message-item.user .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 8px 0;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #95a5a6;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.input-container {
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  margin-bottom: 12px;
}

.message-input {
  flex: 1;
  background: white;
  border-radius: 20px;
}

.send-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}
</style>
