package cn.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 用户信息表
 * @TableName user
 */
@TableName(value ="user")
@Data
public class User implements Serializable, UserDetails {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户账号
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户类型(00系统用户)
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别(0男 1女 2未知)
     */
    private String sex;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态(0正常 1停用)
     */
    private Integer status;

    /**
     * 删除标志(0代表存在 2代表删除)
     */
    private String delFlag;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户在线状态
     */
    @TableField("userStatus")
    private String userStatus;

    /**
     * 人脸数据
     */
    private String faceData;

    /**
     * 人脸图片路径
     */
    private String imageUrl;

    /**
     * 逻辑删除
     */
    private Integer deleted;

    /**
     * 用户信息
     */
    private byte[] user;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    // ================ 新增临时字段（不映射数据库） ================

    /**
     * 角色名称列表（如：["ROLE_ADMIN", "ROLE_USER"]）
     */
    @TableField(exist = false)
    private List<String> rnames;

    /**
     * 权限名称列表（如：["user:list", "role:add"]）
     */
    @TableField(exist = false)
    private List<String> pnames;

    /**
     * 角色ID列表（用于前端角色分配）
     */
    @TableField(exist = false)
    private List<Long> roleIds;

    /**
     * 令牌字段（用于登录后存储JWT令牌）
     */
    @TableField(exist = false)
    private transient String token; // transient关键字避免序列化

    // ================ Spring Security UserDetails接口实现 ================

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        // 角色权限
        if (rnames != null) {
            for (String role : rnames) {
                authorities.add(new SimpleGrantedAuthority(role));
            }
        }
        // 功能权限
        if (pnames != null) {
            for (String permission : pnames) {
                authorities.add(new SimpleGrantedAuthority(permission));
            }
        }
        return authorities;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return status == 0; // 0-正常状态表示未锁定
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return "0".equals(delFlag) && status == 0; // 未删除且状态正常
    }
}
