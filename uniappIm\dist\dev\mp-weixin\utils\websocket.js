"use strict";
const common_vendor = require("../common/vendor.js");
let socketTask = null;
let messageHandler = null;
let reconnectTimer = null;
let heartbeatTimer = null;
let reconnectCount = 0;
let maxReconnectCount = 5;
let isManualClose = false;
const WS_URL = "ws://localhost:9999/ws";
function connectWebSocket(token, onMessage) {
  if (socketTask) {
    socketTask.close();
  }
  messageHandler = onMessage;
  socketTask = common_vendor.index.connectSocket({
    url: WS_URL,
    success: () => {
      console.log("WebSocket连接成功");
    },
    fail: (err) => {
      console.error("WebSocket连接失败:", err);
    }
  });
  socketTask.onOpen(() => {
    console.log("WebSocket已打开");
    reconnectCount = 0;
    isManualClose = false;
    sendWebSocketMessage({
      type: "auth",
      token
    });
    startHeartbeat();
  });
  socketTask.onMessage((res) => {
    try {
      const message = JSON.parse(res.data);
      console.log("收到WebSocket消息:", message);
      if (message.type === "auth_success") {
        console.log("WebSocket认证成功");
      } else if (message.type === "heartbeat") {
        console.log("心跳响应");
      } else if (messageHandler) {
        messageHandler(message);
      }
    } catch (error) {
      console.error("解析WebSocket消息失败:", error);
    }
  });
  socketTask.onClose(() => {
    console.log("WebSocket连接关闭");
    stopHeartbeat();
    if (!isManualClose && reconnectCount < maxReconnectCount && !reconnectTimer) {
      reconnectCount++;
      const delay = Math.min(1e3 * Math.pow(2, reconnectCount), 3e4);
      console.log(`第${reconnectCount}次重连WebSocket，${delay}ms后重试`);
      reconnectTimer = setTimeout(() => {
        connectWebSocket(token, messageHandler);
        reconnectTimer = null;
      }, delay);
    } else if (reconnectCount >= maxReconnectCount) {
      console.error("WebSocket重连次数已达上限，停止重连");
      common_vendor.index.showToast({
        title: "网络连接失败，请检查网络",
        icon: "none"
      });
    }
  });
  socketTask.onError((err) => {
    console.error("WebSocket错误:", err);
  });
}
function sendWebSocketMessage(message) {
  if (socketTask && socketTask.readyState === 1) {
    socketTask.send({
      data: JSON.stringify(message),
      success: () => {
        console.log("WebSocket消息发送成功:", message);
      },
      fail: (err) => {
        console.error("WebSocket消息发送失败:", err);
      }
    });
  } else {
    console.error("WebSocket未连接");
  }
}
function closeWebSocket() {
  isManualClose = true;
  stopHeartbeat();
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  if (socketTask) {
    socketTask.close();
    socketTask = null;
  }
  reconnectCount = 0;
}
function startHeartbeat() {
  heartbeatTimer = setInterval(() => {
    sendWebSocketMessage({
      type: "heartbeat"
    });
  }, 3e4);
}
function stopHeartbeat() {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer);
    heartbeatTimer = null;
  }
}
exports.closeWebSocket = closeWebSocket;
exports.connectWebSocket = connectWebSocket;
exports.sendWebSocketMessage = sendWebSocketMessage;
