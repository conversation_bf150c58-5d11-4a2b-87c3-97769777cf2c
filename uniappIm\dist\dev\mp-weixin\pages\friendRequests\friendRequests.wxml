<view class="container data-v-98a9a758"><view class="custom-navbar data-v-98a9a758"><view class="navbar-content data-v-98a9a758"><view class="navbar-left data-v-98a9a758" bindtap="{{a}}"><text class="back-icon data-v-98a9a758">←</text></view><view class="navbar-title data-v-98a9a758">好友申请</view><view class="navbar-right data-v-98a9a758"></view></view></view><view wx:if="{{b}}" class="request-list data-v-98a9a758"><view wx:for="{{c}}" wx:for-item="request" wx:key="j" class="request-item data-v-98a9a758"><view class="user-avatar data-v-98a9a758"><text class="avatar-text data-v-98a9a758">{{request.a}}</text></view><view class="request-info data-v-98a9a758"><view class="user-name data-v-98a9a758">{{request.b}}</view><view class="request-message data-v-98a9a758">{{request.c}}</view><view class="request-time data-v-98a9a758">{{request.d}}</view></view><view wx:if="{{request.e}}" class="action-buttons data-v-98a9a758"><button bindtap="{{request.f}}" class="reject-btn data-v-98a9a758">拒绝</button><button bindtap="{{request.g}}" class="accept-btn data-v-98a9a758">同意</button></view><view wx:else class="status-text data-v-98a9a758"><text class="{{['data-v-98a9a758', request.i]}}">{{request.h}}</text></view></view></view><view wx:elif="{{d}}" class="empty-state data-v-98a9a758"><text class="empty-icon data-v-98a9a758">📭</text><text class="empty-text data-v-98a9a758">暂无好友申请</text></view><view wx:if="{{e}}" class="loading data-v-98a9a758"><text class="data-v-98a9a758">加载中...</text></view></view>