"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      searchResults: [],
      loading: false,
      showEmpty: false,
      emptyText: "请输入关键词搜索用户"
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    onSearchInput() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = [];
        this.showEmpty = false;
      }
    },
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none"
        });
        return;
      }
      this.loading = true;
      this.showEmpty = false;
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!userInfo) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }
        const result = await utils_api.searchUsers(this.searchKeyword.trim());
        console.log("搜索结果:", result);
        if (result.code === 200) {
          this.searchResults = result.data.map((user) => ({
            ...user,
            requestSent: false
          }));
          if (this.searchResults.length === 0) {
            this.showEmpty = true;
            this.emptyText = "未找到相关用户";
          }
        } else {
          common_vendor.index.showToast({
            title: result.message || "搜索失败",
            icon: "none"
          });
          this.showEmpty = true;
          this.emptyText = "搜索失败，请重试";
        }
      } catch (error) {
        console.error("搜索用户失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
        this.showEmpty = true;
        this.emptyText = "网络错误，请重试";
      } finally {
        this.loading = false;
      }
    },
    async sendFriendRequest(user) {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (!userInfo) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          return;
        }
        const result = await utils_api.sendFriendRequest({
          toUserId: user.userId,
          message: "我想加你为好友"
        });
        console.log("发送好友申请结果:", result);
        if (result.code === 200) {
          user.requestSent = true;
          this.$forceUpdate();
          common_vendor.index.showToast({
            title: "好友申请已发送",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: result.message || "发送失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("发送好友申请失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    c: $data.searchKeyword,
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: $data.searchResults.length > 0
  }, $data.searchResults.length > 0 ? {
    f: common_vendor.f($data.searchResults, (user, k0, i0) => {
      return {
        a: common_vendor.t(user.nickname ? user.nickname.charAt(0) : user.username.charAt(0)),
        b: common_vendor.t(user.nickname || user.username),
        c: common_vendor.t(user.username),
        d: common_vendor.t(user.requestSent ? "已发送" : "添加"),
        e: common_vendor.o(($event) => $options.sendFriendRequest(user), user.userId),
        f: user.requestSent,
        g: user.userId
      };
    })
  } : {}, {
    g: $data.showEmpty
  }, $data.showEmpty ? {
    h: common_vendor.t($data.emptyText)
  } : {}, {
    i: $data.loading
  }, $data.loading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-893823e0"], ["__file", "E:/wolk/study/uniappIm/src/pages/addFriend/addFriend.vue"]]);
wx.createPage(MiniProgramPage);
