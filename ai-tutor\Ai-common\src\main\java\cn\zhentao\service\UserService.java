package cn.zhentao.service;

import cn.zhentao.common.Result;
import cn.zhentao.pojo.User;
import cn.zhentao.pojo.Role;
import cn.zhentao.pojo.Permission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpSession;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.List;

/**
 * 用户服务接口
 * 定义用户相关的业务逻辑方法
 */
public interface UserService extends IService<User> {

    /**
     * 加载用户详情（Spring Security认证用）
     */
    UserDetails loadUserByUsername(String username) throws UsernameNotFoundException;

    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);

    /**
     * 获取用户拥有的角色
     */
    List<Role> getUserRoles(Long userId);

    /**
     * 获取用户拥有的权限
     */
    List<Permission> getUserPermissions(Long userId);

    /**
     * 给用户分配角色（覆盖原有角色）
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 移除用户的指定角色
     */
    boolean removeUserRoles(Long userId, List<Long> roleIds);

    /**
     * 分页查询用户列表
     */
    IPage<User> getUserPage(Page<User> page, String username, String status);

    /**
     * 校验用户是否有权限访问接口
     */
    boolean hasPermission(Long userId, String apiPath, String method);

    /**
     * 重置用户密码
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 发送验证码
     */
    Result sendCode(String phonenumber, HttpSession session);

    /**
     * 根据手机号查询用户
     */
    User getUserByPhone(String phone);

    /**
     * 验证码登录
     */
    Result loginByCode(String phone, String code, HttpSession session);
}
