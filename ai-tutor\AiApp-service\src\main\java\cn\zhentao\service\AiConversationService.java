package cn.zhentao.service;

import cn.zhentao.util.DashScopeAiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI对话服务
 * 对接小程序AI功能模块
 */
@Slf4j
@Service
public class AiConversationService {

    @Autowired
    private DashScopeAiUtil dashScopeAiUtil;

    // 保存最后一次AI响应，用于错误显示
    private String lastAiResponse;



    // 用户会话缓存
    private final Map<Long, Map<String, Object>> userSessions = new ConcurrentHashMap<>();

    /**
     * 知识问答 - AI百科全书
     */
    public DashScopeAiUtil.AiResponse knowledgeQA(Long userId, String question) {
        log.info("知识问答请求 - 用户ID: {}, 问题: {}", userId, question);

        String sessionId = getUserSessionId(userId, "knowledge");
        String enhancedPrompt = "作为一个知识渊博的AI助手，请详细回答以下问题：" + question;

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "knowledge", response.getSessionId());

        return response;
    }

    /**
     * 知识问答 - 流式输出
     */
    public SseEmitter knowledgeQAStream(Long userId, String question) {
        log.info("知识问答流式请求 - 用户ID: {}, 问题: {}", userId, question);

        String sessionId = getUserSessionId(userId, "knowledge");
        String enhancedPrompt = "作为一个知识渊博的AI助手，请详细回答以下问题：" + question;

        // 使用优化的流式输出，60秒超时，按字符分割
        return dashScopeAiUtil.optimizedStreamCall(enhancedPrompt, sessionId, "word");
    }

    /**
     * 信息查询 - 天气电话资讯
     */
    public DashScopeAiUtil.AiResponse informationQuery(Long userId, String query) {
        log.info("信息查询请求 - 用户ID: {}, 查询: {}", userId, query);

        String sessionId = getUserSessionId(userId, "info");
        String enhancedPrompt = "作为一个信息查询助手，请帮助查询以下信息（如果是天气查询，请说明需要具体城市；如果是电话查询，请提供相关建议）：" + query;

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "info", response.getSessionId());

        return response;
    }

    /**
     * 信息查询 - 流式输出
     */
    public SseEmitter informationQueryStream(Long userId, String query) {
        log.info("信息查询流式请求 - 用户ID: {}, 查询: {}", userId, query);

        String sessionId = getUserSessionId(userId, "info");
        String enhancedPrompt = "作为一个信息查询助手，请帮助查询以下信息（如果是天气查询，请说明需要具体城市；如果是电话查询，请提供相关建议）：" + query;

        // 使用优化的流式输出，60秒超时，按字符分割
        return dashScopeAiUtil.optimizedStreamCall(enhancedPrompt, sessionId, "word");
    }



    /**
     * 文本生成 - 作文故事诗歌
     */
    public DashScopeAiUtil.AiResponse textGeneration(Long userId, String prompt, String type) {
        log.info("文本生成请求 - 用户ID: {}, 类型: {}, 提示: {}", userId, type, prompt);

        String sessionId = getUserSessionId(userId, "text_gen");
        String enhancedPrompt = getTextGenerationPrompt(type, prompt);

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "text_gen", response.getSessionId());

        return response;
    }

    /**
     * 文本生成 - 流式输出
     */
    public SseEmitter textGenerationStream(Long userId, String prompt, String type) {
        log.info("文本生成流式请求 - 用户ID: {}, 类型: {}, 提示: {}", userId, type, prompt);

        String sessionId = getUserSessionId(userId, "text_gen");
        String enhancedPrompt = getTextGenerationPrompt(type, prompt);

        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 语言翻译 - 多语言互译
     */
    public DashScopeAiUtil.AiResponse languageTranslation(Long userId, String text, String fromLang, String toLang) {
        log.info("语言翻译请求 - 用户ID: {}, 从{}翻译到{}: {}", userId, fromLang, toLang, text);

        String sessionId = getUserSessionId(userId, "translation");
        String enhancedPrompt = String.format("请将以下%s文本翻译成%s，只返回翻译结果：%s", fromLang, toLang, text);

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "translation", response.getSessionId());

        return response;
    }

    /**
     * 语言翻译 - 流式输出
     */
    public SseEmitter languageTranslationStream(Long userId, String text, String fromLang, String toLang) {
        log.info("语言翻译流式请求 - 用户ID: {}, 从{}翻译到{}: {}", userId, fromLang, toLang, text);

        String sessionId = getUserSessionId(userId, "translation");
        String enhancedPrompt = String.format("请将以下%s文本翻译成%s：%s", fromLang, toLang, text);

        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 情感陪伴 - 情感识别回应
     */
    public DashScopeAiUtil.AiResponse emotionalCompanion(Long userId, String message) {
        log.info("情感陪伴请求 - 用户ID: {}, 消息: {}", userId, message);

        String sessionId = getUserSessionId(userId, "emotion");
        String enhancedPrompt = "作为一个温暖贴心的AI伙伴，请识别用户的情感状态并给予适当的回应和安慰。用户说：" + message;

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "emotion", response.getSessionId());

        return response;
    }

    /**
     * 情感陪伴 - 流式输出
     */
    public SseEmitter emotionalCompanionStream(Long userId, String message) {
        log.info("情感陪伴流式请求 - 用户ID: {}, 消息: {}", userId, message);

        String sessionId = getUserSessionId(userId, "emotion");
        String enhancedPrompt = "作为一个温暖贴心的AI伙伴，请识别用户的情感状态并给予适当的回应和安慰。用户说：" + message;

        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 智能推荐 - 个性化内容
     */
    public DashScopeAiUtil.AiResponse intelligentRecommendation(Long userId, String preferences, String category) {
        log.info("智能推荐请求 - 用户ID: {}, 偏好: {}, 类别: {}", userId, preferences, category);

        String sessionId = getUserSessionId(userId, "recommendation");
        String enhancedPrompt = String.format("根据用户偏好：%s，请为用户推荐%s相关的个性化内容，包括具体的推荐理由", preferences, category);

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "recommendation", response.getSessionId());

        return response;
    }

    /**
     * 智能推荐 - 流式输出
     */
    public SseEmitter intelligentRecommendationStream(Long userId, String preferences, String category) {
        log.info("智能推荐流式请求 - 用户ID: {}, 偏好: {}, 类别: {}", userId, preferences, category);

        String sessionId = getUserSessionId(userId, "recommendation");
        String enhancedPrompt = String.format("根据用户偏好：%s，请为用户推荐%s相关的个性化内容", preferences, category);

        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 获取用户会话ID
     */
    private String getUserSessionId(Long userId, String type) {
        Map<String, Object> sessions = userSessions.computeIfAbsent(userId, k -> new ConcurrentHashMap<>());
        return (String) sessions.get(type);
    }

    /**
     * 更新用户会话ID
     */
    private void updateUserSession(Long userId, String type, String sessionId) {
        if (sessionId != null) {
            Map<String, Object> sessions = userSessions.computeIfAbsent(userId, k -> new ConcurrentHashMap<>());
            sessions.put(type, sessionId);
        }
    }

    /**
     * 获取文本生成提示词
     */
    private String getTextGenerationPrompt(String type, String prompt) {
        switch (type.toLowerCase()) {
            case "essay":
            case "作文":
                return "作为一个优秀的作文老师，请根据以下要求写一篇作文：" + prompt;
            case "story":
            case "故事":
                return "作为一个创意故事作家，请根据以下要求创作一个故事：" + prompt;
            case "poem":
            case "诗歌":
                return "作为一个诗人，请根据以下要求创作一首诗歌：" + prompt;
            default:
                return "请根据以下要求进行文本创作：" + prompt;
        }
    }

    /**
     * 清除用户会话
     */
    public void clearUserSessions(Long userId) {
        userSessions.remove(userId);
        log.info("已清除用户{}的所有会话", userId);
    }

    /**
     * 获取用户会话统计
     */
    public Map<String, Object> getUserSessionStats(Long userId) {
        Map<String, Object> sessions = userSessions.get(userId);
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", userId);
        stats.put("sessionCount", sessions != null ? sessions.size() : 0);
        stats.put("sessionTypes", sessions != null ? sessions.keySet() : Collections.emptySet());
        return stats;
    }

    // ==================== 语音通话相关功能 ====================

    /**
     * 语音通话 - AI智能体对话
     */
    public DashScopeAiUtil.AiResponse voiceChat(Long userId, String message, String agentRole) {
        log.info("语音通话请求 - 用户ID: {}, AI角色: {}, 消息: {}", userId, agentRole, message);

        String sessionId = getUserSessionId(userId, "voice_chat");
        String enhancedPrompt = getVoiceChatPrompt(agentRole, message);

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "voice_chat", response.getSessionId());

        return response;
    }

    /**
     * 语音通话 - 流式输出
     */
    public SseEmitter voiceChatStream(Long userId, String message, String agentRole) {
        log.info("语音通话流式请求 - 用户ID: {}, AI角色: {}, 消息: {}", userId, agentRole, message);

        String sessionId = getUserSessionId(userId, "voice_chat");
        String enhancedPrompt = getVoiceChatPrompt(agentRole, message);

        return dashScopeAiUtil.streamCall(enhancedPrompt, sessionId);
    }

    /**
     * 获取语音通话提示词
     */
    private String getVoiceChatPrompt(String agentRole, String message) {
        switch (agentRole.toLowerCase()) {
            case "teacher":
            case "老师":
                return "作为一个专业的AI老师，请耐心回答学生的问题，提供详细的解释和学习建议。学生问：" + message;
            case "friend":
            case "朋友":
                return "作为一个友善的AI朋友，请用轻松愉快的语气与用户聊天，分享有趣的话题。朋友说：" + message;
            case "counselor":
            case "心理师":
                return "作为一个专业的AI心理咨询师，请倾听用户的心声，提供温暖的支持和专业的建议。用户说：" + message;
            case "assistant":
            case "助手":
                return "作为一个全能的AI助手，请高效地帮助用户解决各种问题，提供准确的信息和实用的建议。用户问：" + message;
            default:
                return "作为一个智能AI助手，请回答用户的问题：" + message;
        }
    }

    /**
     * 生成体验Token（当阿里云权限不足时使用）
     */
    public Map<String, Object> generateExperienceToken(String userId, String agentRole) {
        log.info("生成体验Token - 用户ID: {}, AI角色: {}", userId, agentRole);

        Map<String, Object> tokenData = new HashMap<>();
        tokenData.put("token", "eyJSZXF1ZXN0SWQiOiI3NUU1NDVEQi04QTkxLTU5OEYtOEVFNS1EMEE0NEUyNDMxNkMiLCJXb3JrZmxvd1R5cGUiOiJWb2ljZUNoYXQiLCJUZW1wb3JhcnlBSUFnZW50SWQiOiI4OTRhODhiMWVhZmQ0NmVlYWU3OWEwYjU1OWZiMjU2YSIsIkV4cGlyZVRpbWUiOiIyMDI1LTA4LTAzIDAxOjQzOjAzIiwiTmFtZSI6Ijg5NGE4OGIxZWFmZDQ2ZWVhZTc5YTBiNTU5ZmIyNTZhIiwiUmVnaW9uIjoiY24tYmVpamluZyJ9");
        tokenData.put("app_id", "894a88b1eafd46eeae79a0b559fb256a");
        tokenData.put("user_id", userId);
        tokenData.put("timestamp", System.currentTimeMillis());
        tokenData.put("expire_time", "2025-08-03 01:43:03");
        tokenData.put("region", "cn-beijing");
        tokenData.put("agent_role", agentRole);
        tokenData.put("voice_agent_id", "8ed6ed9e6c57469e889a1bc3651ac64b");

        return tokenData;
    }






    /**
     * 游戏娱乐 - 语音游戏互动
     */
    public DashScopeAiUtil.AiResponse gameEntertainment(Long userId, String gameType, String userInput, String gameState) {
        log.info("游戏娱乐请求 - 用户ID: {}, 游戏类型: {}, 用户输入: {}, 游戏状态: {}", userId, gameType, userInput, gameState);

        String sessionId = getUserSessionId(userId, "game_" + gameType);
        String enhancedPrompt = buildGamePrompt(gameType, userInput, gameState);

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "game_" + gameType, response.getSessionId());

        return response;
    }

    /**
     * 游戏娱乐 - 流式输出
     */
    public SseEmitter gameEntertainmentStream(Long userId, String gameType, String userInput, String gameState) {
        log.info("游戏娱乐流式请求 - 用户ID: {}, 游戏类型: {}, 用户输入: {}", userId, gameType, userInput);

        String sessionId = getUserSessionId(userId, "game_" + gameType);
        String enhancedPrompt = buildGamePrompt(gameType, userInput, gameState);

        // 使用优化的流式输出，60秒超时，按字符分割
        return dashScopeAiUtil.optimizedStreamCall(enhancedPrompt, sessionId, "word");
    }



    /**
     * 健康管理 - 健康数据分析和建议
     */
    public DashScopeAiUtil.AiResponse healthManagement(Long userId, String healthData, String analysisType) {
        log.info("健康管理请求 - 用户ID: {}, 健康数据: {}, 分析类型: {}", userId, healthData, analysisType);

        String sessionId = getUserSessionId(userId, "health");
        String enhancedPrompt = buildHealthPrompt(healthData, analysisType);

        DashScopeAiUtil.AiResponse response = dashScopeAiUtil.callWithSession(enhancedPrompt, sessionId);
        updateUserSession(userId, "health", response.getSessionId());

        return response;
    }

    /**
     * 健康管理 - 流式输出
     */
    public SseEmitter healthManagementStream(Long userId, String healthData, String analysisType) {
        log.info("健康管理流式请求 - 用户ID: {}, 健康数据: {}, 分析类型: {}", userId, healthData, analysisType);

        String sessionId = getUserSessionId(userId, "health");
        String enhancedPrompt = buildHealthPrompt(healthData, analysisType);

        // 使用优化的流式输出，60秒超时，按字符分割
        return dashScopeAiUtil.optimizedStreamCall(enhancedPrompt, sessionId, "word");
    }

    /**
     * AI文生图 - 真实图片生成（使用通义万相）
     */
    public DashScopeAiUtil.AiResponse imageGeneration(Long userId, String prompt, String style, String size) {
        log.info("AI文生图请求 - 用户ID: {}, 提示词: {}, 风格: {}, 尺寸: {}", userId, prompt, style, size);

        try {
            // 调用真实的AI图片生成服务
            String imageUrl = generateRealImage(prompt, style, size);

            if (imageUrl != null && !imageUrl.isEmpty()) {
                // 创建成功响应
                DashScopeAiUtil.AiResponse response = new DashScopeAiUtil.AiResponse(
                    imageUrl, // 返回真实生成的图片URL
                    "image_" + System.currentTimeMillis(),
                    true,
                    null
                );

                log.info("AI文生图完成 - 用户ID: {}, 图片URL: {}", userId, imageUrl);
                return response;
            } else {
                log.error("AI文生图失败 - 未能生成有效的图片URL");
                // 返回AI的原始响应，让用户了解失败原因
                String errorMessage = "图片生成失败，请重试";
                if (lastAiResponse != null && !lastAiResponse.trim().isEmpty()) {
                    errorMessage = lastAiResponse;
                }
                return new DashScopeAiUtil.AiResponse(
                    errorMessage, // 将AI响应作为文本内容返回
                    null,
                    false,
                    errorMessage
                );
            }

        } catch (Exception e) {
            log.error("AI文生图异常: {}", e.getMessage(), e);

            String errorMessage = "图片生成异常: " + e.getMessage();
            if (lastAiResponse != null && !lastAiResponse.trim().isEmpty()) {
                errorMessage = lastAiResponse + "\n\n系统异常: " + e.getMessage();
            }

            return new DashScopeAiUtil.AiResponse(
                errorMessage, // 将AI响应和异常信息作为文本内容返回
                null,
                false,
                errorMessage
            );
        }
    }

    /**
     * 调用真实的AI图片生成服务（通义万相）
     */
    private String generateRealImage(String prompt, String style, String size) {
        try {
            log.info("开始调用通义万相图片生成服务，提示词: {}", prompt);

            // 直接调用专门的图片生成方法
            DashScopeAiUtil.AiResponse response = dashScopeAiUtil.generateImage(prompt, style, size);

            if (response.isSuccess() && response.getText() != null) {
                // 从响应中提取图片URL
                String imageUrl = extractImageUrlFromResponse(response.getText());
                if (imageUrl != null && !imageUrl.isEmpty()) {
                    log.info("通义万相生成图片成功: {}", imageUrl);
                    return imageUrl;
                } else {
                    // 如果无法提取URL，直接返回响应文本（可能就是URL）
                    String responseText = response.getText().trim();
                    if (responseText.startsWith("http")) {
                        log.info("直接使用响应文本作为图片URL: {}", responseText);
                        return responseText;
                    } else {
                        // 无法提取URL，但保存AI响应用于错误显示
                        log.warn("无法从AI响应中提取图片URL，保存响应内容用于显示");
                        this.lastAiResponse = responseText;
                        return null;
                    }
                }
            } else {
                // 保存AI的错误响应
                if (response.getText() != null) {
                    this.lastAiResponse = response.getText();
                } else if (response.getErrorMessage() != null) {
                    this.lastAiResponse = response.getErrorMessage();
                }
            }

            log.warn("通义万相未返回有效图片URL，响应: {}", response.getText());
            return null;

        } catch (Exception e) {
            log.error("调用通义万相生成图片失败: {}", e.getMessage(), e);
            this.lastAiResponse = "调用AI服务异常: " + e.getMessage();
            return null;
        }
    }

    /**
     * 构建图片生成提示词
     */
    private String buildImagePrompt(String prompt, String style, String size) {
        StringBuilder enhancedPrompt = new StringBuilder();

        // 使用更直接的图片生成提示词格式
        enhancedPrompt.append("请生成一张图片：").append(prompt);

        // 添加风格描述（简化）
        enhancedPrompt.append("风格：");
        switch (style.toLowerCase()) {
            case "realistic":
                enhancedPrompt.append("写实风格");
                break;
            case "cartoon":
                enhancedPrompt.append("卡通风格");
                break;
            case "anime":
                enhancedPrompt.append("动漫风格");
                break;
            case "oil_painting":
                enhancedPrompt.append("油画风格");
                break;
            case "watercolor":
                enhancedPrompt.append("水彩画风格");
                break;
            case "sketch":
                enhancedPrompt.append("素描风格");
                break;
            case "abstract":
                enhancedPrompt.append("抽象风格");
                break;
            case "pixel":
                enhancedPrompt.append("像素风格");
                break;
            default:
                enhancedPrompt.append("写实风格");
        }


        // 添加尺寸
        enhancedPrompt.append("，尺寸：").append(size);

        // 简化结尾
        enhancedPrompt.append("。请生成图片并返回图片URL。");

        return enhancedPrompt.toString();
    }

    /**
     * 从AI响应中提取图片URL
     */
    private String extractImageUrlFromResponse(String responseText) {
        if (responseText == null || responseText.trim().isEmpty()) {
            return null;
        }

        // 首先尝试匹配Markdown格式的图片链接：![](url)
        java.util.regex.Pattern markdownPattern = java.util.regex.Pattern.compile("!\\[.*?\\]\\((https?://[^\\)]+)\\)");
        java.util.regex.Matcher markdownMatcher = markdownPattern.matcher(responseText);
        if (markdownMatcher.find()) {
            String url = markdownMatcher.group(1).trim();
            log.info("从Markdown格式提取到完整图片URL: {}", url);
            return url;
        }

        // 如果没有Markdown格式，尝试匹配各种可能的图片URL格式
        String[] urlPatterns = {
            "https?://dashscope-result[^\\s]+\\.(?:png|jpg|jpeg|gif|webp|bmp)[^\\s]*",
            "https?://[^\\s]+\\.aliyuncs\\.com[^\\s]*\\.(?:png|jpg|jpeg|gif|webp|bmp)[^\\s]*",
            "https?://[^\\s]+\\.(?:png|jpg|jpeg|gif|webp|bmp)[^\\s]*",
            "https?://[^\\s]+"
        };

        for (String pattern : urlPatterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(responseText);
            if (m.find()) {
                String url = m.group().trim();
                // 只移除明显的尾随字符，保留查询参数
                url = url.replaceAll("[)\\]\"'>\\s]+$", "");
                log.info("从响应中提取到图片URL: {}", url);
                return url;
            }
        }

        log.warn("无法从响应中提取图片URL，响应内容: {}", responseText);
        return null;
    }



    /**
     * 构建游戏提示词
     */
    private String buildGamePrompt(String gameType, String userInput, String gameState) {
        StringBuilder prompt = new StringBuilder();

        switch (gameType) {
            case "riddle":
                if ("start".equals(gameState)) {
                    prompt.append("你是一个有趣的谜语大师。请出一个适合学生的谜语，并等待用户回答。谜语要有趣且有教育意义。");
                } else {
                    prompt.append("用户的回答是：").append(userInput)
                          .append("。请判断答案是否正确，如果正确就夸奖并出下一个谜语，如果错误就给出提示。");
                }
                break;
            case "idiom":
                if ("start".equals(gameState)) {
                    prompt.append("我们来玩成语接龙游戏！我先说一个成语：一心一意。请你接一个以'意'字开头的成语。");
                } else {
                    prompt.append("用户接的成语是：").append(userInput)
                          .append("。请判断这个成语是否正确，然后接一个新的成语继续游戏。");
                }
                break;
            case "quiz":
                if ("start".equals(gameState)) {
                    prompt.append("我们来玩知识问答游戏！我会出一道适合学生的选择题，请你选择正确答案。");
                } else {
                    prompt.append("用户的答案是：").append(userInput)
                          .append("。请判断答案是否正确，解释原因，然后出下一道题。");
                }
                break;
            case "story":
                if ("start".equals(gameState)) {
                    prompt.append("我们来玩故事接龙游戏！我先开始：在一个遥远的森林里，住着一只聪明的小狐狸...");
                } else {
                    prompt.append("用户接的故事是：").append(userInput)
                          .append("。请继续这个故事，让情节更加有趣。");
                }
                break;
            default:
                prompt.append("用户想要玩游戏：").append(userInput).append("。请提供有趣的互动游戏体验。");
        }

        return prompt.toString();
    }



    /**
     * 构建健康提示词
     */
    private String buildHealthPrompt(String healthData, String analysisType) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("作为一个专业的健康管理顾问，请分析以下健康数据并提供建议：\n");
        prompt.append("健康数据：").append(healthData).append("\n");
        prompt.append("分析类型：").append(analysisType).append("\n");
        prompt.append("请提供：\n");
        prompt.append("1. 健康状况评估\n");
        prompt.append("2. 具体的改善建议\n");
        prompt.append("3. 日常生活指导\n");
        prompt.append("4. 注意事项\n");
        prompt.append("建议要实用、具体，适合学生群体。");

        return prompt.toString();
    }



}
