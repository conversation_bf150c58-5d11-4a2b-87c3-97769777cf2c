<view class="chat-container data-v-a041b13f"><view class="header data-v-a041b13f"><view class="header-content data-v-a041b13f"><text class="title data-v-a041b13f">即时通讯</text><view class="header-right data-v-a041b13f"><view class="add-icon data-v-a041b13f" bindtap="{{a}}"><text class="icon data-v-a041b13f">+</text></view></view></view></view><scroll-view class="chat-list data-v-a041b13f" scroll-y="true" enhanced="{{true}}" bounces="{{true}}" show-scrollbar="{{false}}" scroll-anchoring="{{true}}" fast-deceleration="{{false}}"><view wx:for="{{b}}" wx:for-item="user" wx:key="i" class="chat-item data-v-a041b13f" bindtap="{{user.j}}"><view class="avatar data-v-a041b13f"><text class="avatar-text data-v-a041b13f">{{user.a}}</text><view wx:if="{{user.b}}" class="unread-badge data-v-a041b13f"><text class="unread-count data-v-a041b13f">{{user.c}}</text></view></view><view class="chat-content data-v-a041b13f"><view class="chat-header data-v-a041b13f"><text class="friend-name data-v-a041b13f">{{user.d}}</text><text class="chat-time data-v-a041b13f">{{user.e}}</text></view><view class="message-row data-v-a041b13f"><text class="{{['last-message', 'data-v-a041b13f', user.g && 'unread-message']}}">{{user.f}}</text><view wx:if="{{user.h}}" class="mute-icon data-v-a041b13f"><text class="data-v-a041b13f">🔇</text></view></view></view></view></scroll-view><view wx:if="{{c}}" class="loading-state data-v-a041b13f"><text class="data-v-a041b13f">加载中...</text></view><view wx:if="{{d}}" class="empty-state data-v-a041b13f"><text class="empty-text data-v-a041b13f">暂无好友</text><text class="empty-hint data-v-a041b13f">点击右下角"+"添加好友开始聊天</text></view><tab-bar wx:if="{{f}}" class="data-v-a041b13f" bindchange="{{e}}" u-i="a041b13f-0" bind:__l="__l" u-p="{{f}}"/><view wx:if="{{g}}" class="add-menu-modal data-v-a041b13f" bindtap="{{o}}"><view class="add-menu data-v-a041b13f" catchtap="{{n}}"><view class="add-menu-item data-v-a041b13f" bindtap="{{h}}"><view class="menu-item-content data-v-a041b13f"><text class="add-menu-icon data-v-a041b13f">👤</text><text class="add-menu-text data-v-a041b13f">添加好友</text></view></view><view class="add-menu-item data-v-a041b13f" bindtap="{{k}}"><view class="menu-item-content data-v-a041b13f"><text class="add-menu-icon data-v-a041b13f">👥</text><text class="add-menu-text data-v-a041b13f">好友申请</text><text wx:if="{{i}}" class="badge data-v-a041b13f">{{j}}</text></view></view><view class="add-menu-item data-v-a041b13f" bindtap="{{l}}"><view class="menu-item-content data-v-a041b13f"><text class="add-menu-icon data-v-a041b13f">🧪</text><text class="add-menu-text data-v-a041b13f">测试未读消息</text></view></view><view class="add-menu-item data-v-a041b13f" bindtap="{{m}}"><view class="menu-item-content data-v-a041b13f"><text class="add-menu-icon data-v-a041b13f">🚪</text><text class="add-menu-text data-v-a041b13f">退出登录</text></view></view></view></view></view>