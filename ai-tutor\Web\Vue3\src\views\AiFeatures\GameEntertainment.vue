<template>
  <div class="game-entertainment-page">
    <van-nav-bar
      title="游戏娱乐"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
      class="custom-nav-bar"
    />

    <div class="feature-intro">
      <div class="intro-content">
        <div class="intro-icon">🎮</div>
        <div class="intro-text">
          <h3>AI游戏娱乐</h3>
          <p>语音游戏互动，寓教于乐</p>
        </div>
      </div>
    </div>

    <div class="games-container">
      <div class="games-grid">
        <div
          v-for="game in games"
          :key="game.id"
          class="game-card"
          @click="startGame(game)"
        >
          <div class="game-icon">{{ game.icon }}</div>
          <div class="game-info">
            <h4>{{ game.name }}</h4>
            <p>{{ game.description }}</p>
          </div>
        </div>
      </div>

      <div v-if="currentGame" class="game-area">
        <div class="game-header">
          <h3>{{ currentGame.name }}</h3>
          <van-button size="small" @click="endGame">结束游戏</van-button>
        </div>

        <div class="game-content">
          <div v-if="gameMessages.length > 0" class="game-messages">
            <div
              v-for="(message, index) in gameMessages"
              :key="index"
              :class="['game-message', message.type]"
            >
              {{ message.content }}
            </div>
          </div>

          <div class="game-input">
            <van-field
              v-model="gameInput"
              :placeholder="getInputPlaceholder()"
              @keyup.enter="sendGameInput"
            />
            <van-button
              type="primary"
              :loading="isLoading"
              @click="sendGameInput"
            >
              发送
            </van-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'
import aiService from '@/services/aiService'

const currentGame = ref<any>(null)
const gameInput = ref('')
const gameMessages = ref<Array<{
  type: 'user' | 'ai'
  content: string
}>>([])
const isLoading = ref(false)

const games = ref([
  {
    id: 'riddle',
    name: '猜谜语',
    description: '我出谜语你来猜',
    icon: '🤔'
  },
  {
    id: 'idiom',
    name: '成语接龙',
    description: '成语接龙挑战',
    icon: '📚'
  },
  {
    id: 'story',
    name: '故事接龙',
    description: '一起编故事',
    icon: '📖'
  },
  {
    id: 'quiz',
    name: '知识问答',
    description: '趣味知识竞赛',
    icon: '🧠'
  }
])

const startGame = (game: any) => {
  currentGame.value = game
  gameMessages.value = []

  // 游戏开始消息
  const startMessage = getStartMessage(game.id)
  gameMessages.value.push({
    type: 'ai',
    content: startMessage
  })
}

const endGame = () => {
  currentGame.value = null
  gameMessages.value = []
  gameInput.value = ''
}

const getStartMessage = (gameId: string) => {
  const messages = {
    riddle: '欢迎来到猜谜语游戏！我会给你出一个谜语，你来猜答案。准备好了吗？',
    idiom: '成语接龙开始！我先说一个成语，你接下一个。规则：后一个成语的第一个字要和前一个成语的最后一个字相同。',
    story: '故事接龙游戏开始！我们一起编一个有趣的故事，我先开头，你来接下去。',
    quiz: '知识问答游戏开始！我会问你一些有趣的问题，看看你能答对多少。'
  }
  return messages[gameId] || '游戏开始！'
}

const getInputPlaceholder = () => {
  if (!currentGame.value) return ''

  const placeholders = {
    riddle: '输入你的答案...',
    idiom: '输入成语...',
    story: '继续故事...',
    quiz: '输入答案...'
  }
  return placeholders[currentGame.value.id] || '输入内容...'
}

const sendGameInput = async () => {
  if (!gameInput.value.trim() || !currentGame.value) return

  gameMessages.value.push({
    type: 'user',
    content: gameInput.value
  })

  const userInput = gameInput.value
  gameInput.value = ''
  isLoading.value = true

  // 添加AI消息占位符
  const aiMessageIndex = gameMessages.value.length
  gameMessages.value.push({
    type: 'ai',
    content: ''
  })

  let accumulatedText = ''

  try {
    // 使用流式输出
    await aiService.gameEntertainmentStream(
      12345,
      currentGame.value.id,
      userInput,
      // onMessage回调
      (content: string) => {
        accumulatedText += content
        gameMessages.value[aiMessageIndex].content = accumulatedText
      },
      // onComplete回调
      () => {
        isLoading.value = false
      },
      // onError回调
      async (error: any) => {
        console.error('流式输出失败，尝试非流式输出:', error)

        // 流式输出失败，尝试非流式输出
        try {
          const response = await aiService.gameEntertainment(12345, currentGame.value.id, userInput, 'playing')
          if (response.code === 200) {
            gameMessages.value[aiMessageIndex].content = response.data || '游戏继续进行中...'
          } else {
            gameMessages.value[aiMessageIndex].content = '游戏出现了小问题，让我们继续吧！'
          }
        } catch (fallbackError) {
          console.error('非流式输出也失败:', fallbackError)
          gameMessages.value[aiMessageIndex].content = '游戏出现了小问题，让我们继续吧！'
        }

        isLoading.value = false
      }
    )

  } catch (error) {
    console.error('游戏娱乐失败:', error)

    // 备选方案：直接调用非流式API
    try {
      const response = await aiService.gameEntertainment(12345, currentGame.value.id, userInput, 'playing')
      if (response.code === 200) {
        gameMessages.value[aiMessageIndex].content = response.data || '游戏继续进行中...'
      } else {
        gameMessages.value[aiMessageIndex].content = '游戏出现了小问题，让我们继续吧！'
      }
    } catch (fallbackError) {
      console.error('备选方案失败:', fallbackError)
      gameMessages.value[aiMessageIndex].content = '游戏出现了小问题，让我们继续吧！'
    }

    isLoading.value = false
  }
}
</script>

<style scoped>
.game-entertainment-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.feature-intro {
  margin: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.intro-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 154, 158, 0.1);
  border-radius: 16px;
}

.intro-text h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.intro-text p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.games-container {
  padding: 16px;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.game-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.game-card:hover {
  transform: translateY(-2px);
}

.game-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.game-info h4 {
  margin: 0 0 4px 0;
  color: #2c3e50;
}

.game-info p {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
}

.game-area {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.game-header h3 {
  margin: 0;
  color: #2c3e50;
}

.game-messages {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.game-message {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 12px;
  font-size: 14px;
}

.game-message.user {
  background: #e3f2fd;
  text-align: right;
}

.game-message.ai {
  background: #f3e5f5;
}

.game-input {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}
</style>
