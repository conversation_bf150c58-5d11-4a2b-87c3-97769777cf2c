"use strict";
const common_vendor = require("../common/vendor.js");
const BASE_URL = "http://localhost:8080/api";
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    const token = common_vendor.index.getStorageSync("token");
    common_vendor.index.request({
      url: BASE_URL + url,
      method: options.method || "GET",
      data: options.data,
      header: {
        "Content-Type": "application/json",
        "Authorization": token ? `Bearer ${token}` : "",
        ...options.header
      },
      success: (res) => {
        console.log("API请求成功:", url, res);
        resolve(res.data);
      },
      fail: (err) => {
        console.error("API请求失败:", url, err);
        let errorMsg = "网络请求失败";
        if (err.statusCode) {
          switch (err.statusCode) {
            case 401:
              errorMsg = "登录已过期，请重新登录";
              common_vendor.index.removeStorageSync("token");
              common_vendor.index.removeStorageSync("userInfo");
              common_vendor.index.reLaunch({
                url: "/pages/login/login"
              });
              break;
            case 403:
              errorMsg = "没有权限访问";
              break;
            case 404:
              errorMsg = "请求的资源不存在";
              break;
            case 500:
              errorMsg = "服务器内部错误";
              break;
            default:
              errorMsg = err.errMsg || err.message || "网络请求失败";
          }
        } else {
          errorMsg = err.errMsg || err.message || "网络连接失败，请检查网络设置";
        }
        const error = new Error(errorMsg);
        error.statusCode = err.statusCode;
        error.errMsg = err.errMsg;
        error.url = url;
        error.requestData = options.data;
        reject(error);
      }
    });
  });
}
function login(data) {
  return request("/auth/login", {
    method: "POST",
    data
  });
}
function register(data) {
  return request("/auth/register", {
    method: "POST",
    data
  });
}
function getUserList() {
  return request("/auth/users");
}
function getChatHistory(toUserId, params = {}) {
  let url = `/messages/history/${toUserId}`;
  if (params.page || params.size) {
    const queryParams = new URLSearchParams();
    if (params.page)
      queryParams.append("page", params.page);
    if (params.size)
      queryParams.append("size", params.size);
    url += `?${queryParams.toString()}`;
  }
  return request(url);
}
function sendMessage(data) {
  return request("/messages/send", {
    method: "POST",
    data
  });
}
function markSessionAsRead(sessionId) {
  return request(`/messages/session/${sessionId}/read`, {
    method: "POST"
  });
}
function searchUsers(keyword) {
  return request(`/friend/search?keyword=${encodeURIComponent(keyword)}`);
}
function sendFriendRequest(data) {
  return request("/friend/request", {
    method: "POST",
    data
  });
}
function getReceivedFriendRequests() {
  return request("/friend/requests/received");
}
function getSentFriendRequests() {
  return request("/friend/requests/sent");
}
function handleFriendRequest(requestId, action) {
  return request(`/friend/requests/${requestId}/${action}`, {
    method: "POST"
  });
}
function getFriendList() {
  return request("/friend/list");
}
function deleteFriend(friendId) {
  return request(`/friend/${friendId}`, {
    method: "DELETE"
  });
}
function setFriendRemark(friendId, remark) {
  return request(`/friend/${friendId}/remark`, {
    method: "PUT",
    data: { remark }
  });
}
function getPendingRequestCount() {
  return request("/friend/requests/count");
}
exports.deleteFriend = deleteFriend;
exports.getChatHistory = getChatHistory;
exports.getFriendList = getFriendList;
exports.getPendingRequestCount = getPendingRequestCount;
exports.getReceivedFriendRequests = getReceivedFriendRequests;
exports.getSentFriendRequests = getSentFriendRequests;
exports.getUserList = getUserList;
exports.handleFriendRequest = handleFriendRequest;
exports.login = login;
exports.markSessionAsRead = markSessionAsRead;
exports.register = register;
exports.searchUsers = searchUsers;
exports.sendFriendRequest = sendFriendRequest;
exports.sendMessage = sendMessage;
exports.setFriendRemark = setFriendRemark;
