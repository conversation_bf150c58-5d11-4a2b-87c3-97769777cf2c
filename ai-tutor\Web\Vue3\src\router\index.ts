import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomePage/index.vue'),
    meta: { title: 'AI智能助手' }
  },
  {
    path: '/voice-call',
    name: 'VoiceCall',
    component: () => import('@/views/VoiceCall/index.vue'),
    meta: { title: '语音通话' }
  },
  // AI功能页面路由
  {
    path: '/ai-features',
    name: 'AiFeatures',
    component: () => import('@/views/AiFeatures/index.vue'),
    meta: { title: 'AI功能大全' }
  },
  {
    path: '/ai-features/knowledge-qa',
    name: 'KnowledgeQA',
    component: () => import('@/views/AiFeatures/KnowledgeQA.vue'),
    meta: { title: '知识问答' }
  },
  {
    path: '/ai-features/information-query',
    name: 'InformationQuery',
    component: () => import('@/views/AiFeatures/InformationQuery.vue'),
    meta: { title: '信息查询' }
  },
  {
    path: '/ai-features/text-generation',
    name: 'TextGeneration',
    component: () => import('@/views/AiFeatures/TextGeneration.vue'),
    meta: { title: '文本生成' }
  },
  {
    path: '/ai-features/translation',
    name: 'Translation',
    component: () => import('@/views/AiFeatures/Translation.vue'),
    meta: { title: '语言翻译' }
  },
  {
    path: '/ai-features/emotional-companion',
    name: 'EmotionalCompanion',
    component: () => import('@/views/AiFeatures/EmotionalCompanion.vue'),
    meta: { title: '情感陪伴' }
  },
  {
    path: '/ai-features/recommendation',
    name: 'Recommendation',
    component: () => import('@/views/AiFeatures/Recommendation.vue'),
    meta: { title: '智能推荐' }
  },
  {
    path: '/ai-features/game-entertainment',
    name: 'GameEntertainment',
    component: () => import('@/views/AiFeatures/GameEntertainment.vue'),
    meta: { title: '游戏娱乐' }
  },
  {
    path: '/ai-features/health-management',
    name: 'HealthManagement',
    component: () => import('@/views/AiFeatures/HealthManagement.vue'),
    meta: { title: '健康管理' }
  },
  {
    path: '/ai-features/image-generation',
    name: 'ImageGeneration',
    component: () => import('@/views/AiFeatures/ImageGeneration.vue'),
    meta: { title:'AI文生图' }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router
