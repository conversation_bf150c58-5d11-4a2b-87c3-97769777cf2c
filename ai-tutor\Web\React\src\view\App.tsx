import { useEffect, useState } from 'react';
import Welcome from './Welcome';
import { AICallAgentConfig, AICallAgentType } from 'aliyun-auikit-aicall';

import './App.css';
import { Toast } from 'antd-mobile';
import Call from './Call';
import runUserConfig from '@/runConfig.ts';
import { JSONObject } from '@/service/interface.ts';
import { getCallAgentId, getRuntimeConfig } from '@/interface.ts';
import service from '@/service/standard';
import { useTranslation } from '@/common/i18nContext';

Toast.config({
  position: 'bottom',
});

interface AppProps {
  mode?: 'standard' | 'proxy';
  userId?: string;
  userToken?: string;

  shareToken?: string;
  appServer?: string;
  region?: string;

  agentType?: AICallAgentType;
  agentId?: string;

  userData?: string | JSONObject;
  agentConfig?: AICallAgentConfig;

  onAuthFail?: () => void;
}

function App(props: AppProps) {
  const runConfig = getRuntimeConfig(runUserConfig);
  const {
    mode,
    userId = 'YourUserId',
    userToken = 'YourToken',
    shareToken,
    appServer = runConfig.appServer,
    region = runConfig.region,
    onAuthFail,
    agentType = runConfig.agentType,
    agentId,
    userData,
    agentConfig = runConfig.callAgentConfig,
  } = props;
  const [stateAgentType, setStateAgentType] = useState<AICallAgentType | undefined>(agentType);

  useEffect(() => {
    if (appServer) {
      service.setAppServer(appServer);
    }

    const preventContextMenu = function (e: Event) {
      e.preventDefault();
    };
    // 禁用右键菜单
    // disable context menu
    document.addEventListener('contextmenu', preventContextMenu);
    return () => {
      document.removeEventListener('contextmenu', preventContextMenu);
    };
  }, [appServer]);

  if (stateAgentType === undefined)
    return (
      <Welcome
        onAgentTypeSelected={(type) => {
          setStateAgentType(type);
        }}
      />
    );

  return (
    <Call
      mode={mode}
      rc={runConfig}
      autoCall
      userId={userId}
      userToken={userToken}
      agentType={stateAgentType}
      shareToken={shareToken}
      agentId={agentId || getCallAgentId(runConfig, stateAgentType)}
      appServer={appServer}
      region={region}
      userData={typeof userData === 'object' ? JSON.stringify(userData) : userData || runConfig.callUserData}
      agentConfig={agentConfig}
      onExit={() => {
        if (!shareToken) {
          setStateAgentType(undefined);
        }
      }}
      onAuthFail={() => {
        onAuthFail?.();
      }}
    />
  );
}

function AppWithHeader(props: AppProps) {
  const { t } = useTranslation();

  return (
    <>
      <div className='layout-header'>{t('welcome.title')}</div>
      <App {...props} />
    </>
  );
}

export default AppWithHeader;
