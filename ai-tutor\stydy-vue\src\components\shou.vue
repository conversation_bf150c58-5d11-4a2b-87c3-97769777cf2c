<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <el-card class="welcome-card" shadow="hover">
      <div class="welcome-content">
        <div class="welcome-left">
          <div class="welcome-avatar">
            <el-avatar :size="60" :src="user.avatar">
              <el-icon size="30"><User /></el-icon>
            </el-avatar>
          </div>
          <div class="welcome-info">
            <h2>欢迎回来，{{ user.nickname || user.username || '管理员' }}！</h2>
            <p>今天是 {{ currentDate }}，{{ getGreeting() }}</p>
            <p class="welcome-desc">智慧家教管理系统为您提供全面的家教服务管理</p>
          </div>
        </div>
        <div class="welcome-right">
          <div class="weather-info">
            <el-icon size="24" class="weather-icon"><Sunny /></el-icon>
            <span>晴朗 22°C</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6" :xs="12" :sm="12" :md="6">
        <el-card class="stats-card student-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32"><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.students }}</div>
              <div class="stats-label">学生总数</div>
              <div class="stats-trend">
                <el-icon class="trend-up"><TrendCharts /></el-icon>
                <span>+12%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" :xs="12" :sm="12" :md="6">
        <el-card class="stats-card teacher-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32"><Avatar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.teachers }}</div>
              <div class="stats-label">教师总数</div>
              <div class="stats-trend">
                <el-icon class="trend-up"><TrendCharts /></el-icon>
                <span>+8%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" :xs="12" :sm="12" :md="6">
        <el-card class="stats-card course-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32"><Reading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.courses }}</div>
              <div class="stats-label">课程总数</div>
              <div class="stats-trend">
                <el-icon class="trend-up"><TrendCharts /></el-icon>
                <span>+15%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6" :xs="12" :sm="12" :md="6">
        <el-card class="stats-card revenue-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32"><Money /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ stats.revenue.toLocaleString() }}</div>
              <div class="stats-label">本月收入</div>
              <div class="stats-trend">
                <el-icon class="trend-up"><TrendCharts /></el-icon>
                <span>+25%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 快捷操作 -->
      <el-col :span="8">
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="18"><Lightning /></el-icon>
              <span>快捷操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <div class="action-item" @click="quickAction('addStudent')">
              <div class="action-icon student-action">
                <el-icon><Plus /></el-icon>
              </div>
              <span>新增学生</span>
            </div>
            <div class="action-item" @click="quickAction('addTeacher')">
              <div class="action-icon teacher-action">
                <el-icon><Plus /></el-icon>
              </div>
              <span>新增教师</span>
            </div>
            <div class="action-item" @click="quickAction('addCourse')">
              <div class="action-icon course-action">
                <el-icon><Plus /></el-icon>
              </div>
              <span>新增课程</span>
            </div>
            <div class="action-item" @click="quickAction('schedule')">
              <div class="action-icon schedule-action">
                <el-icon><Calendar /></el-icon>
              </div>
              <span>课程安排</span>
            </div>
            <div class="action-item" @click="quickAction('report')">
              <div class="action-icon report-action">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <span>数据报表</span>
            </div>
            <div class="action-item" @click="quickAction('settings')">
              <div class="action-icon settings-action">
                <el-icon><Setting /></el-icon>
              </div>
              <span>系统设置</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :span="8">
        <el-card class="recent-activities-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="18"><Clock /></el-icon>
              <span>最近活动</span>
            </div>
          </template>
          <div class="activities-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-icon" :class="activity.type">
                <el-icon><component :is="activity.icon" /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 系统通知 -->
      <el-col :span="8">
        <el-card class="notifications-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="18"><Bell /></el-icon>
              <span>系统通知</span>
              <el-badge :value="notifications.filter(n => !n.read).length" class="notification-badge" />
            </div>
          </template>
          <div class="notifications-list">
            <div class="notification-item" v-for="notification in notifications" :key="notification.id" :class="{ unread: !notification.read }">
              <div class="notification-icon" :class="notification.type">
                <el-icon><component :is="notification.icon" /></el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-desc">{{ notification.desc }}</div>
                <div class="notification-time">{{ notification.time }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="18"><TrendCharts /></el-icon>
              <span>学生增长趋势</span>
            </div>
          </template>
          <div class="chart-placeholder">
            <el-icon size="64" class="chart-icon"><TrendCharts /></el-icon>
            <p>学生注册趋势图表</p>
            <small>（图表功能开发中...）</small>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon size="18"><PieChart /></el-icon>
              <span>课程分布</span>
            </div>
          </template>
          <div class="chart-placeholder">
            <el-icon size="64" class="chart-icon"><PieChart /></el-icon>
            <p>课程类型分布图</p>
            <small>（图表功能开发中...）</small>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { 
  User, Sunny, UserFilled, Avatar, Reading, Money, TrendCharts,
  Lightning, Plus, Calendar, DataAnalysis, Setting, Clock, Bell,
  PieChart
} from '@element-plus/icons-vue'

const user = ref(JSON.parse(localStorage.getItem('user') || '{}'))

// 统计数据
const stats = ref({
  students: 168,
  teachers: 32,
  courses: 45,
  revenue: 125800
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'student',
    icon: 'UserFilled',
    title: '新学生张小明注册成功',
    time: '2分钟前'
  },
  {
    id: 2,
    type: 'course',
    icon: 'Reading',
    title: '数学课程已更新',
    time: '15分钟前'
  },
  {
    id: 3,
    type: 'teacher',
    icon: 'Avatar',
    title: '李老师完成课程评价',
    time: '1小时前'
  },
  {
    id: 4,
    type: 'system',
    icon: 'Setting',
    title: '系统维护完成',
    time: '2小时前'
  }
])

// 系统通知
const notifications = ref([
  {
    id: 1,
    type: 'warning',
    icon: 'Warning',
    title: '系统维护通知',
    desc: '系统将于今晚22:00-24:00进行维护',
    time: '今天 14:30',
    read: false
  },
  {
    id: 2,
    type: 'info',
    icon: 'InfoFilled',
    title: '新功能上线',
    desc: '课程评价功能已上线，欢迎体验',
    time: '昨天 16:20',
    read: false
  },
  {
    id: 3,
    type: 'success',
    icon: 'SuccessFilled',
    title: '数据备份完成',
    desc: '系统数据已成功备份',
    time: '昨天 10:15',
    read: true
  }
])

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了，注意休息'
  if (hour < 9) return '早上好，新的一天开始了'
  if (hour < 12) return '上午好，工作顺利'
  if (hour < 14) return '中午好，记得休息'
  if (hour < 17) return '下午好，继续加油'
  if (hour < 19) return '傍晚好，一天辛苦了'
  if (hour < 22) return '晚上好，放松一下'
  return '夜深了，早点休息'
}

// 快捷操作
const quickAction = (action) => {
  const actions = {
    addStudent: () => ElMessage.info('新增学生功能开发中...'),
    addTeacher: () => ElMessage.info('新增教师功能开发中...'),
    addCourse: () => ElMessage.info('新增课程功能开发中...'),
    schedule: () => ElMessage.info('课程安排功能开发中...'),
    report: () => ElMessage.info('数据报表功能开发中...'),
    settings: () => ElMessage.info('系统设置功能开发中...')
  }
  
  actions[action]?.()
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里可以调用实际的统计API
    // const res = await axios.get('/dashboard/stats')
    // if (res.data.code === 200) {
    //   stats.value = res.data.data
    // }
    
    // 模拟数据更新
    setTimeout(() => {
      stats.value = {
        students: Math.floor(Math.random() * 200) + 150,
        teachers: Math.floor(Math.random() * 20) + 30,
        courses: Math.floor(Math.random() * 30) + 40,
        revenue: Math.floor(Math.random() * 50000) + 100000
      }
    }, 1000)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

.welcome-card {
  margin-bottom: 20px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.welcome-avatar {
  flex-shrink: 0;
}

.welcome-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-info p {
  margin: 4px 0;
  opacity: 0.9;
}

.welcome-desc {
  font-size: 14px;
  opacity: 0.8;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.weather-icon {
  color: #ffd700;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  border-radius: 12px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.student-card .stats-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.teacher-card .stats-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.course-card .stats-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-card .stats-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stats-label {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 8px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #27ae60;
  font-size: 12px;
  font-weight: 600;
}

.trend-up {
  color: #27ae60;
}

.content-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #f8f9fa;
}

.action-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.student-action { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.teacher-action { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.course-action { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.schedule-action { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.report-action { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.settings-action { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }

.activities-list,
.notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item,
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
}

.activity-item:last-child,
.notification-item:last-child {
  border-bottom: none;
}

.activity-icon,
.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-icon.student,
.notification-icon.info {
  background: #409eff;
}

.activity-icon.course {
  background: #67c23a;
}

.activity-icon.teacher {
  background: #e6a23c;
}

.activity-icon.system,
.notification-icon.warning {
  background: #f56c6c;
}

.notification-icon.success {
  background: #67c23a;
}

.activity-content,
.notification-content {
  flex: 1;
}

.activity-title,
.notification-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 13px;
  color: #7f8c8d;
  margin-bottom: 4px;
}

.activity-time,
.notification-time {
  font-size: 12px;
  color: #bbb;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-badge {
  margin-left: auto;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 12px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #bbb;
  text-align: center;
}

.chart-icon {
  margin-bottom: 16px;
  opacity: 0.6;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .stats-number {
    font-size: 24px;
  }
}
</style>