package cn.zhentao.service.impl;

import cn.zhentao.service.BaseService;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 通用Service实现类
 * 继承MyBatis Plus的ServiceImpl，实现基础的CRUD操作
 *
 * @param <M> Mapper类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 2024-07-28
 */
public class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements BaseService<T> {

    @Autowired
    protected M baseMapper;

    // 继承父类的所有方法，无需重复实现
    // MyBatis Plus的ServiceImpl已经提供了完整的CRUD操作
}
