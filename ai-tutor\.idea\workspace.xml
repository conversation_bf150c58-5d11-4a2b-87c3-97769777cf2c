<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a710a763-6387-4c46-8f91-b720a7aa803e" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.feisuan/rules/project_rule.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/dataSources.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-member/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-member/src/test/java/cn/zhentao/AppTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/PageResult$PageData.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/PageResult$PageData.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/PageResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/PageResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/Result.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/Result.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/ResultCode.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/common/ResultCode.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/config/MybatisPlusConfig$MyMetaObjectHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/config/MybatisPlusConfig$MyMetaObjectHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/config/MybatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/config/MybatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/controller/BaseController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/controller/BaseController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/generator/CodeGenerator.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/generator/CodeGenerator.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/MenuMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/MenuMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/MessagesMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/MessagesMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/PermissionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/PermissionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/RoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/RoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/RolePermissionMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/RolePermissionMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/UserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/UserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/UserRoleMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/mapper/UserRoleMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/BaseEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/BaseEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Menu.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Menu.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Messages.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Messages.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Permission.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Permission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Role.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/Role.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/RolePermission.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/RolePermission.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/User.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/User.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/UserRole.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/pojo/UserRole.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/BaseService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/BaseService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/MenuService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/MenuService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/MessagesService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/MessagesService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/PermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/PermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/RolePermissionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/RolePermissionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/RoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/RoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/UserRoleService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/UserRoleService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/UserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/UserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/BaseServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/BaseServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/MenuServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/MenuServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/MessagesServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/MessagesServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/PermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/PermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/RolePermissionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/RolePermissionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/RoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/RoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/UserRoleServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/UserRoleServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/UserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/service/impl/UserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/HttpUtils$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/HttpUtils$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/HttpUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/HttpUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/JwtUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/JwtUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/RegexPatterns.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/RegexPatterns.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/RegexUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-common/target/classes/cn/zhentao/util/RegexUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/StudyImApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/StudyImApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/CorsConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/CorsConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/GlobalExceptionHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/GlobalExceptionHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/NettyConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/NettyConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/RedisConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/RedisConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/ScheduleConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/ScheduleConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$Cache.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$Cache.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$OnlineStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$OnlineStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$RateLimit.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$RateLimit.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$Schedule.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig$Schedule.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/config/SystemConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/AuthController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/AuthController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/FriendController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/FriendController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/HealthController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/HealthController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/MessageController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/MessageController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/StatsController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/controller/StatsController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/ApiResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/ApiResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/LoginRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/LoginRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/MessageDto.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/MessageDto.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/RegisterRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/RegisterRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/SendMessageRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/dto/SendMessageRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/FriendRequest$RequestStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/FriendRequest$RequestStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/FriendRequest.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/FriendRequest.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Friendship$FriendshipStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Friendship$FriendshipStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Friendship.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Friendship.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Message$MessageStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Message$MessageStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Message$MessageType.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Message$MessageType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Message.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/Message.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/User$UserStatus.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/User$UserStatus.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/User.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/entity/User.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/CorsHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/CorsHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/NettyServer$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/NettyServer$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/NettyServer.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/NettyServer.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/WebSocketHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/netty/WebSocketHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/FriendRequestRepository.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/FriendRequestRepository.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/FriendshipRepository.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/FriendshipRepository.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/MessageRepository.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/MessageRepository.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/UserRepository.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/repository/UserRepository.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/FriendService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/FriendService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/InMemoryUserStatusService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/InMemoryUserStatusService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/MessageService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/MessageService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/RedisService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/RedisService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/UserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/service/UserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/util/JwtUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-im/target/classes/com/zhentao/studyim/util/JwtUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-im/target/classes/schema.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/UserLoginApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/UserLoginApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/ApplicationConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/ApplicationConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/CorsConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/CorsConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/DatabaseConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/DatabaseConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/SecurityConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/SecurityConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/UserLoginMybatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/UserLoginMybatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/WebMvcConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/config/WebMvcConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/HealthController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/HealthController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/LoginController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/LoginController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/MenuController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/MenuController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/PermissionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/PermissionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/RoleController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/RoleController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/TestController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/TestController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/UserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/controller/UserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/filter/TokenFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/filter/TokenFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/interceptor/PermissionInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/interceptor/PermissionInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/util/JwtService.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/util/JwtService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/util/JwtUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/util/JwtUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/util/WebCorsConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/util/WebCorsConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/vo/Constant.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/vo/Constant.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/vo/QueryParam.class" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/target/classes/cn/zhentao/vo/QueryParam.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/src/main/java/cn/zhentao/util/DashScopeAiUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/src/main/java/cn/zhentao/util/DashScopeAiUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/AppServiceApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/AppServiceApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/common/Result.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/common/Result.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/AiTestController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/HealthController.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/HealthController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/MiniProgramAiController.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/MiniProgramAiController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/VoiceCallController.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/controller/VoiceCallController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/service/AiConversationService.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/service/AiConversationService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/util/DashScopeAiUtil$AiResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/util/DashScopeAiUtil$AiResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/util/DashScopeAiUtil$ImageResponse.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/util/DashScopeAiUtil$ImageResponse.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/util/DashScopeAiUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/AiApp-service/target/classes/cn/zhentao/util/DashScopeAiUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logs/ai-app-service.log" beforeDir="false" afterPath="$PROJECT_DIR$/logs/ai-app-service.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="30tZl8CSOWylkuNQy6ukXX9h4AU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven. [org.apache.maven.plugins:maven-archetype-plugin:RELEASE:generate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.AppServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ArcsoftFaceServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.GatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.StudyImApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.UserLoginApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/wolk/study/ai-tutor&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.33448276&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="hiddenConfigurations">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <set>
              <option value="PcServiceApplication" />
            </set>
          </value>
        </entry>
      </map>
    </option>
    <option name="configurationStatuses">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <map>
              <entry key="AppServiceApplication" value="STOPPED" />
              <entry key="ArcsoftFaceServiceApplication" value="FAILED" />
              <entry key="GatewayApplication" value="STOPPED" />
              <entry key="StudyImApplication" value="STOPPED" />
              <entry key="UserLoginApplication" value="STOPPED" />
            </map>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ArcsoftFaceServiceApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="Ai-userlogin" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="Ai-userlogin" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="AppServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="AiApp-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.AppServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ArcsoftFaceServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-face" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.ArcsoftFaceServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PcServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="AiPC-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.PcServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StudyImApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-im" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.studyim.StudyImApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserLoginApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-userlogin" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.UserLoginApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.23892.409" />
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-IU-252.23892.409" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a710a763-6387-4c46-8f91-b720a7aa803e" name="Changes" comment="" />
      <created>1754448978513</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754448978513</updated>
      <workItem from="1754448980166" duration="907000" />
      <workItem from="1754449953222" duration="1082000" />
      <workItem from="1754451392251" duration="2018000" />
      <workItem from="1754528156488" duration="1377000" />
      <workItem from="1754529995056" duration="306000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="javascript:npm:react" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vite" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava2:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="javascript:npm:prettier" />
    <option featureType="dependencySupport" implementationName="java:jakarta.persistence:jakarta.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:org.openjfx:javafx-base" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vue" />
    <option featureType="dependencySupport" implementationName="java:org.thymeleaf:thymeleaf" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
</project>