export const CallPhoneSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='7.333333492279053'
    viewBox='0 0 20 7.333333492279053'
  >
    <g>
      <path
        d='M0.00111161,6.16018L0.00666531,4.0314C0.00666531,3.80376,2.4614,-0.0424691,9.99223,0.000354402C17.5897,0.0431775,20,3.91758,20,4.14522L19.9933,6.27512C19.9911,6.86226,19.5046,7.33669,18.9081,7.33332L15.1272,6.77999C14.5152,6.65941,14.0487,6.29654,14.0498,5.70828L14.0553,4.40554C12.7535,3.38116,11.6039,3.16929,10.2777,3.16253C8.78041,3.15352,7.32312,3.10168,5.95024,4.40329L5.9458,5.66207C5.94469,6.25033,5.47595,6.60757,4.86171,6.72139L1.07742,7.23189C0.464292,7.33895,-0.00110875,6.74956,0.00000200205,6.16018L0.00111161,6.16018Z'
        fill='#FFFFFF'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const MicrophoneSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='21.3333740234375'
    height='21.33349609375'
    viewBox='0 0 21.3333740234375 21.33349609375'
  >
    <g>
      <g>
        <path
          d='M6.294485625,5.960995L6.294485625,9.958575C6.294485625,12.275625,8.172815625,14.153925,10.489855625,14.153925L10.844165624999999,14.153925C13.161205625,14.153925,15.039565625,12.275625,15.039565625,9.958575L15.039565625,5.960995C15.039565625,3.643955,13.161225625,1.765625,10.844185625,1.765625L10.489855625,1.765625C8.172815625,1.765625,6.294485625,3.643955,6.294485625,5.960995ZM8.466095625000001,11.982325Q7.627825625,11.144065,7.627825625,9.958575L7.627825625,5.960995Q7.627825625,4.775494999999999,8.466095625000001,3.937225Q9.304365624999999,3.098955,10.489855625,3.098955L10.844185625,3.098955Q12.029675625,3.098955,12.867945625,3.937225Q13.706215625,4.775505,13.706215625,5.960995L13.706205625,9.958575Q13.706205625,11.144065,12.867935625,11.982325Q12.029665625,12.820625,10.844165624999999,12.820625L10.489855625,12.820625Q9.304365624999999,12.820625,8.466095625000001,11.982325ZM4.551432625,9.625215C4.183242625,9.625215,3.884765625,9.923695,3.884765625,10.291885Q3.884765625,12.926525,5.747705625,14.789425Q7.522595625,16.564325,9.997935625,16.648225L9.997935625,18.876725C9.997935625,19.244925,10.296415625,19.543425,10.664605625,19.543425C11.032795625,19.543425,11.331275625,19.244925,11.331275625,18.876725L11.331275625,16.648325Q13.809605625,16.566025,15.586165625,14.789425Q17.449065625,12.926425,17.449065625,10.291885C17.449065625,9.923695,17.150665625000002,9.625215,16.782465625,9.625215C16.414265625,9.625215,16.115765625,9.923695,16.115765625,10.291885Q16.115765625,12.374225,14.643365625,13.846625Q13.170955625,15.319025,11.088645625,15.319025L10.667235625,15.319025L10.664605625,15.319025L10.661975625,15.319025L10.245235625,15.319025Q8.162925625,15.319025,6.690515625,13.846625Q5.218355625,12.374425,5.218095625,10.292595C5.218095625,9.923695,4.919625625,9.625215,4.551432625,9.625215Z'
          fillRule='evenodd'
          fill='currentColor'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const MicrophoneClosedSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='21.3333740234375'
    height='21.3330078125'
    viewBox='0 0 21.3333740234375 21.3330078125'
  >
    <g>
      <g>
        <path
          d='M13.707940625,9.958575Q13.707940625,10.386455,13.598740625,10.769095L14.620840625,11.791225C14.890240625,11.237535,15.041240625,10.615695,15.041240625,9.958575L15.041240625,5.960995C15.041240625,3.643955,13.162940625,1.765625,10.845920625,1.765625L10.491590625,1.765625C8.895550625,1.765625,7.507660625,2.656867,6.798470625,3.968825L7.797890625,4.968245Q8.005200625,4.3998550000000005,8.467830625000001,3.937225Q9.306100625,3.098955,10.491590625,3.098955L10.845920625,3.098955Q12.031410625,3.098955,12.869680625,3.937225Q13.707940625,4.775505,13.707940625,5.960995L13.707940625,9.958575ZM4.131600625,3.187805C3.871589625,2.9277949999999997,3.450024625,2.9277949999999997,3.190011625,3.187805C2.929998825,3.447825,2.929999025,3.869385,3.190011625,4.129395000000001L17.199540624999997,18.138925C17.459540625000002,18.398925,17.881140625,18.398925,18.141140625,18.138925C18.401140625,17.878925,18.401140625,17.457425,18.141140625,17.197325L4.131600625,3.187805ZM6.296220625,9.958575L6.296220625,9.120995L7.676730625,10.501515Q7.828420625,11.342935,8.467830625000001,11.982325Q9.107230625,12.621725,9.948650624999999,12.773425L11.304400625,14.129225C11.153820625,14.145525,11.000840625,14.153925,10.845910625,14.153925L10.491590625,14.153925C8.174550625,14.153925,6.296220625,12.275625,6.296220625,9.958575ZM11.090380625,15.319025Q11.749150625,15.319025,12.346870625,15.171625L13.410540625,16.235325Q12.442060625,16.611525,11.333010625,16.648325L11.333010625,18.876725C11.333010625,19.244925,11.034530625,19.543425,10.666340625,19.543425C10.298150625,19.543425,9.999670625,19.244925,9.999670625,18.876725L9.999670625,16.648225Q7.524340625,16.564325,5.749440625,14.789425Q3.886501625,12.926525,3.886501625,10.291885C3.886501625,9.923695,4.184980625,9.625215,4.553170625,9.625215C4.921360625,9.625215,5.219830625,9.923695,5.219830625,10.292595Q5.220090625,12.374425,6.692250625,13.846625Q8.164660625,15.319025,10.246970625,15.319025L10.663710625,15.319025L10.666340625,15.319025L10.668970625,15.319025L11.090380625,15.319025ZM15.522540625,12.692825L16.496940625,13.667325Q17.450840624999998,12.177125,17.450840624999998,10.291885C17.450840624999998,9.923695,17.152340625,9.625215,16.784140625,9.625215C16.416040625,9.625215,16.117540625,9.923695,16.117540625,10.291885Q16.117540625,11.615605,15.522540625,12.692825Z'
          fillRule='evenodd'
          fill='#26244C'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const CameraSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='21.33333396911621'
    height='21.33333396911621'
    viewBox='0 0 21.33333396911621 21.33333396911621'
  >
    <g>
      <g>
        <path
          d='M3.55512375,4.4453125L13.33294375,4.4453125C14.31474375,4.4453125,15.11064375,5.2412505,15.11064375,6.2230925L15.11064375,8.0692225L18.20034375,6.1678725C18.79264375,5.8034225,19.55514375,6.2295125,19.55514375,6.9249025L19.55514375,14.4101125C19.55514375,15.1055125,18.79264375,15.5316125,18.20034375,15.1672125L15.11064375,13.2658025L15.11064375,15.1120125C15.11064375,16.0938125,14.31474375,16.8897125,13.33294375,16.8897125L3.55512375,16.8897125C2.5732827499999997,16.8897125,1.777344597711,16.0938125,1.777344597711,15.1120125L1.777344597711,6.2230925C1.777344597711,5.2412505,2.5732827499999997,4.4453125,3.55512375,4.4453125ZM15.11064375,9.6347925L15.11064375,11.7002325L18.22174375,13.6147325L18.22174375,7.7202625000000005L15.11064375,9.6347925ZM13.77734375,8.8897425L13.77734375,6.2230925C13.77734375,6.1003625,13.73394375,5.9956025,13.64724375,5.9088225C13.56034375,5.8220425,13.45564375,5.7786425,13.33294375,5.7786425L3.55512375,5.7786425C3.43239375,5.7786425,3.32763375,5.8220425,3.2408537500000003,5.9088225C3.1540737500000002,5.9956025,3.11067375,6.1003625,3.11067375,6.2230925L3.11067375,15.1120125C3.11067375,15.2347125,3.1540737500000002,15.3395125,3.2408537500000003,15.4263125C3.32763375,15.5130125,3.43239375,15.5564125,3.55512375,15.5564125L13.33294375,15.5564125C13.45564375,15.5564125,13.56034375,15.5130125,13.64724375,15.4263125C13.73394375,15.3395125,13.77734375,15.2347125,13.77734375,15.1120125L13.77734375,8.8897425Z'
          fill='#26244C'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const CameraClosedSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='21.33333396911621'
    height='21.33333396911621'
    viewBox='0 0 21.33333396911621 21.33333396911621'
  >
    <g>
      <g>
        <path
          d='M3.1098637499999997,3.1406755C2.84951375,2.8803259,2.42739975,2.8803259,2.16704975,3.1406755C1.90670075,3.4010255,1.90670075,3.8231355000000002,2.16704975,4.0834825L16.30914375,18.2256125C16.56954375,18.4860125,16.99164375,18.4860125,17.25204375,18.2256125C17.51234375,17.9653125,17.51234375,17.5432125,17.25204375,17.2828125L3.1098637499999997,3.1406755ZM13.77734375,6.2213525L13.77734375,11.9227425L15.13144375,13.2768125L18.20034375,15.1654125C18.79264375,15.5299125,19.55514375,15.1037125,19.55514375,14.4084125L19.55514375,6.9231725C19.55514375,6.2277725,18.79264375,5.8016825,18.20034375,6.1661325L15.11064375,8.0674925L15.11064375,6.2213525C15.11064375,5.2395125,14.31474375,4.4435725,13.33294375,4.4435725L6.29817375,4.4435725L7.63151375,5.7769125L13.33294375,5.7769125C13.45564375,5.7769125,13.56034375,5.8203025,13.64724375,5.9070925C13.73394375,5.9938725,13.77734375,6.098622499999999,13.77734375,6.2213525ZM1.77734375,6.2213525C1.77734375,6.0279925,1.80821465,5.841832500000001,1.86529685,5.6675525L3.11067375,6.9129325L3.11067375,15.1102125C3.11067375,15.2330125,3.1540737500000002,15.3377125,3.2408537500000003,15.4245125Q3.37102375,15.5547125,3.55512375,15.5547125L11.75243375,15.5547125L13.08574375,16.888012500000002L3.55512375,16.888012500000002C2.5732827499999997,16.888012500000002,1.77734375,16.0921125,1.77734375,15.1102125L1.77734375,6.2213525ZM15.11064375,9.633062500000001L15.11064375,11.6985025L18.22174375,13.6130125L18.22174375,7.7185225L15.11064375,9.633062500000001Z'
          fillRule='evenodd'
          fill='#26244C'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const CameraSwitchSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='22'
    height='22'
    viewBox='0 0 22 22'
  >
    <g>
      <g></g>
      <g>
        <path
          d='M2.0625,19.25L19.9375,19.25C20.3156,19.25,20.625,18.9406,20.6112,18.5625L20.6113,5.5C20.6113,5.12188,20.3019,4.8125,19.9238,4.8125L15.4825,4.8125L14.3138,3.059375C14.19,2.866875,13.97,2.75,13.7431,2.75L8.24313,2.75C8.01625,2.75,7.80313,2.866875,7.6725,3.059375L6.50375,4.8125L2.0625,4.8125C1.684375,4.8125,1.375,5.12188,1.375,5.5L1.375,18.5625C1.375,18.9406,1.684375,19.25,2.0625,19.25ZM19.25,17.875L2.75687,17.875L2.7568799999999998,6.1875L6.88188,6.1875C7.10875,6.1875,7.32188,6.07062,7.4525,5.87812L8.62125,4.125L13.3856,4.125L14.5544,5.87812C14.6781,6.07062,14.8981,6.1875,15.125,6.1875L19.25,6.1875L19.25,17.875Z'
          fillRule='evenodd'
          fill='#D8D9E6'
          fillOpacity='1'
        />
      </g>
      <g>
        <path
          d='M10.43036,7.5625L7.680359,7.5625L8.59237,8.474508C8.44178,8.59047,8.29857,8.71625,8.16373,8.85109C7.344358,9.67046,6.875,10.780149999999999,6.875,11.96236C6.875,14.39241,8.84495,16.362360000000002,11.275,16.362360000000002L11.275,14.93533L11.22584,14.934940000000001C9.606580000000001,14.90868,8.30203,13.587869999999999,8.30203,11.96236C8.30203,11.162510000000001,8.61839,10.41456,9.172789999999999,9.86015C9.30823,9.72471,9.45605,9.60285,9.61404,9.49618L10.43036,10.3125L10.43036,7.5625ZM11.27502,7.5625C13.705079999999999,7.5625,15.67502,9.53245,15.67502,11.9625C15.67502,13.14471,15.20567,14.2544,14.386289999999999,15.07377C14.27461,15.185459999999999,14.15718,15.29092,14.03458,15.38974L15.00714,16.362299999999998L12.11964,16.362299999999998L12.11964,13.4748L13.01691,14.37207C13.14466,14.27952,13.26518,14.17676,13.37723,14.06471C13.93164,13.5103,14.248000000000001,12.76235,14.248000000000001,11.9625C14.248000000000001,10.33699,12.943439999999999,9.01618,11.32419,8.98993L11.27502,8.98953L11.27502,7.5625Z'
          fillRule='evenodd'
          fill='#D8D9E6'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const FeedbackSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='17.4208984375'
    viewBox='0 0 20 17.4208984375'
  >
    <g>
      <g>
        <path
          d='M10.89285340209961,10.892858Q10.89285340209961,10.980796,10.87570340209961,11.06705Q10.85854340209961,11.15329,10.82489340209961,11.234539999999999Q10.79124340209961,11.31578,10.742383402099609,11.3889Q10.69352340209961,11.46202,10.63134340209961,11.5242Q10.56916340209961,11.58639,10.496043402099609,11.63524Q10.42292340209961,11.6841,10.341683402099608,11.71775Q10.26043340209961,11.7514,10.17418340209961,11.76856Q10.08793940209961,11.78572,10.000000402099609,11.78572Q9.91206140209961,11.78572,9.82581240209961,11.76856Q9.73956340209961,11.7514,9.65831840209961,11.71775Q9.57707440209961,11.6841,9.50395540209961,11.63524Q9.430837402099609,11.58639,9.36865540209961,11.5242Q9.30647340209961,11.46202,9.257617402099608,11.3889Q9.208760402099609,11.31578,9.175108102099609,11.234539999999999Q9.14145540209961,11.15329,9.12429940209961,11.06705Q9.10714340209961,10.980796,9.10714340209961,10.892858Q9.10714340209961,10.804919,9.12429940209961,10.71867Q9.14145540209961,10.632421,9.175108102099609,10.551176Q9.208760402099609,10.469931,9.257617402099608,10.396812Q9.30647340209961,10.323694,9.36865540209961,10.261512Q9.430837402099609,10.19933,9.50395540209961,10.150473999999999Q9.57707440209961,10.101617,9.65831840209961,10.0679647Q9.73956340209961,10.034312,9.82581240209961,10.017156Q9.91206140209961,10,10.000000402099609,10Q10.08793940209961,10,10.17418340209961,10.017156Q10.26043340209961,10.034312,10.341683402099608,10.0679647Q10.42292340209961,10.101617,10.496043402099609,10.150473999999999Q10.56916340209961,10.19933,10.63134340209961,10.261512Q10.69352340209961,10.323694,10.742383402099609,10.396812Q10.79124340209961,10.469931,10.82489340209961,10.551176Q10.85854340209961,10.632421,10.87570340209961,10.71867Q10.89285340209961,10.804919,10.89285340209961,10.892858Z'
          fill='#3A3D48'
          fillOpacity='1'
        />
      </g>
      <g>
        <path
          d='M18.4375,0L15.1786,0C14.6855,2.17983e-7,14.2857,0.399746,14.2857,0.892857C14.2857,1.38597,14.6855,1.78571,15.1786,1.78571L17.3214,1.78571C17.8145,1.78571,18.2143,2.18546,18.2143,2.67857L18.2143,11.9643C18.2143,12.4574,17.8145,12.8571,17.3214,12.8571L13.0554,12.8571C12.6409,12.8571,12.2435,13.0217,11.9504,13.3147L10.6223,14.6429L10,15.2652L9.37768,14.6429L8.48482,13.75L8.04955,13.3147C7.75658,13.0217,7.35913,12.8571,6.94464,12.8571L2.67857,12.8571C2.18546,12.8571,1.78571,12.4574,1.78571,11.9643L1.78571,2.67857C1.78571,2.18546,2.18546,1.78571,2.67857,1.78571L4.82143,1.78571C5.31454,1.78571,5.71429,1.38597,5.71429,0.892857C5.71429,0.399746,5.31454,2.17983e-7,4.82143,0L1.5625,0C0.699555,-2.17983e-7,-1.08991e-7,0.699555,0,1.5625L0,13.0804C-1.08991e-7,13.9433,0.699555,14.6429,1.5625,14.6429L6.85268,14.6429L9.36875,17.1594C9.71741,17.5079,10.2826,17.5079,10.6313,17.1594L13.1473,14.6429L18.4375,14.6429C19.3004,14.6429,20,13.9433,20,13.0804L20,1.5625C20,0.699555,19.3004,-2.17983e-7,18.4375,0Z'
          fill='#3A3D48'
          fillOpacity='1'
        />
      </g>
      <g>
        <path
          d='M10.000000402099609,8.57143C10.49311340209961,8.57143,10.89285340209961,8.17168,10.89285340209961,7.67857L10.89285340209961,0.892857C10.89285340209961,0.399746,10.49311340209961,0,10.000000402099609,0C9.506888402099609,0,9.10714340209961,0.399746,9.10714340209961,0.892857L9.10714340209961,7.67857C9.10714340209961,8.17168,9.506888402099609,8.57143,10.000000402099609,8.57143Z'
          fill='#3A3D48'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const SettingSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='22'
    height='20'
    viewBox='0 0 22 20'
  >
    <g>
      <path
        d='M15.3833,20L6.61698,20C5.82096,20,5.08573,19.569,4.68948,18.8702L0.294922,11.1199C-0.0983062,10.4263,-0.0983062,9.57367,0.294922,8.88016L4.68948,1.12979C5.08573,0.430956,5.82096,3.97364e-7,6.61698,3.97364e-7L15.3833,3.97364e-7C16.1792,3.97364e-7,16.9145,0.430967,17.3108,1.12983L21.7051,8.8802C22.0983,9.57369,22.0983,10.4263,21.7051,11.1198L17.3108,18.8702C16.9145,19.569,16.1792,20,15.3833,20ZM15.8789,18.0419L20.2733,10.2915C20.3835,10.0972,20.3835,9.90283,20.2733,9.7085L15.8789,1.95813C15.7688,1.76382,15.6036,1.66667,15.3833,1.66667L6.61698,1.66667C6.39668,1.66667,6.23143,1.76382,6.12127,1.95812L1.72671,9.70849C1.61652,9.90283,1.61652,10.0972,1.72671,10.2915L6.12127,18.0419C6.23143,18.2362,6.39668,18.3333,6.61698,18.3333L15.3833,18.3333C15.6036,18.3333,15.7688,18.2362,15.8789,18.0419ZM15.4,10C15.4,12.4546,13.43,14.4444,11,14.4444C8.56995,14.4444,6.6,12.4546,6.6,10C6.6,7.5454,8.56995,5.55556,11,5.55556C13.43,5.55556,15.4,7.5454,15.4,10ZM13.75,10C13.75,9.23294,13.4815,8.57821,12.9446,8.03581C12.4076,7.49342,11.7594,7.22222,11,7.22222C10.2406,7.22222,9.59243,7.49342,9.05545,8.03581C8.51849,8.57821,8.25,9.23294,8.25,10C8.25,10.767,8.51849,11.4218,9.05545,11.9642C9.59243,12.5066,10.2406,12.7778,11,12.7778C11.7594,12.7778,12.4076,12.5066,12.9446,11.9642C13.4815,11.4218,13.75,10.767,13.75,10Z'
        fill='#3A3D48'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const UserSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='19.857666015625'
    height='20'
    viewBox='0 0 19.857666015625 20'
  >
    <g>
      <path
        d='M9.929044140624999,11.69275625C8.768784140625,11.69275625,7.778444140625,12.10589625,6.958024140625,12.93215625C6.137604140625,13.75850625,5.727394140625,14.75590625,5.727394140625,15.92450625C5.727394140625,16.20500625,5.501651140625,16.43230625,5.223192140625,16.43230625C4.944734140625,16.43230625,4.718994300905,16.20500625,4.718994300905,15.92450625C4.718994300905,14.47550625,5.227656140625,13.23865625,6.244984140625,12.21404625C6.727574140625,11.72798625,7.257574140625,11.35721625,7.834984140625,11.10173625C6.6720841406249995,10.38889625,5.8954541406250005,9.100256250000001,5.8954541406250005,7.62890625C5.8954541406250005,5.38524625,7.701354140625,3.56640495857,9.929044140624999,3.56640495857C12.156724140625,3.56640495857,13.962624140625,5.38524625,13.962624140625,7.62890625C13.962624140625,9.85939625,12.177834140625,11.67007625,9.968214140625001,11.69120625C9.955284140625,11.69221625,9.942224140625001,11.69275625,9.929044140624999,11.69275625ZM12.954234140625,7.62890625C12.954234140625,6.7875362500000005,12.658884140625,6.0693762499999995,12.068184140625,5.47443625C11.477474140624999,4.87949625,10.764424140625,4.58202625,9.929044140624999,4.58202625C9.093664140625,4.58202625,8.380614140625,4.87949625,7.789914140625,5.47443625C7.199204140625,6.0693762499999995,6.9038541406250005,6.7875362500000005,6.9038541406250005,7.62890625C6.9038541406250005,8.470276250000001,7.199204140625,9.188436249999999,7.789914140625,9.78337625C8.380614140625,10.378316250000001,9.093664140625,10.67578625,9.929044140624999,10.67578625C10.764424140625,10.67578625,11.477474140624999,10.378316250000001,12.068184140625,9.78337625C12.658884140625,9.188436249999999,12.954234140625,8.470276250000001,12.954234140625,7.62890625ZM13.310464140625,11.93203625C13.880944140625,12.42256625,14.325564140625,13.00833625,14.644304140625,13.68930625C14.974294140625,14.39430625,15.139294140625,15.13930625,15.139294140625,15.92440625C15.139294140625,16.05910625,15.086194140625,16.18820625,14.991594140625,16.28340625C14.897094140625,16.37860625,14.768794140625,16.43220625,14.635094140625,16.43220625C14.356644140625,16.43220625,14.130894140625,16.20480625,14.130894140625,15.92440625C14.130894140625,15.29100625,13.997994140625,14.69030625,13.732174140625,14.12240625C13.475054140625,13.57310625,13.116244140625,13.10041625,12.655744140625,12.70445625C12.543544140625,12.60797625,12.478944140625,12.46680625,12.478944140625,12.31824625C12.478944140625,12.03780625,12.704684140625,11.81043625,12.983134140625,11.81043625C13.103134140625,11.81043625,13.219234140625,11.85356625,13.310464140625,11.93203625Z'
        fill='#8D70FF'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const VoiceOneSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <path
        d='M3,15L3,14.2539C3,12.7849,4.1927900000000005,11.5897,5.65893,11.5897L6.19301,11.5897C5.74657,11.2575,5.36838,10.83818,5.0837900000000005,10.35699C4.6954899999999995,9.39519,4.53732,8.256910000000001,4.50393,7.02302L4.50393,5.15125C4.7122399999999995,-0.38164,12.63402,-0.3858600000000001,12.84435,5.15125L12.84435,7.02302C12.82928,8.85515,12.56608,9.68383,12.26445,10.35699C11.97985,10.83818,11.60166,11.2575,11.15522,11.5897L11.6893,11.5897C13.1554,11.5897,14.3482,12.7849,14.3482,14.2539L14.3482,15L3,15ZM12.02363,5.09649C11.99431,3.27085,10.503070000000001,1.7947389999999999,8.67418,1.7947389999999999C6.84529,1.7947389999999999,5.35405,3.27085,5.32471,5.09649L6.53956,5.09649Q7.82432,4.8578600000000005,8.17429,3.7574C8.245809999999999,3.5325,8.43874,3.36784,8.67473,3.36784C8.910910000000001,3.36784,9.10591,3.54219,9.1783,3.76701Q9.52989,4.85891,10.81033,5.09649L12.02363,5.09649ZM5.32422,6.95175L5.32422,5.91838L6.53951,5.91838C7.43305,5.91838,8.22055,5.45467,8.674890000000001,4.7548200000000005C9.12924,5.45467,9.91674,5.91838,10.810279999999999,5.91838L12.02404,5.91838L12.02404,6.95175Q11.85595,9.90908,11.57112,9.91722C10.31503,12.1331,7.03238,12.132,5.77714,9.91722Q5.49231,9.90908,5.32422,6.95175ZM6.6232500000000005,12.4117L6.6232500000000005,12.9452C6.6232500000000005,13.4074,6.77635,13.8345,7.03439,14.1781L3.821777,14.1781C3.861617,13.1973,4.67039,12.4117,5.65884,12.4117L6.6232500000000005,12.4117ZM9.90454,12.9452L9.90454,12.4117L7.4436,12.4117L7.4436,12.9452C7.50507,14.5778,9.842500000000001,14.579,9.90454,12.9452ZM10.31372,14.1781L13.5263,14.1781C13.4865,13.1973,12.67772,12.4117,11.68928,12.4117L10.72486,12.4117L10.72486,12.9452C10.72486,13.4074,10.571760000000001,13.8345,10.31372,14.1781Z'
        fillRule='evenodd'
        fill='#FCFCFD'
        fillOpacity='1'
      />
      <path
        d='M2.9,15L2.9,14.2539Q2.9,13.1108,3.708882,12.3003Q4.51785,11.4897,5.65893,11.4897L6.19301,11.4897L6.19301,11.5897L6.13331,11.67Q5.43826,11.1528,4.99771,10.4079L4.99388,10.40141L4.99106,10.39443Q4.45949,9.07776,4.40397,7.02573L4.40393,7.02438L4.40393,5.14937L4.404,5.14749Q4.4795300000000005,3.14134,5.79884,1.963802Q6.99022,0.9004384,8.673359999999999,0.9000015Q10.35666,0.899565,11.54829,1.962599Q12.86803,3.13991,12.94428,5.14746L12.94435,5.14936L12.94435,7.02302Q12.92708,9.12275,12.3557,10.39788L12.35339,10.40304L12.35052,10.4079Q11.90998,11.1528,11.21492,11.67L11.15522,11.5897L11.15522,11.4897L11.6893,11.4897Q12.83038,11.4897,13.6393,12.3003Q14.4482,13.1108,14.4482,14.2539L14.4482,15.1L2.9,15.1L2.9,15ZM3.1,15L3,15L3,14.9L14.3482,14.9L14.3482,15L14.2482,15L14.2482,14.2539Q14.2482,13.1935,13.4978,12.4416Q12.74742,11.6897,11.6893,11.6897L10.85332,11.6897L11.09552,11.5095Q11.75831,11.0163,12.17837,10.30608L12.26445,10.35699L12.17319,10.3161Q12.72743,9.0792,12.74435,7.02302L12.74435,5.15125L12.84435,5.15125L12.74442,5.15505Q12.67141,3.23252,11.41515,2.11184Q10.280439999999999,1.0995848,8.67341,1.100002Q7.06652,1.100418,5.93201,2.11301Q4.67619,3.2339,4.60386,5.15502L4.50393,5.15125L4.60393,5.15125L4.60393,7.02302L4.50393,7.02302L4.6039,7.02032Q4.65844,9.0363,5.17651,10.31955L5.0837900000000005,10.35699L5.16986,10.30608Q5.58992,11.0163,6.25271,11.5095L6.49491,11.6897L5.65893,11.6897Q4.60081,11.6897,3.850443,12.4416Q3.1,13.1935,3.1,14.2539L3.1,15ZM11.92364,5.09809Q11.9023,3.76866,10.9535,2.83217Q10.00374,1.894739,8.67418,1.894739Q7.34463,1.894739,6.3948599999999995,2.83217Q5.44606,3.76865,5.42469,5.09809L5.32471,5.09649L5.32471,4.99649L6.53956,4.99649L6.53956,5.09649L6.5213,4.99817Q7.74717,4.77048,8.078990000000001,3.72709Q8.22504,3.26784,8.67473,3.26784Q9.122630000000001,3.26784,9.273489999999999,3.73636Q9.60679,4.77147,10.82857,4.99816L10.81033,5.09649L10.81033,4.99649L12.02363,4.99649L12.02363,5.09649L11.92364,5.09809ZM12.12524,5.19649L10.80113,5.19649L10.79209,5.19481Q9.45299,4.94635,9.08311,3.79766Q8.97692,3.46784,8.67473,3.46784Q8.371310000000001,3.46784,8.269580000000001,3.7877Q7.90146,4.94525,6.5578199999999995,5.1948L6.54877,5.19649L5.22309,5.19649L5.22472,5.09488Q5.2474,3.68372,6.25437,2.6898299999999997Q7.26255,1.694739,8.67418,1.694739Q10.08582,1.694739,11.09399,2.6898299999999997Q12.10095,3.68373,12.12361,5.09488L12.12524,5.19649ZM5.22422,6.95175L5.22422,5.81838L6.53951,5.81838Q7.17235,5.81838,7.72326,5.51168Q8.25737,5.21432,8.59102,4.7003699999999995L8.674890000000001,4.57117L8.75877,4.7003699999999995Q9.09243,5.21432,9.626529999999999,5.51168Q10.177430000000001,5.81838,10.810279999999999,5.81838L12.12404,5.81838L12.12404,6.95459L12.12388,6.95742Q11.9506,10.00643,11.57397,10.01717L11.57112,9.91722L11.65811,9.96653Q11.18744,10.79685,10.36057,11.2511Q9.5821,11.6788,8.6738,11.6787Q7.76558,11.6786,6.9872,11.2508Q6.16054,10.7965,5.6901399999999995,9.96652L5.77714,9.91722L5.774290000000001,10.01717Q5.397679999999999,10.00642,5.22438,6.95742L5.22422,6.95459L5.22422,6.95175ZM5.42422,6.95175L5.32422,6.95175L5.42406,6.94607Q5.50783,8.42,5.62035,9.15668Q5.67561,9.5185,5.736610000000001,9.69638Q5.7638,9.77566,5.78902,9.81082L5.779999999999999,9.81726L5.83634,9.81887L5.86414,9.86791Q6.30657,10.64854,7.08353,11.0756Q7.81693,11.4786,8.67382,11.4787Q9.53079,11.4788,10.26426,11.0759Q11.04142,10.64888,11.48412,9.8679L11.51192,9.81887L11.56826,9.81726L11.55924,9.81082Q11.58446,9.77566,11.61164,9.69638Q11.67265,9.5185,11.72791,9.15668Q11.84044,8.419920000000001,11.9242,6.94607L12.02404,6.95175L11.92404,6.95175L11.92404,5.91838L12.02404,5.91838L12.02404,6.01838L10.810279999999999,6.01838Q10.12551,6.01838,9.529250000000001,5.68642Q8.951730000000001,5.36489,8.59102,4.80927L8.674890000000001,4.7548200000000005L8.75877,4.80927Q8.398060000000001,5.36489,7.82054,5.68642Q7.22427,6.01838,6.53951,6.01838L5.32422,6.01838L5.32422,5.91838L5.42422,5.91838L5.42422,6.95175ZM6.72325,12.4117L6.72325,12.9452Q6.72325,13.5972,7.11436,14.1181L7.23454,14.2781L3.717633,14.2781L3.72186,14.174Q3.753426,13.3969,4.3151600000000006,12.8551Q4.87857,12.3117,5.65884,12.3117L6.72325,12.3117L6.72325,12.4117ZM6.52325,12.4117L6.6232500000000005,12.4117L6.6232500000000005,12.5117L5.65884,12.5117Q4.95931,12.5117,4.454,12.9991Q3.950006,13.4852,3.921695,14.1822L3.821777,14.1781L3.821777,14.0781L7.03439,14.0781L7.03439,14.1781L6.95443,14.2382Q6.52325,13.6639,6.52325,12.9452L6.52325,12.4117ZM9.80454,12.9452L9.80454,12.4117L9.90454,12.4117L9.90454,12.5117L7.4436,12.5117L7.4436,12.4117L7.5436,12.4117L7.5436,12.9452L7.4436,12.9452L7.54353,12.9414Q7.56375,13.4783,7.91192,13.7891Q8.22662,14.07,8.67388,14.0701Q9.12117,14.0702,9.43592,13.7894Q9.78421,13.4787,9.80461,12.9414L9.90454,12.9452L9.80454,12.9452ZM10.00454,12.9452L10.00454,12.9471L10.004470000000001,12.949Q9.98084,13.5713,9.56906,13.9387Q9.1974,14.2702,8.673829999999999,14.2701Q8.15033,14.27,7.77874,13.9383Q7.36709,13.5709,7.34367,12.949L7.3436,12.9471L7.3436,12.3117L10.00454,12.3117L10.00454,12.9452ZM10.31372,14.0781L13.5263,14.0781L13.5263,14.1781L13.4264,14.1822Q13.3981,13.4852,12.89411,12.9991Q12.38881,12.5117,11.68927,12.5117L10.72486,12.5117L10.72486,12.4117L10.824860000000001,12.4117L10.824860000000001,12.9452Q10.824860000000001,13.6639,10.39369,14.2382L10.31372,14.1781L10.31372,14.0781ZM10.31372,14.2781L10.113579999999999,14.2781L10.23375,14.1181Q10.62486,13.5972,10.62486,12.9452L10.62486,12.3117L11.68928,12.3117Q12.46955,12.3117,13.033,12.8551Q13.5947,13.397,13.6263,14.174L13.6305,14.2781L10.31372,14.2781Z'
        fill='#23262F'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const VoiceTwoSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <path
        d='M2.6666667461395264,15.111087763023377L14.222266746139526,15.111087763023377L14.222266746139526,14.353887763023376C14.222266746139526,12.862787763023377,13.007666746139526,11.649787763023376,11.514706746139526,11.649787763023376L10.970876746139526,11.649787763023376C11.869156746139526,10.983587763023376,12.495996746139527,9.971977763023377,12.652586746139526,8.813427763023377L12.690866746139527,6.449787763023377L12.690866746139527,5.671197763023376C12.731766746139526,4.6903377630233765,12.589656746139527,3.8443077630233766,12.216556746139526,3.1685077630233764C10.667946746139526,0.12819276302337645,6.219776746139527,0.12983276302337643,4.672356746139526,3.1685077630233764C4.3026667461395265,4.165987763023376,4.224786746139526,4.938337763023377,4.198046746139527,5.671197763023376L4.198046746139527,6.449787763023377L4.236306746139526,8.813427763023377C4.392926746139526,9.972007763023376,5.019756746139526,10.983587763023376,5.918016746139527,11.649787763023376L5.3741767461395265,11.649787763023376C3.881246746139526,11.649787763023376,2.6666667461395264,12.862787763023377,2.6666667461395264,14.353887763023376L2.6666667461395264,15.111087763023377ZM11.855586746139526,5.115057763023376C11.855586746139526,3.2365977630233767,10.325376746139526,1.7083457630233765,8.444476746139525,1.7083457630233765C6.5635767461395265,1.7083457630233765,5.033366746139526,3.2365977630233767,5.033366746139526,5.115057763023376L5.033366746139526,5.510067763023376L7.960506746139527,5.510067763023376C8.919206746139526,5.510067763023376,9.699206746139527,4.731067763023376,9.699206746139527,3.7735677630233764L9.699206746139527,3.6475977630233767L10.534506746139527,3.6475977630233767L10.534506746139527,3.7735677630233764C10.534506746139527,4.587327763023376,11.097886746139526,5.272147763023376,11.855586746139526,5.459427763023377L11.855586746139526,5.115057763023376ZM5.033366746139526,8.243137763023377C5.033366746139526,10.121597763023377,6.5635767461395265,11.649887763023376,8.444476746139525,11.649887763023376C10.325376746139526,11.649887763023376,11.855586746139526,10.121597763023377,11.855556746139527,8.243137763023377L11.855556746139527,6.310477763023377C11.129236746139526,6.191597763023377,10.504186746139526,5.768067763023376,10.116826746139527,5.176007763023376C9.656966746139528,5.878877763023376,8.862096746139526,6.344297763023376,7.960476746139526,6.344297763023376L5.033366746139526,6.344297763023376L5.033366746139526,8.243137763023377ZM3.5035807461395265,14.276887763023376L5.885956746139526,14.276887763023376L11.002926746139526,14.276887763023376L13.385366746139526,14.276887763023376C13.344866746139527,13.281487763023376,12.521276746139526,12.484087763023377,11.514766746139527,12.484087763023377L5.374206746139526,12.484087763023377C4.367696746139527,12.484087763023377,3.5441487461395265,13.281487763023376,3.5035807461395265,14.276887763023376Z'
        fillRule='evenodd'
        fill='#FFFFFF'
        fillOpacity='1'
      />
      <path
        d='M2.6666667461395264,15.011087763023376L14.222266746139526,15.011087763023376L14.222266746139526,15.111087763023377L14.122266746139527,15.111087763023377L14.122266746139527,14.353887763023376Q14.122266746139527,13.277087763023376,13.357666746139527,12.513487763023376Q12.593026746139527,11.749787763023377,11.514716746139527,11.749787763023377L10.668166746139526,11.749787763023377L10.911306746139527,11.569487763023377Q12.320406746139527,10.524467763023376,12.553486746139527,8.800037763023376L12.652586746139526,8.813427763023377L12.552596746139526,8.811817763023377L12.590856746139526,6.448167763023377L12.590836746139527,5.669107763023376L12.590926746139527,5.667027763023376Q12.653536746139526,4.1668977630233766,12.129016746139527,3.2168477630233765L12.128206746139526,3.2153877630233763L12.127456746139526,3.2138977630233763Q11.579046746139527,2.1372477630233764,10.552116746139525,1.5451967630233765Q9.586916746139526,0.9887316630233765,8.444026746139526,0.9888877630233764Q7.301246746139526,0.9890437630233765,6.336146746139526,1.5456507630233765Q5.309476746139526,2.1377577630233766,4.761466746139527,3.2138877630233766L4.672356746139526,3.1685077630233764L4.766126746139527,3.2032677630233763Q4.346896746139526,4.334427763023376,4.297986746139527,5.674847763023377L4.198046746139527,5.671197763023376L4.298046746139526,5.671197763023376L4.298046746139526,6.449787763023377L4.336296746139526,8.811817763023377L4.236306746139526,8.813427763023377L4.335406746139526,8.800037763023376Q4.568516746139526,10.524447763023376,5.977586746139526,11.569487763023377L6.2207167461395265,11.749787763023377L5.3741767461395265,11.749787763023377Q4.295866746139526,11.749787763023377,3.5312387461395263,12.513487763023376Q2.7666667461395265,13.277087763023376,2.7666667461395265,14.353887763023376L2.7666667461395265,15.111087763023377L2.6666667461395264,15.111087763023377L2.6666667461395264,15.011087763023376ZM2.5666667461395263,15.211087763023377L2.5666667461395263,14.353887763023376Q2.5666667461395263,13.194087763023376,3.389907746139526,12.371987763023377Q4.213096746139526,11.549787763023376,5.3741767461395265,11.549787763023376L5.918016746139527,11.549787763023376L5.918016746139527,11.649787763023376L5.858446746139526,11.730087763023377Q4.381616746139526,10.634837763023377,4.137206746139526,8.826827763023378L4.136416746139526,8.820967763023376L4.098046746139526,6.449787763023377L4.098046746139526,5.669377763023377L4.098116746139526,5.667547763023377Q4.148196746139527,4.295037763023377,4.578596746139526,3.1337577630233766L4.580606746139527,3.1283077630233764L4.583246746139526,3.1231377630233763Q5.158176746139526,1.9941477630233764,6.236226746139526,1.3723987630233765Q7.247686746139526,0.7890515630233764,8.443996746139526,0.7888877630233765Q9.640426746139527,0.7887237630233764,10.652006746139527,1.3719297630233764Q11.730326746139527,1.9936077630233764,12.305666746139526,3.1231277630233762L12.216556746139526,3.1685077630233764L12.304096746139527,3.1201777630233765Q12.855666746139526,4.119267763023377,12.790766746139527,5.6753677630233765L12.690866746139527,5.671197763023376L12.790866746139526,5.671197763023376L12.790866746139526,6.451407763023377L12.752466746139527,8.820967763023376L12.751666746139527,8.826827763023378Q12.507326746139526,10.634867763023376,11.030446746139527,11.730087763023377L10.970876746139526,11.649787763023376L10.970876746139526,11.549787763023376L11.514716746139527,11.549787763023376Q12.675766746139526,11.549787763023376,13.498966746139526,12.371987763023377Q14.322266746139526,13.194087763023376,14.322266746139526,14.353887763023376L14.322266746139526,15.211087763023377L2.6666667461395264,15.211087763023377L2.5666667461395263,15.211087763023377ZM11.755586746139526,5.115057763023376Q11.755586746139526,3.7476777630233764,10.784706746139527,2.7780377630233763Q9.813766746139526,1.8083457630233766,8.444476746139525,1.8083457630233766Q7.075196746139526,1.8083457630233766,6.104246746139527,2.7780377630233763Q5.133366746139526,3.7476677630233763,5.133366746139526,5.115057763023376L5.133366746139526,5.510067763023376L5.033366746139526,5.510067763023376L5.033366746139526,5.410067763023377L7.960506746139527,5.410067763023377Q8.638146746139526,5.410067763023377,9.118706746139527,4.930117763023376Q9.599206746139526,4.450227763023376,9.599206746139526,3.7735677630233764L9.599206746139526,3.5475977630233766L10.634506746139525,3.5475977630233766L10.634506746139525,3.7735677630233764Q10.634506746139525,4.3429977630233765,10.989186746139527,4.789197763023377Q11.338466746139526,5.228597763023377,11.879586746139527,5.362347763023377L11.855586746139526,5.459427763023377L11.755586746139526,5.459427763023377L11.755586746139526,5.115057763023376ZM11.955586746139526,5.115057763023376L11.955586746139526,5.587157763023376L11.831596746139526,5.556507763023377Q11.224316746139527,5.406407763023377,10.832626746139526,4.913647763023376Q10.434506746139526,4.412807763023377,10.434506746139526,3.7735677630233764L10.434506746139526,3.6475977630233767L10.534506746139527,3.6475977630233767L10.534506746139527,3.7475977630233763L9.699206746139527,3.7475977630233763L9.699206746139527,3.6475977630233767L9.799206746139525,3.6475977630233767L9.799206746139525,3.7735677630233764Q9.799206746139525,4.533137763023376,9.260036746139527,5.071627763023376Q8.720916746139526,5.610067763023377,7.960506746139527,5.610067763023377L4.9333667461395265,5.610067763023377L4.9333667461395265,5.115057763023376Q4.9333667461395265,3.6647477630233767,5.962916746139527,2.6365277630233765Q6.992426746139526,1.6083457630233764,8.444476746139525,1.6083457630233764Q9.896536746139526,1.6083457630233764,10.926036746139527,2.6365277630233765Q11.955586746139526,3.6647577630233763,11.955586746139526,5.115057763023376ZM5.133366746139526,8.243137763023377Q5.133366746139526,9.610527763023377,6.104246746139527,10.580157763023376Q7.075186746139527,11.549887763023376,8.444476746139525,11.549887763023376Q9.813766746139526,11.549887763023376,10.784696746139526,10.580157763023376Q11.755576746139527,9.610537763023377,11.755556746139526,8.243137763023377L11.755556746139526,6.310477763023377L11.855556746139527,6.310477763023377L11.839406746139526,6.409167763023376Q10.679976746139527,6.219397763023377,10.033146746139526,5.230757763023377L10.116826746139527,5.176007763023376L10.200506746139526,5.230757763023377Q9.835526746139525,5.788607763023377,9.252746746139525,6.111217763023377Q8.651056746139528,6.444297763023377,7.960476746139526,6.444297763023377L5.033366746139526,6.444297763023377L5.033366746139526,6.344297763023376L5.133366746139526,6.344297763023376L5.133366746139526,8.243137763023377ZM4.9333667461395265,8.243137763023377L4.9333667461395265,6.2442977630233765L7.960476746139526,6.2442977630233765Q8.599386746139526,6.2442977630233765,9.155876746139526,5.936237763023376Q9.695306746139526,5.637627763023376,10.033146746139526,5.121257763023377L10.116826746139527,4.9933577630233765L10.200506746139526,5.121257763023377Q10.799156746139527,6.036247763023376,11.871706746139527,6.211787763023376L11.955556746139527,6.225517763023377L11.955556746139527,8.243137763023377Q11.955576746139526,9.693457763023376,10.926036746139527,10.721677763023376Q9.896546746139526,11.749887763023377,8.444476746139525,11.749887763023377Q6.992416746139527,11.749887763023377,5.962916746139527,10.721677763023376Q4.9333667461395265,9.693447763023377,4.9333667461395265,8.243137763023377ZM3.5035807461395265,14.176887763023377L5.885956746139526,14.176887763023377L11.002926746139526,14.176887763023377L13.385366746139526,14.176887763023377L13.385366746139526,14.276887763023376L13.285466746139527,14.280987763023376Q13.256666746139526,13.573087763023377,12.742966746139526,13.079287763023377Q12.227916746139526,12.584087763023376,11.514766746139527,12.584087763023376L5.374206746139526,12.584087763023376Q4.6610567461395265,12.584087763023376,4.145966746139527,13.079287763023377Q3.6323467461395262,13.573087763023377,3.603497746139526,14.280987763023376L3.5035807461395265,14.276887763023376L3.5035807461395265,14.176887763023377ZM3.5035807461395265,14.376887763023376L3.3994227461395266,14.376887763023376L3.4036637461395265,14.272787763023377Q3.4357857461395263,13.484587763023377,4.007356746139527,12.935087763023377Q4.580506746139527,12.384087763023377,5.374206746139526,12.384087763023377L11.514766746139527,12.384087763023377Q12.308466746139526,12.384087763023377,12.881566746139526,12.935087763023377Q13.453166746139527,13.484587763023377,13.485266746139526,14.272787763023377L13.489566746139527,14.376887763023376L11.002926746139526,14.376887763023376L5.885956746139526,14.376887763023376L3.5035807461395265,14.376887763023376Z'
        fill='#23262F'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const VoiceThreeSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <path
        d='M2.6666667461395264,15.111087763023377L14.222266746139526,15.111087763023377L14.222266746139526,14.353887763023376C14.222266746139526,12.862787763023377,13.007666746139526,11.649787763023376,11.514706746139526,11.649787763023376L10.970876746139526,11.649787763023376C11.869156746139526,10.983587763023376,12.495996746139527,9.971977763023377,12.652586746139526,8.813427763023377L12.690866746139527,6.449787763023377L12.690866746139527,5.671197763023376C12.731766746139526,4.6903377630233765,12.589656746139527,3.8443077630233766,12.216556746139526,3.1685077630233764C10.667946746139526,0.12819276302337645,6.219776746139527,0.12983276302337643,4.672356746139526,3.1685077630233764C4.3026667461395265,4.165987763023376,4.224786746139526,4.938337763023377,4.198046746139527,5.671197763023376L4.198046746139527,6.449787763023377L4.236306746139526,8.813427763023377C4.392926746139526,9.972007763023376,5.019756746139526,10.983587763023376,5.918016746139527,11.649787763023376L5.3741767461395265,11.649787763023376C3.881246746139526,11.649787763023376,2.6666667461395264,12.862787763023377,2.6666667461395264,14.353887763023376L2.6666667461395264,15.111087763023377ZM8.444476746139525,11.649887763023376C6.5635767461395265,11.649887763023376,5.033366746139526,10.121597763023377,5.033366746139526,8.243137763023377L5.033366746139526,5.115057763023376C5.033366746139526,3.2365977630233767,6.5635767461395265,1.7083457630233765,8.444476746139525,1.7083457630233765C10.325376746139526,1.7083457630233765,11.855586746139526,3.2365977630233767,11.855586746139526,5.115057763023376L11.855586746139526,5.459427763023377L11.855556746139527,6.310477763023377L11.855556746139527,8.243137763023377C11.855586746139526,10.121597763023377,10.325376746139526,11.649887763023376,8.444476746139525,11.649887763023376ZM3.5555827461395264,14.237287763023376L5.937956746139527,14.237287763023376L11.054926746139527,14.237287763023376L13.437366746139526,14.237287763023376C13.396866746139526,13.241887763023376,12.573276746139527,12.444487763023377,11.566766746139526,12.444387763023377L5.426206746139526,12.444487763023377C4.419696746139526,12.444487763023377,3.5961507461395263,13.241887763023376,3.5555827461395264,14.237287763023376Z'
        fillRule='evenodd'
        fill='#FFFFFF'
        fillOpacity='1'
      />
      <path
        d='M2.6666667461395264,15.011087763023376L14.222266746139526,15.011087763023376L14.222266746139526,15.111087763023377L14.122266746139527,15.111087763023377L14.122266746139527,14.353887763023376Q14.122266746139527,13.277087763023376,13.357666746139527,12.513487763023376Q12.593026746139527,11.749787763023377,11.514716746139527,11.749787763023377L10.668166746139526,11.749787763023377L10.911306746139527,11.569487763023377Q12.320406746139527,10.524467763023376,12.553486746139527,8.800037763023376L12.652586746139526,8.813427763023377L12.552596746139526,8.811817763023377L12.590856746139526,6.448167763023377L12.590836746139527,5.669107763023376L12.590926746139527,5.667027763023376Q12.653536746139526,4.1668977630233766,12.129016746139527,3.2168477630233765L12.128206746139526,3.2153877630233763L12.127456746139526,3.2138977630233763Q11.579046746139527,2.1372477630233764,10.552116746139525,1.5451967630233765Q9.586916746139526,0.9887316630233765,8.444026746139526,0.9888877630233764Q7.301246746139526,0.9890437630233765,6.336146746139526,1.5456507630233765Q5.309476746139526,2.1377577630233766,4.761466746139527,3.2138877630233766L4.672356746139526,3.1685077630233764L4.766126746139527,3.2032677630233763Q4.346896746139526,4.334427763023376,4.297986746139527,5.674847763023377L4.198046746139527,5.671197763023376L4.298046746139526,5.671197763023376L4.298046746139526,6.449787763023377L4.336296746139526,8.811817763023377L4.236306746139526,8.813427763023377L4.335406746139526,8.800037763023376Q4.568516746139526,10.524447763023376,5.977586746139526,11.569487763023377L6.2207167461395265,11.749787763023377L5.3741767461395265,11.749787763023377Q4.295866746139526,11.749787763023377,3.5312387461395263,12.513487763023376Q2.7666667461395265,13.277087763023376,2.7666667461395265,14.353887763023376L2.7666667461395265,15.111087763023377L2.6666667461395264,15.111087763023377L2.6666667461395264,15.011087763023376ZM2.5666667461395263,15.211087763023377L2.5666667461395263,14.353887763023376Q2.5666667461395263,13.194087763023376,3.389907746139526,12.371987763023377Q4.213096746139526,11.549787763023376,5.3741767461395265,11.549787763023376L5.918016746139527,11.549787763023376L5.918016746139527,11.649787763023376L5.858446746139526,11.730087763023377Q4.381616746139526,10.634837763023377,4.137206746139526,8.826827763023378L4.136416746139526,8.820967763023376L4.098046746139526,6.449787763023377L4.098046746139526,5.669377763023377L4.098116746139526,5.667547763023377Q4.148196746139527,4.295037763023377,4.578596746139526,3.1337577630233766L4.580606746139527,3.1283077630233764L4.583246746139526,3.1231377630233763Q5.158176746139526,1.9941477630233764,6.236226746139526,1.3723987630233765Q7.247686746139526,0.7890515630233764,8.443996746139526,0.7888877630233765Q9.640426746139527,0.7887237630233764,10.652006746139527,1.3719297630233764Q11.730326746139527,1.9936077630233764,12.305666746139526,3.1231277630233762L12.216556746139526,3.1685077630233764L12.304096746139527,3.1201777630233765Q12.855666746139526,4.119267763023377,12.790766746139527,5.6753677630233765L12.690866746139527,5.671197763023376L12.790866746139526,5.671197763023376L12.790866746139526,6.451407763023377L12.752466746139527,8.820967763023376L12.751666746139527,8.826827763023378Q12.507326746139526,10.634867763023376,11.030446746139527,11.730087763023377L10.970876746139526,11.649787763023376L10.970876746139526,11.549787763023376L11.514716746139527,11.549787763023376Q12.675766746139526,11.549787763023376,13.498966746139526,12.371987763023377Q14.322266746139526,13.194087763023376,14.322266746139526,14.353887763023376L14.322266746139526,15.211087763023377L2.6666667461395264,15.211087763023377L2.5666667461395263,15.211087763023377ZM8.444476746139525,11.749887763023377Q6.992416746139527,11.749887763023377,5.962916746139527,10.721677763023376Q4.9333667461395265,9.693447763023377,4.9333667461395265,8.243137763023377L4.9333667461395265,5.115057763023376Q4.9333667461395265,3.6647477630233767,5.962916746139527,2.6365277630233765Q6.992426746139526,1.6083457630233764,8.444476746139525,1.6083457630233764Q9.896536746139526,1.6083457630233764,10.926036746139527,2.6365277630233765Q11.955586746139526,3.6647577630233763,11.955586746139526,5.115057763023376L11.955586746139526,5.459427763023377L11.955556746139527,6.310487763023376L11.955556746139527,8.243137763023377Q11.955576746139526,9.693447763023377,10.926026746139526,10.721677763023376Q9.896536746139526,11.749887763023377,8.444476746139525,11.749887763023377L8.444476746139525,11.749887763023377ZM8.444476746139525,11.549887763023376Q9.813766746139526,11.549887763023376,10.784696746139526,10.580157763023376Q11.755576746139527,9.610527763023377,11.755556746139526,8.243137763023377L11.755556746139526,6.310477763023377L11.755586746139526,5.459427763023377L11.755586746139526,5.115057763023376Q11.755586746139526,3.7476777630233764,10.784706746139527,2.7780377630233763Q9.813766746139526,1.8083457630233766,8.444476746139525,1.8083457630233766Q7.075186746139527,1.8083457630233766,6.104246746139527,2.7780377630233763Q5.133366746139526,3.7476677630233763,5.133366746139526,5.115057763023376L5.133366746139526,8.243137763023377Q5.133366746139526,9.610527763023377,6.104246746139527,10.580157763023376Q7.075186746139527,11.549887763023376,8.444476746139525,11.549887763023376L8.444476746139525,11.549887763023376ZM3.5555827461395264,14.137287763023377L5.937956746139527,14.137287763023377L11.054926746139527,14.137287763023377L13.437366746139526,14.137287763023377L13.437366746139526,14.237287763023376L13.337466746139526,14.241387763023376Q13.308666746139526,13.533487763023377,12.794966746139526,13.039687763023377Q12.279916746139527,12.544487763023376,11.566766746139526,12.544387763023376L5.426206746139526,12.544487763023376Q4.713056746139527,12.544487763023376,4.197976746139526,13.039687763023377Q3.6843467461395263,13.533487763023377,3.6554997461395264,14.241387763023376L3.5555827461395264,14.237287763023376L3.5555827461395264,14.137287763023377ZM3.5555827461395264,14.337287763023376L3.4514247461395264,14.337287763023376L3.4556657461395264,14.233187763023377Q3.487787746139526,13.444987763023377,4.059356746139526,12.895487763023377Q4.632506746139526,12.344487763023377,5.426206746139526,12.344487763023377L11.566766746139526,12.344387763023377Q12.360466746139526,12.344487763023377,12.933566746139526,12.895487763023377Q13.505166746139526,13.444987763023377,13.537266746139526,14.233187763023377L13.541566746139527,14.337287763023376L11.054926746139527,14.337287763023376L5.937956746139527,14.337287763023376L3.5555827461395264,14.337287763023376Z'
        fill='#23262F'
        fillOpacity='1'
      />
    </g>
    <g>
      <path
        d='M4.44444465637207,5.46361465637207L4.44447748297207,6.22222465637207L6.14630465637207,6.22222465637207C7.094624656372071,6.22222465637207,8.03031465637207,6.02850465637207,8.85211465637207,5.66203465637207C9.361844656372071,5.4347176563720705,9.92503465637207,5.288539656372071,10.50574465637207,5.2309126563720705L10.50574465637207,6.17162465637207L11.485244656372071,6.17162465637207L11.485244656372071,5.20302265637207L12.44444465637207,5.20302265637207L12.358644656372071,4.44444465637207L11.06912465637207,4.44444465637207C10.12076465637207,4.44444465637207,9.18511465637207,4.63815965637207,8.36330465637207,5.00462965637207C7.6899346563720705,5.30489965637207,6.92330465637207,5.46361465637207,6.14626465637207,5.46361465637207L4.44444465637207,5.46361465637207Z'
        fill='#FFFFFF'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const SubtitleMoreSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='7'
    height='7'
    viewBox='0 0 7 7'
  >
    <g>
      <path
        d='M5.38086,3.78357L0.296931,6.04309L0.7030689999999999,6.95691L7.61914,3.8831L0.742821,0.062921L0.257179,0.937079L5.38086,3.78357Z'
        fillRule='evenodd'
        fill='#8D70FF'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const MaskCloseSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='24'
    height='24'
    viewBox='0 0 24 24'
  >
    <g>
      <path
        d='M18,7.05L16.95,6L12,10.95L7.05,6L6,7.05L10.95,12L6,16.95L7.05,18L12,13.05L16.95,18L18,16.95L13.05,12L18,7.05Z'
        fill='#FCFCFD'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const BackSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='26'
    height='26'
    viewBox='0 0 26 26'
  >
    <g>
      <path
        d='M14.957071301879882,20.074462381744382L7.882631301879883,13.000002381744384L14.957071301879882,5.925562381744385L16.10594130187988,7.074432381744385L10.180381301879883,13.000002381744384L16.10594130187988,18.925562381744385L14.957071301879882,20.074462381744382Z'
        fillRule='evenodd'
        fill='#3A3D48'
        fillOpacity='1'
      />
    </g>
  </svg>
);

export const CopySVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='15.75'
    height='15.75'
    viewBox='0 0 15.75 15.75'
  >
    <g transform='matrix(-1,0,0,1,31.5,0)'>
      <g>
        <path
          d='M20.25,3.375L30.375,3.375C30.9937,3.375,31.5,3.88125,31.5,4.5L31.5,14.625C31.5,15.2437,30.9937,15.75,30.375,15.75L20.25,15.75C19.63125,15.75,19.125,15.2437,19.125,14.625L19.125,4.5C19.125,3.88125,19.63125,3.375,20.25,3.375ZM30.375,14.625L30.375,4.5L20.25,4.5L20.25,14.625L30.375,14.625Z'
          fillRule='evenodd'
          fill='#B2B7C4'
          fillOpacity='1'
        />
      </g>
      <g>
        <path
          d='M16.875,9L15.75,9L15.75,1.125C15.75,0.50625,16.25625,0,16.875,0L24.75,0L24.75,1.125L16.875,1.125L16.875,9Z'
          fill='#B2B7C4'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const ConfigSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='28'
    height='28'
    viewBox='0 0 28 28'
  >
    <defs>
      <clipPath id='master_svg0_868_61016'>
        <rect x='28' y='0' width='28' height='28' rx='0' />
      </clipPath>
    </defs>
    <g transform='matrix(0,1,-1,0,28,-28)' clipPath='url(#master_svg0_868_61016)'>
      <g>
        <path
          d='M42.8758765625,14.1094L42.8758765625,2.624972C42.8758765625,2.141752,42.4799765625,1.75,41.9967765625,1.75C41.5134765625,1.75,41.1175765625,2.141752,41.1175765625,2.624972L41.1175765625,14.111C39.6161965625,14.5007,38.4978365625,15.8702,38.4978365625,17.4999C38.4978365625,19.1321,39.6140265625,20.5032,41.1252765625,20.8905C41.1204765625,20.9269,41.1178765625,20.9634,41.1175765625,21.0001L41.1175765625,25.3749C41.1175765625,25.8582,41.5134765625,26.2499,41.9967765625,26.2499C42.4799765625,26.2499,42.8758765625,25.8582,42.8758765625,25.3749L42.8758765625,21.0001C42.8751765625,20.964,42.8722765625,20.928,42.8671765625,20.8923C44.3816765625,20.5071,45.5030765625,19.1341,45.5030765625,17.5001C45.5033765625,15.8679,44.3871765625,14.4969,42.8758765625,14.1094ZM43.2373765625,18.7373C42.4237765625,19.551,41.0596765625,19.3714,40.4843765625,18.3749C39.9089765625,17.3784,40.4354765625,16.107300000000002,41.5468765625,15.8094C42.6584765625,15.5116,43.7499765625,16.3491,43.7499765625,17.4998C43.751276562499996,17.9642,43.5666765625,18.4099,43.2373765625,18.7373L43.2373765625,18.7373ZM34.1161565625,5.35943C34.1210965625,5.32315,34.1237665625,5.2866,34.1241565625,5.24999L34.1241565625,2.624972C34.1241565625,2.142175,33.7327765625,1.750790138,33.2499765625,1.750790138C32.7671765625,1.750790138,32.3757965625,2.142175,32.3757965625,2.624972L32.3757965625,5.24999C32.3751165625,5.28706,32.3767665625,5.3241499999999995,32.3807365625,5.36101C30.8694365625,5.74822,29.7509765625,7.11926,29.7509765625,8.751660000000001C29.7509765625,10.38405,30.8696365625,11.7545,32.3809265625,12.1422C32.3770665625,12.178,32.3753865625,12.2139,32.3758965625,12.2499L32.3758965625,25.3749C32.3758965625,25.8577,32.7672765625,26.2491,33.2500765625,26.2491C33.7328765625,26.2491,34.1242565625,25.8577,34.1242565625,25.3749L34.1242565625,12.25C34.1238565625,12.2144,34.1213165625,12.179,34.1166465625,12.1438C35.6311065625,11.7585,36.7512465625,10.38554,36.7512465625,8.751660000000001C36.7512465625,7.11777,35.6308165625,5.74447,34.1161565625,5.35943ZM34.4873665625,9.98914C33.6737065625,10.80282,32.3096765625,10.62327,31.7343065625,9.62674C31.1589465625,8.63022,31.6854165625,7.35913,32.7968965625,7.06129C33.9084065625,6.76346,34.9999165625,7.60097,34.9999165625,8.75165C35.0012165625,9.21605,34.8166465625,9.661660000000001,34.4873665625,9.98914ZM54.2498765625,8.75165C54.2498765625,7.11718,53.1274765625,5.74447,51.612576562499996,5.35943C51.6163765625,5.32308,51.617976562500004,5.28653,51.617276562499995,5.24999L51.617276562499995,2.624972C51.617276562499995,2.142175,51.225876562500005,1.750790272,50.7430765625,1.750790272C50.260276562499996,1.750790272,49.8688765625,2.142175,49.8688765625,2.624972L49.8688765625,5.24999C49.869276562500005,5.287129999999999,49.8720765625,5.32422,49.8770765625,5.36101C48.3657765625,5.74822,47.248976562500005,7.11926,47.248976562500005,8.751660000000001C47.248976562500005,10.38405,48.3651765625,11.755,49.8763765625,12.1423C49.871576562499996,12.1786,49.868976562499995,12.2151,49.8686765625,12.2516L49.8686765625,25.3749C49.8680765625,25.8582,50.259676562500005,26.2503,50.7428765625,26.2503C51.2261765625,26.2503,51.6177765625,25.8582,51.6170765625,25.3749L51.6170765625,12.2516C51.6177765625,12.2157,51.616276562500005,12.1797,51.612576562499996,12.1439C53.127576562499996,11.7587,54.2498765625,10.38603,54.2498765625,8.75165ZM51.9872765625,9.98904C51.1735765625,10.80259,49.8096765625,10.6229,49.2344765625,9.626380000000001C48.6591765625,8.62986,49.185776562499996,7.35891,50.2971765625,7.06118C51.4085765625,6.7634,52.499976562499995,7.60096,52.499976562499995,8.75165C52.5011765625,9.21603,52.3165765625,9.6616,51.9872765625,9.98904Z'
          fill='#624AFF'
          fillOpacity='1'
        />
      </g>
    </g>
  </svg>
);

export const SwitchViewSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='22'
    height='22'
    viewBox='0 0 22 22'
  >
    <g>
      <g>
        <path
          d='M11.6875,19.25L19.25,19.25C20.00625,19.25,20.625,18.63125,20.625,17.875L20.625,13.75C20.625,12.99375,20.00625,12.375,19.25,12.375L11.6875,12.375C10.93125,12.375,10.3125,12.99375,10.3125,13.75L10.3125,17.875C10.3125,18.63125,10.93125,19.25,11.6875,19.25ZM11.6875,17.875L11.6875,13.75L19.25,13.75L19.25,17.875L11.6875,17.875Z'
          fillRule='evenodd'
          fill='#FFFFFF'
        />
      </g>
      <g>
        <path
          d='M8.2499708203125,7.5150928906250005L8.2499708203125,9.343212890625L5.7531408203125,6.840565890625C5.5947508203125,6.6818078906250005,5.5134408203125,6.600952890625,5.4218608203125,6.571819890625C5.3430308203125,6.546745890625,5.2579648203125,6.546695890625,5.1791098203125,6.571678890625C5.0874918203125,6.600704890625,5.0060888203125,6.681464890625,4.8475158203125,6.840037890625L4.7774168203125,6.910136890625C4.6188428203125,7.068710890625,4.5380828203125,7.150113890625,4.5090568203125,7.241731890625C4.4840738203125,7.320586890625,4.4841238203125,7.405652890625,4.5091978203125,7.484482890625C4.5383308203125,7.576062890625,4.6191858203125005,7.657372890625,4.7779438203125,7.815762890625L7.2805908203125,10.312592890625L5.4524708203125005,10.312592890625C5.2284448203125,10.312592890625,5.1139018203125,10.313042890624999,5.0286428203125,10.357232890625C4.9552568203125,10.395272890625,4.8951528203125,10.455382890625,4.8571128203125,10.528762890625C4.8129198203125,10.614022890625,4.8124658203125,10.728562890625,4.8124658203125,10.952592890624999L4.8124658203125,11.047582890625C4.8124658203125,11.271612890625,4.8129198203125,11.386152890624999,4.8571128203125,11.471412890625C4.8951528203125,11.544792890625,4.9552568203125,11.604902890624999,5.0286428203125,11.642942890625001C5.1139018203125,11.687132890625,5.2284448203125,11.687582890625,5.4524708203125005,11.687582890625L8.9849608203125,11.687582890625C9.2089908203125,11.687582890625,9.323530820312499,11.687132890625,9.4087908203125,11.642942890625001C9.4821708203125,11.604902890624999,9.542280820312499,11.544792890625,9.580320820312501,11.471412890625C9.6245108203125,11.386152890624999,9.6249608203125,11.271612890625,9.6249608203125,11.047582890625L9.6249608203125,7.5150928906250005C9.6249608203125,7.291066890625,9.6245108203125,7.176523890625,9.580320820312501,7.091264890625C9.542280820312499,7.017878890625,9.4821708203125,6.957774890625,9.4087908203125,6.919734890625C9.323530820312499,6.875541890625,9.2089908203125,6.875087890625,8.9849608203125,6.875087890625L8.889970820312499,6.875087890625C8.6659408203125,6.875087890625,8.5514008203125,6.875541890625,8.4661408203125,6.919734890625C8.3927608203125,6.957774890625,8.3326508203125,7.017878890625,8.2946108203125,7.091264890625C8.250420820312499,7.176523890625,8.2499708203125,7.291066890625,8.2499708203125,7.5150928906250005'
          fill='#FFFFFF'
        />
      </g>
      <g>
        <path
          d='M8.2975,15.125L2.75,15.125C1.99375,15.125,1.375,14.5063,1.375,13.75L1.375,4.8125C1.375,4.05625,1.99375,3.4375,2.75,3.4375L17.875,3.4375C18.6313,3.4375,19.25,4.05625,19.25,4.8125L19.25,10.36C19.25,10.584019999999999,19.2495,10.69856,19.2054,10.78382C19.1673,10.85721,19.1072,10.91731,19.0338,10.95535C18.9486,10.99955,18.834,11,18.61,11L18.515,11C18.291,11,18.1764,10.99955,18.0912,10.95535C18.0178,10.91731,17.9577,10.85721,17.9196,10.78382C17.8755,10.69856,17.875,10.584019999999999,17.875,10.36L17.875,4.8125L2.75,4.8125L2.75,13.75L8.2975,13.75C8.521519999999999,13.75,8.63606,13.7505,8.72132,13.7946C8.79471,13.8327,8.85481,13.8928,8.89285,13.9662C8.93705,14.0514,8.9375,14.166,8.9375,14.39L8.9375,14.485C8.9375,14.709,8.93705,14.8236,8.89285,14.9088C8.85481,14.9822,8.79471,15.0423,8.72132,15.0804C8.63606,15.1245,8.521519999999999,15.125,8.2975,15.125'
          fill='#FFFFFF'
        />
      </g>
    </g>
  </svg>
);

export const NoCameraSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='75'
    height='75'
    viewBox='0 0 75 75'
  >
    <g>
      <g>
        <path
          d='M37.5,0C16.7893,-3.99027e-7,0,16.7893,0,37.5C0,58.2107,16.7893,75,37.5,75C58.2107,75,75,58.2107,75,37.5C74.9712,16.8013,58.1987,0.0288101,37.5,0Z'
          fill='#FFFFFF'
          fillOpacity='0.2'
        />
      </g>
      <g>
        <g>
          <path
            d='M54.3684,42.66681908623242L20.34156,42.66681908623242C18.80827,42.66164709823242,17.38942,43.43947793823242,16.622778,44.70548793823242C15.792408,46.07671793823242,15.792408,47.76614793823242,16.622778,49.13737793823242C20.78097,56.49926793823242,28.8691,61.06436793823242,37.6389,60.99926793823242C46.4088,60.93426793823242,54.4215,56.249767938232424,58.459,48.827137938232426C59.1803,47.61806793823242,59.1803,46.13616793823242,58.459,44.92707793823242C57.6292,43.51942193823242,56.062,42.65348123823242,54.3684,42.66681908623242Z'
            fill='#FFFFFF'
          />
        </g>
        <g>
          <path
            d='M37.500000780334474,36.25C42.300210780334474,36.25,46.19151078033447,32.2435,46.19151078033447,27.3012L46.19151078033447,25.94443C46.189110780334474,21.00385,42.298510780334475,17,37.500000780334474,17C32.70148078033447,17,28.810854300334473,21.00385,28.808510780334473,25.94443L28.808510780334473,27.3012C28.80850757442447,32.2435,32.69982078033447,36.25,37.500000780334474,36.25Z'
            fill='#FFFFFF'
          />
        </g>
      </g>
    </g>
  </svg>
);

export const WelcomeArrowLeftSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='20'
    viewBox='0 0 20 20'
  >
    <g transform='matrix(0,1,-1,0,20,-20)'>
      <g>
        <path
          d='M30.146876488418577,12.799940226745605L35.55320648841858,7.393548226745605C35.67050648841858,7.276340026745605,35.82940648841858,7.210490077733605,35.99520648841858,7.210490077733605C36.34040648841858,7.210490077733605,36.62020648841858,7.490315226745605,36.62020648841858,7.8354902267456055C36.62020648841858,8.001248226745606,36.554306488418575,8.160223226745606,36.43670648841858,8.277810226745606L31.03074648841858,13.683820226745606C30.74598648841858,13.968580226745605,30.40226648841858,14.110970226745605,29.99955648841858,14.110970226745605C29.596846488418578,14.110970226745605,29.253116488418577,13.968580226745605,28.96835648841858,13.683820226745606L23.56196548841858,8.277430226745606C23.44475708841858,8.160223226745606,23.37890713413558,8.001248226745606,23.37890713413558,7.8354902267456055C23.37890713413558,7.490315226745605,23.65873248841858,7.2104903757576055,24.00390748841858,7.2104903757576055C24.16966548841858,7.2104903757576055,24.32864048841858,7.276340426745605,24.44597648841858,7.393673226745605L29.85223648841858,12.799940226745605C29.950446488418578,12.898150226745607,30.04866648841858,12.898150226745607,30.146876488418577,12.799940226745605Z'
          fill='#26244C'
        />
        <path
          d='M23.17890748841858,7.8354902267456055Q23.17890748841858,8.177216226745605,23.42054408841858,8.418850226745606L28.82693648841858,13.825250226745606Q29.31265648841858,14.310970226745606,29.99955648841858,14.310970226745606Q30.68644648841858,14.310970226745606,31.17216648841858,13.825240226745606L36.57820648841858,8.419230226745606Q36.82020648841858,8.177140226745605,36.82020648841858,7.8354902267456055Q36.82020648841858,7.493766226745605,36.57860648841858,7.252127426745606Q36.33700648841858,7.010490226745605,35.99520648841858,7.010490226745605Q35.65350648841858,7.010490226745605,35.41180648841858,7.252127426745606L30.00545648841858,12.658520226745605L29.99955648841858,12.673600226745606L29.99366648841858,12.658520226745605L24.58739648841858,7.252252326745605Q24.345633488418578,7.010490226745605,24.00390748841858,7.010490226745605Q23.662183488418577,7.010490226745605,23.420545088418578,7.252128426745606Q23.17890748841858,7.493766226745605,23.17890748841858,7.8354902267456055ZM35.55320648841858,7.393548226745605L30.146876488418577,12.799940226745605C30.04866648841858,12.898150226745607,29.950446488418578,12.898150226745607,29.85223648841858,12.799940226745605L24.44597648841858,7.393673226745605C24.32864048841858,7.276340426745605,24.16966548841858,7.2104902267456055,24.00390748841858,7.2104902267456055C23.65873248841858,7.2104902267456055,23.37890648841858,7.490315226745605,23.37890648841858,7.8354902267456055C23.37890648841858,8.001248226745606,23.44475708841858,8.160223226745606,23.56196548841858,8.277430226745606L28.96835648841858,13.683820226745606C29.253116488418577,13.968580226745605,29.596846488418578,14.110970226745605,29.99955648841858,14.110970226745605C30.40226648841858,14.110970226745605,30.74598648841858,13.968580226745605,31.03074648841858,13.683820226745606L36.43670648841858,8.277810226745606C36.554306488418575,8.160223226745606,36.62020648841858,8.001248226745606,36.62020648841858,7.8354902267456055C36.62020648841858,7.490315226745605,36.34040648841858,7.2104902267456055,35.99520648841858,7.2104902267456055C35.82940648841858,7.2104902267456055,35.67050648841858,7.276340026745605,35.55320648841858,7.393548226745605Z'
          fillRule='evenodd'
          fill='#26244C'
        />
      </g>
    </g>
  </svg>
);

export const WelcomeArrowRightSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='20'
    height='20'
    viewBox='0 0 20 20'
  >
    <g transform='matrix(0,1,1,0,0,0)'>
      <g>
        <path
          d='M10.14687648841858,12.799940226745605L15.55320648841858,7.393548226745605C15.67050648841858,7.276340026745605,15.829406488418579,7.210490077733605,15.99520648841858,7.210490077733605C16.34040648841858,7.210490077733605,16.62020648841858,7.490315226745605,16.62020648841858,7.8354902267456055C16.62020648841858,8.001248226745606,16.55430648841858,8.160223226745606,16.43670648841858,8.277810226745606L11.030746488418579,13.683820226745606C10.745986488418579,13.968580226745605,10.40226648841858,14.110970226745605,9.99955648841858,14.110970226745605C9.596846488418578,14.110970226745605,9.253116488418579,13.968580226745605,8.968356488418578,13.683820226745606L3.561965488418579,8.277430226745606C3.444757088418579,8.160223226745606,3.378907134135579,8.001248226745606,3.378907134135579,7.8354902267456055C3.378907134135579,7.490315226745605,3.658732488418579,7.2104903757576055,4.003907488418579,7.2104903757576055C4.169665488418579,7.2104903757576055,4.328640488418579,7.276340426745605,4.445976488418579,7.393673226745605L9.85223648841858,12.799940226745605C9.950446488418578,12.898150226745607,10.04866648841858,12.898150226745607,10.14687648841858,12.799940226745605Z'
          fill='#26244C'
        />
        <path
          d='M3.178907488418579,7.8354902267456055Q3.178907488418579,8.177216226745605,3.420544088418579,8.418850226745606L8.826936488418578,13.825250226745606Q9.312656488418579,14.310970226745606,9.99955648841858,14.310970226745606Q10.686446488418579,14.310970226745606,11.17216648841858,13.825240226745606L16.57820648841858,8.419230226745606Q16.820206488418577,8.177140226745605,16.820206488418577,7.8354902267456055Q16.820206488418577,7.493766226745605,16.57860648841858,7.252127426745606Q16.33700648841858,7.010490226745605,15.99520648841858,7.010490226745605Q15.653506488418579,7.010490226745605,15.411806488418579,7.252127426745606L10.005456488418579,12.658520226745605L9.99955648841858,12.673600226745606L9.99366648841858,12.658520226745605L4.587396488418579,7.252252326745605Q4.345633488418579,7.010490226745605,4.003907488418579,7.010490226745605Q3.662183488418579,7.010490226745605,3.4205450884185793,7.252128426745606Q3.178907488418579,7.493766226745605,3.178907488418579,7.8354902267456055ZM15.55320648841858,7.393548226745605L10.14687648841858,12.799940226745605C10.04866648841858,12.898150226745607,9.950446488418578,12.898150226745607,9.85223648841858,12.799940226745605L4.445976488418579,7.393673226745605C4.328640488418579,7.276340426745605,4.169665488418579,7.2104902267456055,4.003907488418579,7.2104902267456055C3.658732488418579,7.2104902267456055,3.378906488418579,7.490315226745605,3.378906488418579,7.8354902267456055C3.378906488418579,8.001248226745606,3.444757088418579,8.160223226745606,3.561965488418579,8.277430226745606L8.968356488418578,13.683820226745606C9.253116488418579,13.968580226745605,9.596846488418578,14.110970226745605,9.99955648841858,14.110970226745605C10.40226648841858,14.110970226745605,10.745986488418579,13.968580226745605,11.030746488418579,13.683820226745606L16.43670648841858,8.277810226745606C16.55430648841858,8.160223226745606,16.62020648841858,8.001248226745606,16.62020648841858,7.8354902267456055C16.62020648841858,7.490315226745605,16.34040648841858,7.2104902267456055,15.99520648841858,7.2104902267456055C15.829406488418579,7.2104902267456055,15.67050648841858,7.276340026745605,15.55320648841858,7.393548226745605Z'
          fillRule='evenodd'
          fill='#26244C'
        />
      </g>
    </g>
  </svg>
);

export const backSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='22'
    height='22'
    viewBox='0 0 22 22'
  >
    <g>
      <path
        d='M9.47461845954895,3.8491721153259277C9.854328459548949,3.8491721153259277,10.16211845954895,4.156980115325927,10.16211845954895,4.536672115325928C10.16211845954895,4.719006115325928,10.08969845954895,4.893882115325928,9.96075845954895,5.0228121153259275L4.66379845954895,10.319772115325929L18.55892845954895,10.319772115325929C18.93862845954895,10.319772115325929,19.24642845954895,10.627572115325929,19.24642845954895,11.007272115325929C19.24642845954895,11.386972115325928,18.93862845954895,11.694772115325929,18.55892845954895,11.694772115325929L4.76396845954895,11.694772115325929L10.01048845954895,16.94127211532593C10.13980845954895,17.070572115325927,10.21223845954895,17.245472115325928,10.21223845954895,17.427872115325926C10.21223845954895,17.807572115325925,9.904438459548949,18.115372115325926,9.52473845954895,18.115372115325926C9.342408459548949,18.115372115325926,9.16753845954895,18.042872115325927,9.03859845954895,17.913972115325926L3.1908634595489502,12.066202115325927L3.1724754595489504,12.047082115325928C2.88611745954895,11.737732115325928,2.74380493954895,11.372282115325927,2.74554662654895,10.950732115325927C2.7472882895489503,10.529192115325927,2.8926164595489503,10.164922115325929,3.18152245954895,9.857952115325928L3.1960334595489504,9.842992115325927L8.98834845954895,4.050672115325928C9.11741845954895,3.9215966153259276,9.29228845954895,3.8491708313459276,9.47461845954895,3.8491721153259277Z'
        fill='#26244C'
      />
    </g>
  </svg>
);

export const subtitleListSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='16'
    height='16'
    viewBox='0 0 16 16'
  >
    <g>
      <g>
        <path
          d='M12.67605,3.462022828125C12.9212,3.462022828125,13.15443,3.558753828125,13.33067,3.735003828125C13.50692,3.911243828125,13.60365,4.143143828125,13.60365,4.389623828125L13.60365,11.679223828125C13.60365,11.924373828125,13.50692,12.157603828125,13.33067,12.333843828125C13.15443,12.510093828125,12.92253,12.606823828125,12.67605,12.606823828125L5.38645,12.606823828125C5.1413,12.606823828125,4.90807,12.510093828125,4.73183,12.333843828125C4.55558,12.157603828125,4.458849,11.925703828125,4.458849,11.679223828125L4.458849,4.389623828125C4.458849,4.144473828125,4.55558,3.911243828125,4.73183,3.735003828125C4.90807,3.558753828125,5.13997,3.462022828125,5.38645,3.462022828125L12.67605,3.462022828125ZM12.67605,2.534423828125L5.38645,2.534423828125C4.366089,2.534423828125,3.53125,3.369262828125,3.53125,4.389623828125L3.53125,11.679223828125C3.53125,12.699623828125,4.366089,13.534423828125,5.38645,13.534423828125L12.67605,13.534423828125C13.69645,13.534423828125,14.53125,12.699623828125,14.53125,11.679223828125L14.53125,4.389623828125C14.53125,3.369262828125,13.69645,2.534423828125,12.67605,2.534423828125Z'
          fill='#26244C'
        />
      </g>
      <g>
        <path
          d='M11.796173689804078,5.007055044174194L6.267687689804077,5.007055044174194C6.013260689804077,5.007055044174194,5.805213689804077,5.215102044174194,5.805213689804077,5.469529044174195C5.805213689804077,5.7239570441741945,6.013260689804077,5.932004044174194,6.267687689804077,5.932004044174194L11.796173689804078,5.932004044174194C12.050603689804078,5.932004044174194,12.258653689804078,5.7239570441741945,12.258653689804078,5.469529044174195C12.258653689804078,5.215102044174194,12.050603689804078,5.007055044174194,11.796173689804078,5.007055044174194Z'
          fill='#26244C'
        />
      </g>
      <g>
        <path
          d='M9.800513689804077,7.57120418548584L6.267687689804077,7.57120418548584C6.013260689804077,7.57120418548584,5.805213689804077,7.7792511854858395,5.805213689804077,8.03367818548584C5.805213689804077,8.288106185485839,6.013260689804077,8.49615318548584,6.267687689804077,8.49615318548584L9.800513689804077,8.49615318548584C10.054943689804077,8.49615318548584,10.262993689804077,8.288106185485839,10.262993689804077,8.03367818548584C10.262993689804077,7.7792511854858395,10.054943689804077,7.57120418548584,9.800513689804077,7.57120418548584Z'
          fill='#26244C'
        />
      </g>
      <g>
        <path
          d='M11.796173689804078,7.555261135101318L11.256843689804077,7.555261135101318C11.002423689804077,7.555261135101318,10.794373689804077,7.763308135101318,10.794373689804077,8.017735135101319C10.794373689804077,8.272163135101318,11.002423689804077,8.480210135101318,11.256843689804077,8.480210135101318L11.796173689804078,8.480210135101318C12.050603689804078,8.480210135101318,12.258653689804078,8.272163135101318,12.258653689804078,8.017735135101319C12.258653689804078,7.763308135101318,12.050603689804078,7.555261135101318,11.796173689804078,7.555261135101318ZM11.796173689804078,10.135311135101318L8.263353689804077,10.135311135101318C8.008923689804078,10.135311135101318,7.800873689804077,10.343361135101318,7.800873689804077,10.597781135101318C7.800873689804077,10.852211135101317,8.008923689804078,11.060261135101317,8.263353689804077,11.060261135101317L11.796173689804078,11.060261135101317C12.050603689804078,11.060261135101317,12.258653689804078,10.852211135101317,12.258653689804078,10.597781135101318C12.258653689804078,10.343361135101318,12.050603689804078,10.135311135101318,11.796173689804078,10.135311135101318ZM6.807023689804077,10.135311135101318L6.267687689804077,10.135311135101318C6.013260689804077,10.135311135101318,5.805213689804077,10.343361135101318,5.805213689804077,10.597781135101318C5.805213689804077,10.852211135101317,6.013260689804077,11.060261135101317,6.267687689804077,11.060261135101317L6.807023689804077,11.060261135101317C7.0614436898040776,11.060261135101317,7.269493689804078,10.852211135101317,7.269493689804078,10.597781135101318C7.269493689804078,10.343361135101318,7.0614436898040776,10.135311135101318,6.807023689804077,10.135311135101318Z'
          fill='#26244C'
        />
      </g>
    </g>
  </svg>
);

export const dialogCloseSVG = (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width='12'
    height='12'
    viewBox='0 0 12 12'
  >
    <g>
      <path
        d='M6,5.18856L0.811437,0L0,0.811437L5.18856,6L0,11.1886L0.811437,12L6,6.81144L11.1886,12L12,11.1886L6.81144,6L12,0.811437L11.1886,0L6,5.18856Z'
        fillRule='evenodd'
        fill='#555555'
      />
    </g>
  </svg>
);
