package cn.zhentao;

import cn.zhentao.dto.RechargeRequest;
import cn.zhentao.dto.RechargeResponse;
import cn.zhentao.entity.MemberRecharge;
import cn.zhentao.service.MemberRechargeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

/**
 * 会员充值服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MemberRechargeServiceTest {

    @Autowired
    private MemberRechargeService memberRechargeService;

    @Test
    public void testCreateRechargeOrder() {
        // 创建充值请求
        RechargeRequest request = new RechargeRequest();
        request.setUserId(1L);
        request.setRoleId(1L);
        request.setRechargeAmount(new BigDecimal("99.00"));
        request.setPayChannel(MemberRecharge.PayChannel.WECHAT.getCode());
        request.setMemberMonths(3);
        request.setRemark("测试充值");

        // 创建充值订单
        RechargeResponse response = memberRechargeService.createRechargeOrder(request);

        // 验证结果
        assert response != null;
        assert response.getRechargeId() != null;
        assert response.getTradeNo() != null;
        assert response.getRechargeAmount().equals(new BigDecimal("99.00"));
        
        System.out.println("充值订单创建成功:");
        System.out.println("订单ID: " + response.getRechargeId());
        System.out.println("交易号: " + response.getTradeNo());
        System.out.println("支付二维码: " + response.getQrCode());
    }

    @Test
    public void testCheckMemberStatus() {
        Long userId = 1L;
        
        // 检查会员状态
        boolean isValidMember = memberRechargeService.isValidMember(userId);
        MemberRecharge currentMember = memberRechargeService.getCurrentMemberInfo(userId);
        Double totalAmount = memberRechargeService.getTotalRechargeAmount(userId);
        
        System.out.println("用户ID: " + userId);
        System.out.println("是否为有效会员: " + isValidMember);
        System.out.println("当前会员信息: " + currentMember);
        System.out.println("累计充值金额: " + totalAmount);
    }

    @Test
    public void testPaymentCallback() {
        String tradeNo = "MR1703123456001";
        Integer payChannel = MemberRecharge.PayChannel.WECHAT.getCode();
        
        // 模拟支付回调数据
        java.util.Map<String, Object> callbackData = new java.util.HashMap<>();
        callbackData.put("return_code", "SUCCESS");
        callbackData.put("result_code", "SUCCESS");
        callbackData.put("out_trade_no", tradeNo);
        callbackData.put("transaction_id", "wx123456789");
        
        // 处理支付回调
        boolean success = memberRechargeService.handlePaymentCallback(tradeNo, payChannel, callbackData);
        
        System.out.println("支付回调处理结果: " + success);
        
        // 验证充值记录状态
        MemberRecharge recharge = memberRechargeService.getRechargeByTradeNo(tradeNo);
        if (recharge != null) {
            System.out.println("充值记录状态: " + recharge.getPayStatus());
            System.out.println("支付时间: " + recharge.getPayTime());
        }
    }
}
