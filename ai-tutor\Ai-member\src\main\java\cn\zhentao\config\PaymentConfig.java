package cn.zhentao.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "payment")
public class PaymentConfig {

    /**
     * 微信支付配置
     */
    private WechatConfig wechat = new WechatConfig();

    /**
     * 支付宝配置
     */
    private AlipayConfig alipay = new AlipayConfig();

    @Data
    public static class WechatConfig {
        /**
         * 应用ID
         */
        private String appId = "wx1234567890abcdef";

        /**
         * 商户号
         */
        private String mchId = "1234567890";

        /**
         * API密钥
         */
        private String apiKey = "your_wechat_api_key_here";

        /**
         * 证书路径
         */
        private String certPath = "classpath:cert/wechat_cert.p12";

        /**
         * 支付回调地址
         */
        private String notifyUrl = "http://your-domain.com/api/member/recharge/callback/wechat";

        /**
         * 支付网关地址
         */
        private String gatewayUrl = "https://api.mch.weixin.qq.com";
    }

    @Data
    public static class AlipayConfig {
        /**
         * 应用ID
         */
        private String appId = "2021000000000000";

        /**
         * 私钥
         */
        private String privateKey = "your_alipay_private_key_here";

        /**
         * 支付宝公钥
         */
        private String alipayPublicKey = "your_alipay_public_key_here";

        /**
         * 支付回调地址
         */
        private String notifyUrl = "http://your-domain.com/api/member/recharge/callback/alipay";

        /**
         * 支付网关地址
         */
        private String gatewayUrl = "https://openapi.alipay.com/gateway.do";

        /**
         * 签名类型
         */
        private String signType = "RSA2";

        /**
         * 字符编码
         */
        private String charset = "UTF-8";

        /**
         * 数据格式
         */
        private String format = "json";
    }
}
