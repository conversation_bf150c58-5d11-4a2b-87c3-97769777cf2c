package cn.zhentao.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 会员充值请求DTO
 */
@Data
public class RechargeRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 角色ID（会员类型）
     */
    @NotNull(message = "会员类型不能为空")
    private Long roleId;

    /**
     * 充值金额
     */
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    private BigDecimal rechargeAmount;

    /**
     * 支付渠道（1-微信，2-支付宝）
     */
    @NotNull(message = "支付渠道不能为空")
    private Integer payChannel;

    /**
     * 会员时长（月数）
     */
    @NotNull(message = "会员时长不能为空")
    private Integer memberMonths;

    /**
     * 备注
     */
    private String remark;
}
