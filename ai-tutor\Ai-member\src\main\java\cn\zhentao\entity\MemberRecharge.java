package cn.zhentao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员充值记录表
 * @TableName member_recharge
 */
@TableName(value = "member_recharge")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberRecharge implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称（如：普通会员、VIP）
     */
    private String roleName;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 支付渠道（1-微信，2-支付宝）
     */
    private Integer payChannel;

    /**
     * 支付订单号（微信/支付宝返回）
     */
    private String tradeNo;

    /**
     * 支付状态（0-未支付，1-已支付，2-支付失败）
     */
    private Integer payStatus;

    /**
     * 支付完成时间
     */
    private LocalDateTime payTime;

    /**
     * 会员生效时间
     */
    private LocalDateTime memberStartTime;

    /**
     * 会员失效时间
     */
    private LocalDateTime memberEndTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 备注（如：活动充值、首次充值）
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 支付渠道枚举
     */
    public enum PayChannel {
        WECHAT(1, "微信支付"),
        ALIPAY(2, "支付宝");

        private final Integer code;
        private final String desc;

        PayChannel(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static PayChannel getByCode(Integer code) {
            for (PayChannel channel : values()) {
                if (channel.getCode().equals(code)) {
                    return channel;
                }
            }
            return null;
        }
    }

    /**
     * 支付状态枚举
     */
    public enum PayStatus {
        UNPAID(0, "未支付"),
        PAID(1, "已支付"),
        FAILED(2, "支付失败");

        private final Integer code;
        private final String desc;

        PayStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static PayStatus getByCode(Integer code) {
            for (PayStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
