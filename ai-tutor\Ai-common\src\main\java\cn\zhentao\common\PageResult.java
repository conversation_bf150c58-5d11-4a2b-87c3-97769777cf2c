package cn.zhentao.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页返回结果类
 * 专门用于分页数据的统一返回格式
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 返回消息
     */
    private String message;

    /**
     * 分页数据
     */
    private PageData<T> data;

    /**
     * 时间戳
     */
    private Long timestamp;

    public PageResult() {
        this.timestamp = System.currentTimeMillis();
    }

    public PageResult(Integer code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public PageResult(Integer code, String message, PageData<T> data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 分页数据内部类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PageData<T> implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 数据列表
         */
        private List<T> records;

        /**
         * 总记录数
         */
        private Long total;

        /**
         * 每页大小
         */
        private Long size;

        /**
         * 当前页
         */
        private Long current;

        /**
         * 总页数
         */
        private Long pages;

        /**
         * 是否有上一页
         */
        private Boolean hasPrevious;

        /**
         * 是否有下一页
         */
        private Boolean hasNext;

        public PageData() {}

        public PageData(IPage<T> page) {
            this.records = page.getRecords();
            this.total = page.getTotal();
            this.size = page.getSize();
            this.current = page.getCurrent();
            this.pages = page.getPages();
            // 计算是否有上一页和下一页
            this.hasPrevious = page.getCurrent() > 1;
            this.hasNext = page.getCurrent() < page.getPages();
        }
    }

    /**
     * 分页成功返回
     */
    public static <T> PageResult<T> success(IPage<T> page) {
        PageData<T> pageData = new PageData<>(page);
        return new PageResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), pageData);
    }

    /**
     * 分页成功返回（自定义消息）
     */
    public static <T> PageResult<T> success(String message, IPage<T> page) {
        PageData<T> pageData = new PageData<>(page);
        return new PageResult<>(ResultCode.SUCCESS.getCode(), message, pageData);
    }

    /**
     * 分页失败返回
     */
    public static <T> PageResult<T> error() {
        return new PageResult<>(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage());
    }

    /**
     * 分页失败返回（自定义消息）
     */
    public static <T> PageResult<T> error(String message) {
        return new PageResult<>(ResultCode.ERROR.getCode(), message);
    }

    /**
     * 分页失败返回（自定义状态码和消息）
     */
    public static <T> PageResult<T> error(Integer code, String message) {
        return new PageResult<>(code, message);
    }

    /**
     * 分页失败返回（使用结果码枚举）
     */
    public static <T> PageResult<T> error(ResultCode resultCode) {
        return new PageResult<>(resultCode.getCode(), resultCode.getMessage());
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
