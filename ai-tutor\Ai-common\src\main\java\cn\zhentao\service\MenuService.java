package cn.zhentao.service;

import cn.zhentao.pojo.Menu;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 菜单管理服务接口
 * <AUTHOR>
 * @createDate 2025-07-30
 */
public interface MenuService extends IService<Menu> {

    /**
     * 获取菜单树结构
     */
    List<Menu> getMenuTree();

    /**
     * 根据父菜单ID获取子菜单
     */
    List<Menu> getMenusByParentId(Long parentId);

    /**
     * 根据用户ID获取用户菜单树
     */
    List<Menu> getUserMenuTree(Long userId);

    /**
     * 根据角色ID获取角色菜单
     */
    List<Menu> getMenusByRoleId(Long roleId);

    /**
     * 分页查询菜单
     */
    IPage<Menu> getMenuPage(Page<Menu> page, String menuName, String status);

    /**
     * 检查菜单名称是否存在
     */
    boolean existsByMenuName(String menuName, Long parentId);

    /**
     * 检查路由地址是否存在
     */
    boolean existsByPath(String path);

    /**
     * 获取所有启用的菜单
     */
    List<Menu> getEnabledMenus();

    /**
     * 批量删除菜单
     */
    boolean deleteMenusByIds(List<Long> menuIds);

    /**
     * 更新菜单状态
     */
    boolean updateMenuStatus(Long menuId, String status);

    /**
     * 构建菜单树
     */
    List<Menu> buildMenuTree(List<Menu> menus);

    /**
     * 获取菜单的所有子菜单ID
     */
    List<Long> getChildMenuIds(Long menuId);

    /**
     * 检查菜单是否有子菜单
     */
    boolean hasChildMenus(Long menuId);
}
