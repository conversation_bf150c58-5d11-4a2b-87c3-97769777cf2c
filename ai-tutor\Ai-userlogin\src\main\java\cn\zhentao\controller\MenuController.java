package cn.zhentao.controller;

import cn.zhentao.pojo.Menu;
import cn.zhentao.service.MenuService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/menu")
public class MenuController {
    @Autowired
    private MenuService menuService;

    @GetMapping("/list")
    public Map<String, Object> getMenuList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String menuName,
            @RequestParam(required = false) String status) {
        
        Page<Menu> page = new Page<>(pageNum, pageSize);
        IPage<Menu> menuPage = menuService.getMenuPage(page, menuName, status);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", menuPage);
        return result;
    }

    @GetMapping("/tree")
    public Map<String, Object> getMenuTree() {
        List<Menu> menus = menuService.getMenuTree();
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", menus);
        return result;
    }

    @GetMapping("/{menuId}")
    public Map<String, Object> getMenuById(@PathVariable Long menuId) {
        Menu menu = menuService.getById(menuId);
        Map<String, Object> result = new HashMap<>();
        if (menu == null) {
            result.put("code", 404);
            result.put("message", "菜单不存在");
        } else {
            result.put("code", 200);
            result.put("data", menu);
        }
        return result;
    }

    @PostMapping
    public Map<String, Object> addMenu(@RequestBody Menu menu) {
        boolean success = menuService.save(menu);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "添加成功");
        } else {
            result.put("code", 500);
            result.put("message", "添加失败");
        }
        return result;
    }

    @PutMapping
    public Map<String, Object> updateMenu(@RequestBody Menu menu) {
        boolean success = menuService.updateById(menu);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "修改成功");
        } else {
            result.put("code", 500);
            result.put("message", "修改失败");
        }
        return result;
    }

    @DeleteMapping("/{menuId}")
    public Map<String, Object> deleteMenu(@PathVariable Long menuId) {
        boolean success = menuService.removeById(menuId);
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "删除成功");
        } else {
            result.put("code", 500);
            result.put("message", "删除失败");
        }
        return result;
    }

    @GetMapping("/user/current")
    public Map<String, Object> getCurrentUserMenuTree() {
        // 这里需要从SecurityContext中获取当前用户ID
        // 简化实现，返回所有菜单
        List<Menu> menus = menuService.getMenuTree();
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", menus);
        return result;
    }
}
