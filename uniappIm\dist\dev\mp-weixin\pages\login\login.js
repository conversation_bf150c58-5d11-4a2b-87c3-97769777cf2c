"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  data() {
    return {
      loading: false,
      // 登录加载状态
      showRegisterModal: false,
      // 注册弹窗显示状态
      loginForm: {
        // 登录表单数据
        username: "",
        // 用户输入账号
        password: ""
        // 用户输入密码
      },
      registerForm: {
        // 注册表单数据
        username: "",
        password: "",
        nickname: ""
      }
    };
  },
  methods: {
    /**
     * 处理登录
     */
    async handleLogin() {
      if (!this.loginForm.username || !this.loginForm.password) {
        common_vendor.index.showToast({
          title: "请填写完整信息",
          icon: "none"
        });
        return;
      }
      this.loading = true;
      try {
        console.log("=== 开始登录 ===");
        console.log("登录表单数据:", this.loginForm);
        console.log("API地址:", "http://localhost:8080/api/auth/login");
        const result = await utils_api.login(this.loginForm);
        console.log("登录API响应:", result);
        if (result.code === 200) {
          common_vendor.index.setStorageSync("token", result.data.token);
          common_vendor.index.setStorageSync("userInfo", result.data.user);
          console.log("登录成功，保存的用户信息:", result.data.user);
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success"
          });
          common_vendor.index.reLaunch({
            url: "/pages/chat/chat"
          });
        } else {
          console.log("登录失败:", result.message);
          common_vendor.index.showToast({
            title: result.message || "登录失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("登录异常:", error);
        common_vendor.index.showToast({
          title: "网络错误，请检查后端服务: " + error.message,
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    /**
     * 显示注册弹窗
     */
    handleRegister() {
      this.showRegisterModal = true;
    },
    /**
     * 关闭注册弹窗
     */
    closeRegisterPopup() {
      this.showRegisterModal = false;
      this.registerForm = {
        username: "",
        password: "",
        nickname: ""
      };
    },
    /**
     * 提交注册
     */
    async submitRegister() {
      if (!this.registerForm.username || !this.registerForm.password) {
        common_vendor.index.showToast({
          title: "请填写完整信息",
          icon: "none"
        });
        return;
      }
      try {
        const result = await utils_api.register(this.registerForm);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "注册成功",
            icon: "success"
          });
          this.closeRegisterPopup();
          this.loginForm.username = this.registerForm.username;
          this.loginForm.password = this.registerForm.password;
        } else {
          common_vendor.index.showToast({
            title: result.message,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "注册失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loginForm.username,
    b: common_vendor.o(($event) => $data.loginForm.username = $event.detail.value),
    c: $data.loginForm.password,
    d: common_vendor.o(($event) => $data.loginForm.password = $event.detail.value),
    e: $data.loading
  }, $data.loading ? {} : {}, {
    f: common_vendor.t($data.loading ? "登录中..." : "开始AI之旅"),
    g: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    h: $data.loading,
    i: common_vendor.o((...args) => $options.handleRegister && $options.handleRegister(...args)),
    j: $data.showRegisterModal
  }, $data.showRegisterModal ? {
    k: $data.registerForm.username,
    l: common_vendor.o(($event) => $data.registerForm.username = $event.detail.value),
    m: $data.registerForm.password,
    n: common_vendor.o(($event) => $data.registerForm.password = $event.detail.value),
    o: $data.registerForm.nickname,
    p: common_vendor.o(($event) => $data.registerForm.nickname = $event.detail.value),
    q: common_vendor.o((...args) => $options.closeRegisterPopup && $options.closeRegisterPopup(...args)),
    r: common_vendor.o((...args) => $options.submitRegister && $options.submitRegister(...args)),
    s: common_vendor.o(() => {
    }),
    t: common_vendor.o((...args) => $options.closeRegisterPopup && $options.closeRegisterPopup(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-cdfe2409"], ["__file", "E:/wolk/study/uniappIm/src/pages/login/login.vue"]]);
wx.createPage(MiniProgramPage);
