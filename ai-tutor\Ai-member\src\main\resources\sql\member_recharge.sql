-- 会员充值记录表
CREATE TABLE IF NOT EXISTS `member_recharge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `role_name` varchar(50) DEFAULT NULL COMMENT '角色名称（如：普通会员、VIP）',
  `recharge_amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `pay_channel` tinyint(4) NOT NULL COMMENT '支付渠道（1-微信，2-支付宝）',
  `trade_no` varchar(128) NOT NULL COMMENT '支付订单号（微信/支付宝返回）',
  `pay_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付状态（0-未支付，1-已支付，2-支付失败）',
  `pay_time` datetime DEFAULT NULL COMMENT '支付完成时间',
  `member_start_time` datetime DEFAULT NULL COMMENT '会员生效时间',
  `member_end_time` datetime DEFAULT NULL COMMENT '会员失效时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注（如：活动充值、首次充值）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trade_no` (`trade_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_pay_time` (`pay_time`),
  KEY `idx_member_end_time` (`member_end_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员充值记录表';

-- 插入测试数据
INSERT INTO `member_recharge` (`user_id`, `username`, `role_id`, `role_name`, `recharge_amount`, `pay_channel`, `trade_no`, `pay_status`, `pay_time`, `member_start_time`, `member_end_time`, `remark`) VALUES
(1, 'admin', 1, 'VIP会员', 99.00, 1, 'MR1703123456001', 1, '2024-01-15 10:30:00', '2024-01-15 10:30:00', '2024-04-15 10:30:00', '首次充值'),
(2, 'teacher', 2, '普通会员', 29.00, 2, 'MR1703123456002', 1, '2024-01-20 14:20:00', '2024-01-20 14:20:00', '2024-02-20 14:20:00', '月度充值'),
(3, 'student', 2, '普通会员', 29.00, 1, 'MR1703123456003', 0, NULL, '2024-02-01 09:00:00', '2024-03-01 09:00:00', '待支付订单');

-- 创建会员套餐表（可选）
CREATE TABLE IF NOT EXISTS `member_package` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `package_name` varchar(100) NOT NULL COMMENT '套餐名称',
  `package_type` varchar(50) NOT NULL COMMENT '套餐类型（月卡、季卡、年卡）',
  `duration_months` int(11) NOT NULL COMMENT '有效期（月数）',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `current_price` decimal(10,2) NOT NULL COMMENT '现价',
  `discount_rate` decimal(3,2) DEFAULT NULL COMMENT '折扣率',
  `features` text COMMENT '套餐特权（JSON格式）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（0-下架，1-上架）',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员套餐表';

-- 插入套餐数据
INSERT INTO `member_package` (`package_name`, `package_type`, `duration_months`, `original_price`, `current_price`, `discount_rate`, `features`, `status`, `sort_order`) VALUES
('月度会员', '月卡', 1, 39.00, 29.00, 0.74, '{"features":["无限制学习","专属客服","学习报告"]}', 1, 1),
('季度会员', '季卡', 3, 117.00, 79.00, 0.68, '{"features":["无限制学习","专属客服","学习报告","优先技术支持"]}', 1, 2),
('年度会员', '年卡', 12, 468.00, 299.00, 0.64, '{"features":["无限制学习","专属客服","学习报告","优先技术支持","专属学习计划"]}', 1, 3),
('VIP会员', 'VIP', 12, 999.00, 599.00, 0.60, '{"features":["所有功能","一对一指导","定制学习方案","优先新功能体验"]}', 1, 4);
