{"name": "amaui-web-aicall-vue3", "private": true, "version": "2.5.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vant": "^4.8.0", "@vant/touch-emulator": "^1.4.0", "axios": "^1.6.0", "aliyun-auikit-aicall": "2.6.0", "eventemitter3": "^5.0.1", "highlight.js": "^11.11.1", "lottie-web": "^5.12.2", "markdown-it": "^14.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.2.0", "typescript": "~5.2.0", "vite": "^5.0.0", "vue-tsc": "^1.8.25"}}