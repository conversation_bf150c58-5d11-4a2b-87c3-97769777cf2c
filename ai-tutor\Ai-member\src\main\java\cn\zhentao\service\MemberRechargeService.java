package cn.zhentao.service;

import cn.zhentao.dto.RechargeRequest;
import cn.zhentao.dto.RechargeResponse;
import cn.zhentao.entity.MemberRecharge;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会员充值服务接口
 */
public interface MemberRechargeService extends IService<MemberRecharge> {

    /**
     * 创建充值订单
     * @param request 充值请求
     * @return 充值响应（包含支付信息）
     */
    RechargeResponse createRechargeOrder(RechargeRequest request);

    /**
     * 处理支付回调
     * @param tradeNo 交易号
     * @param payChannel 支付渠道
     * @param callbackData 回调数据
     * @return 处理结果
     */
    boolean handlePaymentCallback(String tradeNo, Integer payChannel, Map<String, Object> callbackData);

    /**
     * 查询用户充值记录（分页）
     * @param page 分页参数
     * @param userId 用户ID
     * @param payStatus 支付状态
     * @return 分页结果
     */
    IPage<MemberRecharge> getUserRechargeRecords(Page<MemberRecharge> page, Long userId, Integer payStatus);

    /**
     * 根据交易号查询充值记录
     * @param tradeNo 交易号
     * @return 充值记录
     */
    MemberRecharge getRechargeByTradeNo(String tradeNo);

    /**
     * 检查用户是否为有效会员
     * @param userId 用户ID
     * @return 是否为有效会员
     */
    boolean isValidMember(Long userId);

    /**
     * 获取用户当前会员信息
     * @param userId 用户ID
     * @return 会员信息
     */
    MemberRecharge getCurrentMemberInfo(Long userId);

    /**
     * 获取用户充值总金额
     * @param userId 用户ID
     * @return 总金额
     */
    Double getTotalRechargeAmount(Long userId);

    /**
     * 获取即将到期的会员列表
     * @return 即将到期的会员列表
     */
    List<MemberRecharge> getExpiringMembers();

    /**
     * 延长会员时间
     * @param userId 用户ID
     * @param months 延长月数
     * @return 是否成功
     */
    boolean extendMemberTime(Long userId, Integer months);

    /**
     * 取消充值订单
     * @param rechargeId 充值记录ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelRechargeOrder(Long rechargeId, Long userId);

    /**
     * 获取充值统计数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据
     */
    Map<String, Object> getRechargeStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 生成支付二维码
     * @param rechargeId 充值记录ID
     * @param payChannel 支付渠道
     * @return 二维码内容
     */
    String generatePaymentQrCode(Long rechargeId, Integer payChannel);
}
