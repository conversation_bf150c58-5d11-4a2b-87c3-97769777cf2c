"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const TabBar = () => "../../components/TabBar/TabBar.js";
const _sfc_main = {
  components: {
    TabBar
  },
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: "好友列表", badge: 0, showBadge: false },
        { name: "收到申请", badge: 0, showBadge: true },
        { name: "发出申请", badge: 0, showBadge: false }
      ],
      friendList: [],
      receivedRequests: [],
      sentRequests: [],
      showSearchModalFlag: false,
      searchKeyword: "",
      searchResults: [],
      showRemarkModalFlag: false,
      selectedFriend: {},
      remarkInput: "",
      saving: false,
      friendSearchKeyword: ""
    };
  },
  onLoad() {
    this.loadAllData();
  },
  computed: {
    filteredFriendList() {
      if (!this.friendSearchKeyword.trim()) {
        return this.friendList;
      }
      const keyword = this.friendSearchKeyword.toLowerCase();
      return this.friendList.filter((friend) => {
        const remark = (friend.remark || "").toLowerCase();
        const nickname = (friend.nickname || "").toLowerCase();
        const username = (friend.username || "").toLowerCase();
        return remark.includes(keyword) || nickname.includes(keyword) || username.includes(keyword);
      });
    }
  },
  onShow() {
    this.loadAllData();
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
    },
    async loadAllData() {
      await Promise.all([
        this.loadFriendList(),
        this.loadReceivedRequests(),
        this.loadSentRequests()
      ]);
    },
    async loadFriendList() {
      try {
        const result = await utils_api.getFriendList();
        console.log("好友列表API响应:", result);
        if (result.code === 200) {
          this.friendList = result.data || [];
          console.log("好友列表数据:", this.friendList);
        }
      } catch (error) {
        console.error("加载好友列表失败:", error);
      }
    },
    async loadReceivedRequests() {
      try {
        const result = await utils_api.getReceivedFriendRequests();
        if (result.code === 200) {
          this.receivedRequests = result.data || [];
          this.tabs[1].badge = this.receivedRequests.filter((r) => r.status === "PENDING").length;
        }
      } catch (error) {
        console.error("加载收到的申请失败:", error);
      }
    },
    async loadSentRequests() {
      try {
        const result = await utils_api.getSentFriendRequests();
        if (result.code === 200) {
          this.sentRequests = result.data || [];
          this.tabs[2].badge = this.sentRequests.length;
        }
      } catch (error) {
        console.error("加载发出的申请失败:", error);
      }
    },
    async handleRequest(requestId, action) {
      try {
        const result = await utils_api.handleFriendRequest(requestId, action);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: action === "accept" ? "已同意" : "已拒绝",
            icon: "success"
          });
          await this.loadAllData();
          if (this.receivedRequests.length === 0) {
            this.tabs[1].badge = 0;
          }
        } else {
          common_vendor.index.showToast({
            title: result.message,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },
    deleteFriendConfirm(friend) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: `确定要删除好友 ${friend.nickname || friend.username} 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.deleteFriendAction(friend.userId || friend.id);
          }
        }
      });
    },
    async deleteFriendAction(friendId) {
      try {
        const result = await utils_api.deleteFriend(friendId);
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "删除成功",
            icon: "success"
          });
          this.loadFriendList();
        } else {
          common_vendor.index.showToast({
            title: result.message,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "删除失败",
          icon: "none"
        });
      }
    },
    openChat(friend) {
      const userId = friend.userId || friend.id;
      const displayName = this.getDisplayName(friend);
      common_vendor.index.navigateTo({
        url: `/pages/chatDetail/chatDetail?userId=${userId}&nickname=${encodeURIComponent(displayName)}`
      });
    },
    getDisplayName(friend) {
      if (friend.remark && friend.remark.trim()) {
        return friend.remark;
      }
      return friend.nickname || friend.username || "未知用户";
    },
    showRemarkModal(friend) {
      console.log("显示备注模态框，好友数据:", friend);
      this.selectedFriend = friend;
      this.remarkInput = friend.remark || "";
      this.showRemarkModalFlag = true;
    },
    hideRemarkModal() {
      this.showRemarkModalFlag = false;
      this.selectedFriend = {};
      this.remarkInput = "";
      this.saving = false;
    },
    onRemarkInput(e) {
      this.remarkInput = e.detail.value;
    },
    onInputFocus() {
      console.log("输入框获得焦点");
    },
    onInputBlur() {
      console.log("输入框失去焦点");
    },
    async saveRemark() {
      if (this.saving)
        return;
      this.saving = true;
      try {
        const result = await utils_api.setFriendRemark(this.selectedFriend.userId, this.remarkInput.trim());
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "备注设置成功",
            icon: "success"
          });
          this.hideRemarkModal();
          this.loadFriendList();
        } else {
          common_vendor.index.showToast({
            title: result.message || "设置失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("设置备注失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.saving = false;
      }
    },
    onFriendSearch() {
    },
    clearFriendSearch() {
      this.friendSearchKeyword = "";
    },
    showSearchModal() {
      this.showSearchModalFlag = true;
    },
    hideSearchModal() {
      this.showSearchModalFlag = false;
      this.searchKeyword = "";
      this.searchResults = [];
    },
    onSearchInput() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = [];
      }
    },
    async searchUsers() {
      if (!this.searchKeyword.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none"
        });
        return;
      }
      try {
        const result = await utils_api.searchUsers(this.searchKeyword);
        if (result.code === 200) {
          this.searchResults = result.data || [];
        } else {
          common_vendor.index.showToast({
            title: result.message,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "搜索失败",
          icon: "none"
        });
      }
    },
    async sendFriendRequestToUser(user) {
      try {
        const result = await utils_api.sendFriendRequest({
          toUserId: user.userId || user.id,
          message: "请求添加您为好友"
        });
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "申请已发送",
            icon: "success"
          });
          this.hideSearchModal();
        } else {
          common_vendor.index.showToast({
            title: result.message,
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "发送失败",
          icon: "none"
        });
      }
    },
    formatTime(timeStr) {
      const time = new Date(timeStr);
      const now = /* @__PURE__ */ new Date();
      const diff = now - time;
      if (diff < 6e4)
        return "刚刚";
      if (diff < 36e5)
        return Math.floor(diff / 6e4) + "分钟前";
      if (diff < 864e5)
        return Math.floor(diff / 36e5) + "小时前";
      return Math.floor(diff / 864e5) + "天前";
    },
    /**
     * 处理底部导航栏切换
     */
    onTabChange(index) {
      console.log("切换到标签:", index);
    },
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待处理",
        "ACCEPTED": "已同意",
        "REJECTED": "已拒绝"
      };
      return statusMap[status] || status;
    },
    getStatusClass(status) {
      return {
        "status-pending": status === "PENDING",
        "status-accepted": status === "ACCEPTED",
        "status-rejected": status === "REJECTED"
      };
    }
  }
};
if (!Array) {
  const _easycom_TabBar2 = common_vendor.resolveComponent("TabBar");
  _easycom_TabBar2();
}
const _easycom_TabBar = () => "../../components/TabBar/TabBar.js";
if (!Math) {
  _easycom_TabBar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: tab.badge > 0 && tab.showBadge
      }, tab.badge > 0 && tab.showBadge ? {
        c: common_vendor.t(tab.badge)
      } : {}, {
        d: index,
        e: $data.currentTab === index ? 1 : "",
        f: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    b: $data.currentTab === 0
  }, $data.currentTab === 0 ? common_vendor.e({
    c: common_vendor.o([($event) => $data.friendSearchKeyword = $event.detail.value, (...args) => $options.onFriendSearch && $options.onFriendSearch(...args)]),
    d: $data.friendSearchKeyword,
    e: $data.friendSearchKeyword
  }, $data.friendSearchKeyword ? {
    f: common_vendor.o((...args) => $options.clearFriendSearch && $options.clearFriendSearch(...args))
  } : {}, {
    g: common_vendor.f($options.filteredFriendList, (friend, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getDisplayName(friend).charAt(0)),
        b: common_vendor.t($options.getDisplayName(friend)),
        c: common_vendor.t(friend.username),
        d: friend.remark
      }, friend.remark ? {
        e: common_vendor.t(friend.remark)
      } : {}, {
        f: common_vendor.o(($event) => $options.showRemarkModal(friend), friend.userId),
        g: common_vendor.o(($event) => $options.deleteFriendConfirm(friend), friend.userId),
        h: friend.userId,
        i: common_vendor.o(($event) => $options.openChat(friend), friend.userId)
      });
    }),
    h: $options.filteredFriendList.length === 0
  }, $options.filteredFriendList.length === 0 ? {
    i: common_vendor.t($data.friendSearchKeyword ? "未找到相关好友" : "暂无好友"),
    j: common_vendor.t($data.friendSearchKeyword ? "尝试其他关键词" : "点击右上角搜索添加好友")
  } : {}) : {}, {
    k: $data.currentTab === 1
  }, $data.currentTab === 1 ? common_vendor.e({
    l: common_vendor.f($data.receivedRequests, (request, k0, i0) => {
      return {
        a: common_vendor.t((request.fromUser.nickname || request.fromUser.username || "?").charAt(0)),
        b: common_vendor.t(request.fromUser.nickname || request.fromUser.username),
        c: common_vendor.t(request.message || "请求添加您为好友"),
        d: common_vendor.t($options.formatTime(request.createTime)),
        e: common_vendor.o(($event) => $options.handleRequest(request.id, "accept"), request.id),
        f: common_vendor.o(($event) => $options.handleRequest(request.id, "reject"), request.id),
        g: request.id
      };
    }),
    m: $data.receivedRequests.length === 0
  }, $data.receivedRequests.length === 0 ? {} : {}) : {}, {
    n: $data.currentTab === 2
  }, $data.currentTab === 2 ? common_vendor.e({
    o: common_vendor.f($data.sentRequests, (request, k0, i0) => {
      return {
        a: common_vendor.t((request.toUser.nickname || request.toUser.username || "?").charAt(0)),
        b: common_vendor.t(request.toUser.nickname || request.toUser.username),
        c: common_vendor.t(request.message || "请求添加为好友"),
        d: common_vendor.t($options.formatTime(request.createTime)),
        e: common_vendor.t($options.getStatusText(request.status)),
        f: common_vendor.n($options.getStatusClass(request.status)),
        g: request.id
      };
    }),
    p: $data.sentRequests.length === 0
  }, $data.sentRequests.length === 0 ? {} : {}) : {}, {
    q: $data.showSearchModalFlag
  }, $data.showSearchModalFlag ? common_vendor.e({
    r: common_vendor.o((...args) => $options.hideSearchModal && $options.hideSearchModal(...args)),
    s: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    t: $data.searchKeyword,
    v: common_vendor.o((...args) => $options.searchUsers && $options.searchUsers(...args)),
    w: common_vendor.f($data.searchResults, (user, k0, i0) => {
      return {
        a: common_vendor.t((user.nickname || user.username || "?").charAt(0)),
        b: common_vendor.t(user.nickname || user.username),
        c: common_vendor.t(user.username),
        d: common_vendor.o(($event) => $options.sendFriendRequestToUser(user), user.id),
        e: user.id
      };
    }),
    x: $data.searchResults.length === 0 && $data.searchKeyword
  }, $data.searchResults.length === 0 && $data.searchKeyword ? {} : {}, {
    y: common_vendor.o(() => {
    }),
    z: common_vendor.o((...args) => $options.hideSearchModal && $options.hideSearchModal(...args))
  }) : {}, {
    A: $data.showRemarkModalFlag
  }, $data.showRemarkModalFlag ? {
    B: common_vendor.o((...args) => $options.hideRemarkModal && $options.hideRemarkModal(...args)),
    C: common_vendor.t($options.getDisplayName($data.selectedFriend).charAt(0)),
    D: common_vendor.t($options.getDisplayName($data.selectedFriend)),
    E: common_vendor.t($data.selectedFriend.username),
    F: common_vendor.o([($event) => $data.remarkInput = $event.detail.value, (...args) => $options.onRemarkInput && $options.onRemarkInput(...args)]),
    G: common_vendor.o((...args) => $options.onInputFocus && $options.onInputFocus(...args)),
    H: common_vendor.o((...args) => $options.onInputBlur && $options.onInputBlur(...args)),
    I: $data.remarkInput,
    J: common_vendor.o((...args) => $options.hideRemarkModal && $options.hideRemarkModal(...args)),
    K: common_vendor.t($data.saving ? "保存中..." : "保存"),
    L: common_vendor.o((...args) => $options.saveRemark && $options.saveRemark(...args)),
    M: $data.saving,
    N: common_vendor.o(() => {
    }),
    O: common_vendor.o((...args) => $options.hideRemarkModal && $options.hideRemarkModal(...args))
  } : {}, {
    P: common_vendor.o($options.onTabChange),
    Q: common_vendor.p({
      current: 1
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d48b96ff"], ["__file", "E:/wolk/study/uniappIm/src/pages/friend/friend.vue"]]);
wx.createPage(MiniProgramPage);
