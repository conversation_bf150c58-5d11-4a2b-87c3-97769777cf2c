
.container.data-v-893823e0 {
  min-height: 100vh;
  background-color: #ededed;
}

/* 自定义导航栏 */
.custom-navbar.data-v-893823e0 {
  background-color: #fff;
  padding-top: var(--status-bar-height, 44px);
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.navbar-content.data-v-893823e0 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}
.navbar-left.data-v-893823e0 {
  width: 60px;
  display: flex;
  align-items: center;
}
.back-icon.data-v-893823e0 {
  font-size: 20px;
  color: #007aff;
  font-weight: 500;
}
.navbar-title.data-v-893823e0 {
  font-size: 17px;
  font-weight: 600;
  color: #333;
}
.navbar-right.data-v-893823e0 {
  width: 60px;
}

/* 搜索区域 */
.search-section.data-v-893823e0 {
  background-color: #fff;
  padding: 20px 15px;
  margin-bottom: 10px;
}
.search-box.data-v-893823e0 {
  display: flex;
  align-items: center;
  gap: 12px;
}
.search-input.data-v-893823e0 {
  flex: 1;
  height: 44px;
  padding: 0 20px;
  border: 1px solid #e5e5e5;
  border-radius: 22px;
  font-size: 16px;
  background-color: #f8f8f8;
  transition: border-color 0.2s ease;
}
.search-input.data-v-893823e0:focus {
  border-color: #007aff;
  background-color: #fff;
}
.search-btn.data-v-893823e0 {
  height: 44px;
  padding: 0 24px;
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}
.search-btn.data-v-893823e0:active {
  background-color: #06ad56;
}

/* 搜索结果 */
.search-results.data-v-893823e0 {
  background-color: #fff;
  margin: 10px 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.result-title.data-v-893823e0 {
  padding: 20px 15px 10px;
  font-size: 14px;
  font-weight: 500;
  color: #888;
  background-color: #f8f8f8;
}
.user-item.data-v-893823e0 {
  display: flex;
  align-items: center;
  padding: 20px 15px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}
.user-item.data-v-893823e0:last-child {
  border-bottom: none;
}
.user-item.data-v-893823e0:active {
  background-color: #f8f8f8;
}
.user-avatar.data-v-893823e0 {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.avatar-text.data-v-893823e0 {
  color: white;
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
}
.user-info.data-v-893823e0 {
  flex: 1;
}
.user-name.data-v-893823e0 {
  font-size: 17px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
}
.user-username.data-v-893823e0 {
  font-size: 14px;
  color: #888;
}
.add-btn.data-v-893823e0 {
  height: 36px;
  padding: 0 20px;
  background-color: #07c160;
  color: white;
  border: none;
  border-radius: 18px;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
}
.add-btn.data-v-893823e0:active {
  background-color: #06ad56;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.add-btn.data-v-893823e0:disabled {
  background-color: #e5e5e5;
  color: #999;
  box-shadow: none;
  -webkit-transform: none;
          transform: none;
}

/* 空状态 */
.empty-state.data-v-893823e0 {
  text-align: center;
  padding: 50px 20px;
}
.empty-text.data-v-893823e0 {
  color: #666;
  font-size: 14px;
}

/* 加载状态 */
.loading.data-v-893823e0 {
  text-align: center;
  padding: 20px;
  color: #666;
}
