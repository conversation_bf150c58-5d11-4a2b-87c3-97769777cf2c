# AiApp Service 配置
server:
  port: 8082

spring:
  application:
    name: ai-app-service
  profiles:
    active: dev

# 阿里云百炼AI配置
dashscope:
  api-key: sk-0e7519f33026415d9b58ff4679ead7c4
  app-id: b8e521e1b0ee4a9cad5b00495a701f16

# 阿里云语音对话配置
aliyun:
  voice-chat:
    app-id: ${ALIYUN_VOICE_APP_ID:default-app-id}
    workflow-id: ${ALIYUN_VOICE_WORKFLOW_ID:default-workflow-id}
    token: ${ALIYUN_VOICE_TOKEN:default-token}
    region: cn-beijing

# 语音通话RTC配置
voice:
  call:
    app:
      id: ${VOICE_CALL_APP_ID:13d9536a-37db-4d81-b35c-759105f1b8b7}
      key: ${VOICE_CALL_APP_KEY:206cb794480515fa054ea20f02305dd4}

# 日志配置
logging:
  level:
    root: info
    cn.zhentao: debug
    com.alibaba.dashscope: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ai-app-service.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 跨域配置
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:5173
    - http://localhost:8080
    - http://127.0.0.1:3000
    - http://127.0.0.1:5173
    - http://127.0.0.1:8080
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true
  max-age: 3600
