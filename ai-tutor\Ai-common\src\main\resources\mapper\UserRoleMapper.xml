<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.zhentao.mapper.UserRoleMapper">

    <resultMap id="BaseResultMap" type="cn.zhentao.pojo.UserRole">
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="roleId" column="role_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,role_id
    </sql>
</mapper>
