"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "TabBar",
  props: {
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentTab: this.current,
      tabList: [
        {
          icon: "💬",
          text: "聊天",
          pagePath: "/pages/chat/chat"
        },
        {
          icon: "👥",
          text: "好友",
          pagePath: "/pages/friend/friend"
        }
      ]
    };
  },
  watch: {
    current(newVal) {
      this.currentTab = newVal;
    }
  },
  methods: {
    switchTab(index) {
      if (this.currentTab === index)
        return;
      const item = this.tabList[index];
      this.currentTab = index;
      this.$emit("change", index);
      common_vendor.index.switchTab({
        url: item.pagePath,
        fail: () => {
          common_vendor.index.reLaunch({
            url: item.pagePath
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.tabList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.icon),
        b: common_vendor.t(item.text),
        c: index,
        d: $data.currentTab === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a845e0be"], ["__file", "E:/wolk/study/uniappIm/src/components/TabBar/TabBar.vue"]]);
wx.createComponent(Component);
