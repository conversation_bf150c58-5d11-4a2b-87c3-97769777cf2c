<template>
  <div class="recommendation-page">
    <van-nav-bar
      title="智能推荐"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
      class="custom-nav-bar"
    />

    <div class="feature-intro">
      <div class="intro-content">
        <div class="intro-icon">🎯</div>
        <div class="intro-text">
          <h3>AI智能推荐</h3>
          <p>个性化内容推荐，发现你可能喜欢的</p>
        </div>
      </div>
    </div>

    <div class="recommendation-container">
      <div class="input-section">
        <van-field
          v-model="preferences"
          placeholder="告诉我你的兴趣爱好..."
          type="textarea"
          rows="3"
          autosize
        />
        <van-button
          type="primary"
          block
          :loading="isLoading"
          @click="getRecommendations"
          :disabled="!preferences.trim()"
        >
          获取推荐
        </van-button>
      </div>

      <div v-if="recommendations" class="result-section">
        <h4>为你推荐：</h4>
        <div class="recommendations-text">{{ recommendations }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'
import aiService from '@/services/aiService'

const preferences = ref('')
const recommendations = ref('')
const isLoading = ref(false)

const getRecommendations = async () => {
  if (!preferences.value.trim()) return

  isLoading.value = true
  recommendations.value = ''

  let accumulatedText = ''

  try {
    // 使用流式输出
    await aiService.recommendationStream(
      12345,
      preferences.value,
      // onMessage回调
      (content: string) => {
        accumulatedText += content
        recommendations.value = accumulatedText
      },
      // onComplete回调
      () => {
        isLoading.value = false
      },
      // onError回调
      async (error: any) => {
        console.error('流式输出失败，尝试非流式输出:', error)

        // 流式输出失败，尝试非流式输出
        try {
          const response = await aiService.recommendation(12345, preferences.value, '通用')
          if (response.code === 200) {
            recommendations.value = response.data || '推荐生成完成！'
          } else {
            recommendations.value = '推荐失败，请重试。'
            showToast('推荐失败')
          }
        } catch (fallbackError) {
          console.error('非流式输出也失败:', fallbackError)
          recommendations.value = '推荐失败，请重试。'
          showToast('推荐失败')
        }

        isLoading.value = false
      }
    )

  } catch (error) {
    console.error('智能推荐失败:', error)

    // 备选方案：直接调用非流式API
    try {
      const response = await aiService.recommendation(12345, preferences.value, '通用')
      if (response.code === 200) {
        recommendations.value = response.data || '推荐生成完成！'
      } else {
        recommendations.value = '推荐失败，请重试。'
        showToast('推荐失败')
      }
    } catch (fallbackError) {
      console.error('备选方案失败:', fallbackError)
      recommendations.value = '推荐失败，请重试。'
      showToast('推荐失败')
    }

    isLoading.value = false
  }
}
</script>

<style scoped>
.recommendation-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.custom-nav-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.feature-intro {
  margin: 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.intro-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(67, 233, 123, 0.1);
  border-radius: 16px;
}

.intro-text h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.intro-text p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.recommendation-container {
  padding: 16px;
}

.input-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
}

.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.result-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.recommendations-text {
  font-size: 14px;
  line-height: 1.6;
  color: #2c3e50;
}
</style>
